/* local-details.component.css - Fixed version */
.local-details-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Loading styles */
.loading-text { 
  font-size: 20px; 
  font-weight: 500; 
  letter-spacing: 0.5px; 
}

.dots { 
  animation: dotAnimation 1.4s infinite; 
  display: inline-block; 
}

@keyframes dotAnimation { 
  0% { opacity: 0; } 
  50% { opacity: 1; } 
  100% { opacity: 0; } 
}

.breadcrumb-nav {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.back-button {
  background: #f5f5f5;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: #e0e0e0;
  transform: translateX(-3px);
}

.back-button .material-icons {
  color: #555;
  font-size: 24px;
}

.breadcrumb-text {
  font-size: 15px;
  font-weight: 500;
  color: #4a5568;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  background: linear-gradient(135deg, #f8fff8, #f0f9f0);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary);
  border-right: 4px solid var(--primary);
  border-bottom: 4px solid var(--primary);
  border-left: 4px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.content-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.info-section {
  display: flex;
  margin-bottom: 30px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 25px;
  animation: fadeIn 0.5s ease-out;
  transition: all 0.3s ease-out;
}

.site-images-container {
  flex: 0 0 300px;
  margin-right: 30px;
  display: flex;
  flex-direction: column;
}

.logo-container {
  width: 300px;
  height: 280px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.site-logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.architecture-image {
  margin-top: 10px;
  border-radius: 8px;
  overflow: hidden;
}

.architecture-image img {
  width: 100%;
  height: auto;
  object-fit: contain;
  background-color: #f5f5f5;
}

.architecture-image img[src$="default-image.jpg"] {
  max-height: 200px;
  padding: 20px;
  background: linear-gradient(135deg, #f0f8f0, #e8f5e8);
}

.no-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f0f8f0, #e8f5e8);
}

.no-logo .material-icons {
  font-size: 80px;
  color: #81c784;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.no-logo:hover .material-icons {
  opacity: 0.8;
  transform: scale(1.1);
}

/* --- Modernized Stats Section --- */
.site-stats {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px 0 rgba(76, 175, 80, 0.07), 0 1.5px 6px 0 rgba(0,0,0,0.04);
  padding: 0;
  margin-top: 0;
  overflow: hidden;
  margin-bottom: 10px;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 18px 0 12px 0;
  background: transparent;
  transition: background 0.2s;
  border-right: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-right: none;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #388e3c;
  margin-bottom: 2px;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(76,175,80,0.08);
}

.stat-label {
  font-size: 0.85rem;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
  margin-top: 2px;
  letter-spacing: 0.7px;
}

/* Add a subtle hover effect for interactivity */
.stat-item:hover {
  background: #f5fef7;
}

/* Responsive tweaks */
@media (max-width: 768px) {
  .site-stats {
    flex-direction: column;
    box-shadow: 0 1px 6px 0 rgba(76, 175, 80, 0.07), 0 1px 3px 0 rgba(0,0,0,0.04);
  }
  .stat-item {
    border-right: none;
    border-bottom: 1px solid #f0f0f0;
    padding: 14px 0 10px 0;
  }
  .stat-item:last-child {
    border-bottom: none;
  }
}

.site-info-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.site-header {
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #e2e8f0;
}

.site-name {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: var(--primary);
  line-height: 1.2;
}

.site-type {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  background-color: #e8f5e9;
  color: #2e7d32;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  flex: 1;
  margin-top: 24px;
}

.info-column {
  display: flex;
  flex-direction: column;
  gap: 24px; /* Add consistent gap between items */
}

.info-item {
  padding: 0; /* Remove padding */
  transition: all 0.3s ease;
  border: none; /* Remove any borders */
  background: transparent;
}

/* Remove border-bottom and margin-bottom styles completely */
.info-item:hover {
  background: rgba(232, 245, 233, 0.3);
  border-radius: 8px;
}

.info-label {
  display: flex;
  align-items: center;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 8px;
  font-size: 14px;
  gap: 8px;
}

.info-label mat-icon:first-child {
  color: var(--primary);
  font-size: 18px;
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.info-label mat-icon,
.info-label .material-icons {
  margin-right: 8px;
  color: var(--primary);
  font-size: 20px;
}

.info-value {
  color: #1e293b;
  font-size: 14px;
  line-height: 1.6;
  font-weight: 400;
  padding-left: 28px;
  margin-top: -4px;
}

.info-value strong {
  color: #64748b;
  font-weight: 500;
}

.description-text {
  white-space: pre-line;
}

/* Beautiful Sensor Info Button - Modern Glass Design */
.sensor-info-btn {
  margin-left: 10px;
  width: 28px;
  height: 28px;
  min-width: 28px;
  min-height: 28px;
  backdrop-filter: blur(10px);
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  cursor: pointer;
  vertical-align: middle;
  position: relative;
  overflow: hidden;
}

.sensor-info-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12px;
}

.sensor-info-btn:hover {
  transform: translateY(-2px) scale(1.02);
}

.sensor-info-btn:hover::before {
  opacity: 1;
}

.sensor-info-btn mat-icon {
  color: #64748b;
  font-size: 16px;
  width: 16px;
  height: 16px;
  line-height: 16px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  position: relative;
}

.sensor-info-btn:hover mat-icon {
  color: var(--primary);
  transform: rotate(15deg) scale(1.1);
}

.sensor-info-btn.active mat-icon {
  color: var(--primary);
  transform: rotate(15deg);
}

.sensor-info-btn:focus {
  outline: none;
  box-shadow:
    0 0 0 3px rgba(76, 175, 80, 0.3),
    0 4px 15px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

.sensor-info-btn:active {
  transform: translateY(0) scale(0.98);
  transition: all 0.1s ease;
}

/* Sensor Popup Overlay */
.sensor-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeInOverlay 0.3s ease-out;
}

.sensor-popup-container {
  background: white;
  border-radius: 16px;
  border: 2px solid #000000;
  max-width: 800px;
  width: 95%;
  max-height: 85vh;
  overflow: hidden;
  animation: slideInUp 0.3s ease-out;
}

.sensor-popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #f8fffe, #f0f9ff);
}

.popup-title {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
}

.popup-title mat-icon {
  color: var(--primary);
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.close-popup-btn {
  width: 40px;
  height: 40px;
  background: #f1f5f9;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-popup-btn:hover {
  background: #e2e8f0;
  transform: scale(1.05);
}

.close-popup-btn mat-icon {
  color: #64748b;
  font-size: 20px;
}

.sensor-popup-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.sensor-popup-body::-webkit-scrollbar {
  width: 6px;
}

.sensor-popup-body::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.sensor-popup-body::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.sensor-popup-body::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Enhanced Sensor Card Styling */
.sensor-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid #e2e8f0;
  margin-bottom: 16px;
}

.sensor-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.15);
  border-color: var(--primary);
}

.sensor-card.expanded {
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.15);
  border: 1px solid rgba(76, 175, 80, 0.2);
  transform: translateY(-2px);
}

.sensor-card.expanded .sensor-main {
  background: linear-gradient(135deg, #f8fffe, #f0f9ff);
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
}

/* Enhanced sensor main section */
.sensor-main {
  display: flex;
  align-items: flex-start;
  padding: 20px;
  gap: 20px;
  min-height: 80px;
}

/* Enhanced sensor icon */
.sensor-icon {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background: linear-gradient(135deg, var(--primary), #45a049);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.sensor-icon mat-icon {
  color: white;
  font-size: 28px;
  width: 28px;
  height: 28px;
}

/* Enhanced sensor info section */
.sensor-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-top: 4px;
}

/* Enhanced sensor name */
.sensor-name {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  line-height: 1.3;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

/* Enhanced sensor meta information */
.sensor-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 4px;
}

.sensor-info-row {
  display: flex;
  gap: 8px;
  margin-top: 4px;
  font-size: 13px;
}

.sensor-info-label {
  color: #64748b;
  font-weight: 500;
}

.sensor-info-value {
  color: #1e293b;
}

/* Enhanced sensor type badge */
.sensor-type {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  color: #1565c0;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-block;
  width: fit-content;
  border: 1px solid #2196f3;
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.2);
}

/* Enhanced controller info */
.sensor-controller {
  color: var(--primary);
  font-weight: 500;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.sensor-controller::before {
  content: "🔌";
  font-size: 12px;
}

/* Enhanced arrow icon */
.sensor-arrow {
  color: #94a3b8;
  transition: all 0.3s ease;
  align-self: flex-start;
  margin-top: 8px;
}

.sensor-card:hover .sensor-arrow {
  color: var(--primary);
}

.sensor-card.active .sensor-arrow {
  transform: rotate(180deg);
  color: var(--primary);
}

/* Enhanced sensor details section */
.sensor-details {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-top: 1px solid #e2e8f0;
  animation: expandDetails 0.3s ease-out;
  overflow: hidden;
}

@keyframes expandDetails {
  from {
    opacity: 0;
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
  to {
    opacity: 1;
    max-height: 300px;
    padding-top: 16px;
    padding-bottom: 16px;
  }
}

/* Enhanced detail rows */
.detail-row {
  display: flex;
  align-items: flex-start;
  padding: 12px 20px;
  font-size: 14px;
  transition: background-color 0.2s ease;
  position: relative;
  cursor: pointer;
}

.detail-row:hover {
  background-color: rgba(76, 175, 80, 0.05);
  transform: translateX(4px);
}

.detail-row:not(:last-child) {
  border-bottom: 1px solid #e2e8f0;
}

.detail-row::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(to bottom, var(--primary), #81C784);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.detail-row:hover::before {
  opacity: 1;
}

.detail-row:active {
  transform: translateX(2px);
}

/* Enhanced detail labels */
.detail-label {
  flex: 0 0 140px;
  color: #64748b;
  font-weight: 600;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding-right: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.detail-label::after {
  content: '';
  width: 4px;
  height: 4px;
  background: var(--primary);
  border-radius: 50%;
  opacity: 0.6;
}

.detail-label[data-type="type"]::before {
  margin-right: 4px;
}

.detail-label[data-type="controller"]::before {
  content: '🎛️';
  margin-right: 4px;
}

.detail-label[data-type="activity"]::before {
  content: '⏰';
  margin-right: 4px;
}

/* Enhanced detail values */
.detail-value {
  flex: 1;
  color: #1e293b;
  font-weight: 500;
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-variant-numeric: tabular-nums;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.4);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.detail-value:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: var(--primary);
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.15);
}

.detail-value.active {
  color: #16a34a;
  font-weight: 600;
  background: linear-gradient(135deg, #e8f5e9, #f1f8e9);
  color: #2e7d32;
  border-color: var(--primary);
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
}

.detail-value.inactive {
  color: #dc2626;
  font-weight: 600;
  background: linear-gradient(135deg, #ffebee, #fce4ec);
  color: #c62828;
  border-color: #f44336;
  box-shadow: 0 2px 8px rgba(244, 67, 54, 0.2);
}

.detail-row[data-type="type"] .detail-value {
  background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
  color: #1565c0;
  border-color: #2196f3;
  font-weight: 600;
}

.detail-row[data-type="controller"] .detail-value {
  background: linear-gradient(135deg, #fff3e0, #fce4ec);
  color: #e65100;
  border-color: #ff9800;
  font-weight: 600;
}

.detail-row[data-type="activity"] .detail-value {
  background: linear-gradient(135deg, #f3e5f5, #e8eaf6);
  color: #4a148c;
  border-color: #9c27b0;
  font-weight: 500;
}

.detail-value.status {
  position: relative;
  padding-left: 24px;
}

.detail-value.status::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--primary);
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.3);
  animation: pulse 2s infinite;
}

.detail-value.status.inactive::before {
  background: #f44336;
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.3);
}

.detail-value.loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Enhanced controller styles */
.controller-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid #e2e8f0;
  margin-bottom: 16px;
}

.controller-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(73, 80, 87, 0.15);
  border-color: #6c757d;
}

.controller-main {
  display: flex;
  align-items: flex-start;
  padding: 20px;
  gap: 20px;
  min-height: 80px;
}

.controller-icon {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background: linear-gradient(135deg, #495057, #6c757d);
  box-shadow: 0 4px 12px rgba(73, 80, 87, 0.3);
}

.controller-icon mat-icon {
  color: white;
  font-size: 28px;
  width: 28px;
  height: 28px;
}

.controller-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-top: 4px;
}

.controller-name {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  line-height: 1.3;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

.controller-id {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-block;
  width: fit-content;
}

.controller-details {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-top: 1px solid #dee2e6;
  animation: expandDetails 0.3s ease-out;
}

.controller-arrow {
  color: #adb5bd;
  transition: transform 0.3s ease;
}

.controller-card.active .controller-arrow {
  transform: rotate(180deg);
}

/* Enhanced section containers */
.section-container {
  margin-bottom: 32px;
  background: #f8fafb;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

/* Enhanced section headers */
.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  color: var(--primary);
  padding-bottom: 12px;
  border-bottom: 2px solid #e2e8f0;
}

.section-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
}

.section-header mat-icon {
  font-size: 28px;
  width: 28px;
  height: 28px;
  color: var(--primary);
}

/* Enhanced sensor count badge */
.sensor-count-badge {
  display: flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, #e8f5e9, #f1f8e9);
  padding: 16px 20px;
  border-radius: 12px;
  margin-bottom: 24px;
  border: 1px solid #c8e6c9;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.sensor-count-badge mat-icon {
  color: var(--primary);
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.sensor-count-badge span {
  color: #2e7d32;
  font-weight: 600;
  font-size: 16px;
}

.no-sensors-message {
  text-align: center;
  padding: 40px 20px;
  color: #64748b;
}

.no-sensors-message mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #94a3b8;
  margin-bottom: 16px;
}

.no-sensors-message h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #475569;
}

.no-sensors-message p {
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.site-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  color: #64748b;
  font-size: 0.9rem;
}

.site-info mat-icon,
.site-info .material-icons {
  color: var(--primary);
  font-size: 20px;
  width: 20px;
  height: 20px;
}

/* Architecture Plan Section Styles */
.architecture-plan-section {
  margin-top: 40px;
  padding: 25px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.architecture-plan-section:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border-color: #c8e6c9;
}

.plan-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 2rem !important;
  padding-bottom: 1rem !important;
  border-bottom: 1px solid #e2e8f0 !important;
}

.plan-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 10px;
}

.plan-header h3::before {
  content: "";
  display: block;
  width: 24px;
  height: 24px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234CAF50'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14z'/%3E%3Cpath d='M7 12h2v5H7zm4-7h2v12h-2zm4 4h2v8h-2z'/%3E%3C/svg%3E") no-repeat center;
}

.canvas-container {
  position: relative;
  width: 100%;
  height: 400px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

#canvas {
  width: 100%;
  height: 100%;
  background: #f9f9f9;
}

/* Action Section Styles */
.action-section {
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
}

.add-rule-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: linear-gradient(135deg, var(--primary), #45a049);
  color: white;
  border: none;
  border-radius: 30px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 6px rgba(76, 175, 80, 0.2);
}

.add-rule-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 7px 14px rgba(76, 175, 80, 0.3);
}

.add-rule-btn:active {
  transform: translateY(0);
  box-shadow: 0 4px 6px rgba(76, 175, 80, 0.2);
}

.add-rule-btn:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.add-rule-btn mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInOverlay {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(76, 175, 80, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .info-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .info-section {
    flex-direction: column;
    padding: 25px;
  }

  .site-images-container {
    margin-right: 0;
    margin-bottom: 20px;
    align-self: center;
  }

  .logo-container {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
  }

  .site-stats {
    max-width: 400px;
    width: 100%;
  }

  .site-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .site-type {
    margin-left: 0;
  }
}

@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
    gap: 20px; /* Adjust gap for mobile */
  }

  .local-details-container {
    padding: 12px;
  }

  .info-section {
    padding: 20px;
  }

  .site-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .site-type {
    margin-top: 10px;
    margin-left: 0;
  }

  .site-name {
    font-size: 24px;
  }

  .logo-container {
    height: 200px;
  }

  .site-images-container {
    flex: none;
  }

  .stat-value {
    font-size: 24px;
  }

  .info-column {
    gap: 16px; /* Smaller gap on mobile */
  }
}

@media (max-width: 480px) {
  .site-name {
    font-size: 20px;
  }
  
  .sensor-popup-body {
    padding: 20px;
  }

  .logo-container {
    height: 180px;
  }

  .stat-value {
    font-size: 20px;
  }

  .stat-label {
    font-size: 10px;
  }

  .info-column {
    gap: 12px; /* Even smaller gap on very small screens */
  }
}

/* New Tab Styles */
.content-tabs-section {
  margin-top: 20px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* Tab styling overrides */
::ng-deep .local-tabs {
  padding: 15px 20px 0 20px;
}

::ng-deep .local-tabs .mat-mdc-tab-label {
  min-width: 120px !important;
  height: 56px !important;
  padding: 0 16px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  color: #616161 !important;
  opacity: 1 !important;
  transition: color 0.3s, background-color 0.3s;
}

::ng-deep .local-tabs .mat-mdc-tab-label.mdc-tab--active {
  color: var(--primary) !important;
  background-color: #f0fdf4 !important;
}

::ng-deep .local-tabs .mat-mdc-tab-label:hover {
  background-color: #f5f5f5 !important;
}

::ng-deep .local-tabs .mat-mdc-tab-label.mdc-tab--active .tab-icon {
  color: var(--primary);
}

.tab-icon {
  margin-right: 8px;
  font-size: 20px;
  transition: color 0.3s;
}

.tab-label {
  font-size: 16px;
}

::ng-deep .local-tabs .mat-mdc-tab-header {
  border-bottom: 1px solid #e0e0e0;
}

::ng-deep .local-tabs .mat-mdc-tab-group.mat-primary .mat-mdc-tab-header .mat-mdc-tab-label-container .mat-mdc-tab-list .mat-mdc-tab .mdc-tab__text-label {
  color: inherit;
}

::ng-deep .local-tabs .mdc-tab-indicator__content--underline {
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
}

.tab-content {
  padding: 20px;
}

/* Architecture Plan Section */
.architecture-plan-section {
  background: transparent;
  border-radius: 8px;
  margin: 0;
  padding: 0;
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.plan-title-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
}

.plan-title-section h3 {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.plan-title-section .section-icon {
  margin-right: 10px;
  font-size: 28px;
  color: var(--primary);
}

.plan-description {
  color: #757575;
  font-size: 15px;
  margin: 0;
}

.canvas-container {
  width: 100%;
  height: 500px;
  overflow: hidden;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  background: #fafafa;
}

canvas {
  width: 100%;
  height: 100%;
}

/* Applied Rules Section */
.applied-rules-section {
  background: transparent;
  border-radius: 8px;
  padding: 0;
}

.rules-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.rules-header h3 {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
}

.rules-header .section-icon {
  margin-right: 10px;
  font-size: 28px;
  color: var(--primary);
}

.rules-description {
  color: #757575;
  font-size: 15px;
  margin-top: 5px;
}

/* Responsive adjustments for tabs */
@media (max-width: 768px) {
  ::ng-deep .local-tabs .mat-mdc-tab-label {
    min-width: unset !important;
    padding: 0 10px !important;
    font-size: 14px !important;
  }
  
  .tab-icon {
    margin-right: 4px;
    font-size: 18px;
  }
  
  .tab-label {
    font-size: 14px;
  }

  .tab-content {
    padding: 15px;
  }
  
  .plan-title-section h3,
  .rules-header h3 {
    font-size: 18px;
  }
  
  .plan-title-section .section-icon,
  .rules-header .section-icon {
    font-size: 24px;
  }

  .canvas-container {
    height: 400px;
  }
}

@media (max-width: 480px) {
  ::ng-deep .local-tabs .mat-mdc-tab-label {
    font-size: 12px !important;
    height: 48px !important;
    padding: 0 8px !important;
  }
  
  .tab-icon {
    font-size: 16px;
    margin-right: 2px;
  }
  
  .tab-label {
    font-size: 12px;
  }

  .tab-content {
    padding: 10px;
  }
  
  .plan-title-section h3,
  .rules-header h3 {
    font-size: 16px;
  }
  
  .plan-title-section .section-icon,
  .rules-header .section-icon {
    font-size: 22px;
  }

  .canvas-container {
    height: 350px;
  }
}

/* Material Icons Consistency */
.material-icons, mat-icon {
  vertical-align: middle;
}