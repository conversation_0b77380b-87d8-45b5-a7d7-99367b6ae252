import { AuditModel } from "@app/core/models/models-audit/audit-model";

export class ClientLicenceControllerView extends AuditModel {
  // Client
  ClientId: string | null = null;
  ClientName: string | null = null;

  // Licence
  LicenceId: string | null = null;
  LicenceName: string | null = null;

  // Controller Serveur
  ControllerServeurId: string | null = null;
  ControllerServeurName: string | null = null;

  // Controller
  ControllerId: string | null = null;
  ControllerName: string | null = null;
  ControllerModel: string | null = null;
  ControllerSerialNumber: string | null = null;
  ControllerMacAddress: string | null = null;
  ControllerIpAddress: string | null = null;
  ControllerLastConnection: string | null = null; // or Date | null
  ControllerState: string | null = null;
  ControllerBaseTopic: string | null = null;
  ControllerInstallationDate: string | null = null; // or Date | null
}


