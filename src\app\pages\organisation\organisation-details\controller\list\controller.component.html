<div class="controller-section">
  <div class="section-header">
    <h3 class="section-title">Contrôleurs</h3>
    <button
      class="create-button"
      (click)="addNewController()"
      *ngIf="!showCreateForm && !showDetailsModal"
    >
      <i class="material-icons">add</i> Ajouter un contrôleur
    </button>
  </div>
  <div class="search-container">
    <input
      type="text"
      [(ngModel)]="searchParam"
      placeholder="Rechercher par nom..."
      class="search-input"
      (keyup.enter)="loadControllers()"
    />
    <button class="search-button" (click)="loadControllers()">
      <i class="material-icons">search</i>
    </button>
    <!-- <button class="clear-button" (click)="clearSearch()" *ngIf="searchParam">
      <i class="material-icons">clear</i>
    </button> -->
  </div>
  <!-- Loading state -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="spinner"></div>
    <p>Chargement des contrôleurs...</p>
  </div>

  <div class="modal-overlay" *ngIf="showCreateForm">
    <div class="modal-container">
      <div class="modal-header">
        <h3>
          <mat-icon>{{ isEditMode ? "edit" : "add" }}</mat-icon>
          {{ isEditMode ? "Modifier le Contrôleur" : "Ajouter un Contrôleur" }}
        </h3>
        <button class="close-button" (click)="onFormClosed()">
          <i class="material-icons">close</i>
        </button>
      </div>

      <div class="modal-content">
        <app-form
          [controller]="selectedControllerEdit"
          [isEditMode]="isEditMode"
          (formClosed)="onFormClosed()"
          (controllerCreated)="onControllerCreated($event)"
          (controllerUpdated)="onControllerUpdated($event)"
          [clientId]="clientId"
        >
        </app-form>
        <!-- <app-form
          [controller]="selectedController"
          [isEditMode]="isEditMode"
          (formClosed)="onFormClosed()"
        > 
        </app-form>-->
      </div>
    </div>
  </div>

  <!-- Details Modal -->
  <div class="modal-overlay" *ngIf="showDetailsModal">
    <div class="modal-container details-modal">
      <div class="modal-header">
        <h3>Détails du Contrôleur</h3>
        <button class="close-button" (click)="onDetailsClosed()">
          <i class="material-icons">close</i>
        </button>
      </div>

      <div class="modal-content">
        <app-details
          [controller]="selectedController"
          (detailsClosed)="onDetailsClosed()"
        >
        </app-details>
      </div>
    </div>
  </div>

  <!-- Controllers content -->
  <div class="controllers-content">
    <!-- Table View -->
    <div class="table-container" *ngIf="controllers.length > 0">
      <app-generic-table
        [data]="allControllersData"
        [headers]="controllerHeaders"
        [keys]="controllerKeys"
        [actions]="['view', 'edit', 'delete']"
        (actionTriggered)="handleTableAction($event)"
      >
      </app-generic-table>

      <!-- Pagination -->
      <div class="pagination-container">
        <mat-paginator
          [length]="totalCount"
          [pageSize]="pageSize"
          [pageIndex]="currentPage"
          [pageSizeOptions]="[5, 10, 25, 50]"
          (page)="onPageChange($event)"
          aria-label="Select page"
        >
        </mat-paginator>
      </div>
    </div>

    <!-- No controllers message -->
    <div class="no-sites-message" *ngIf="controllers.length === 0">
      <i class="material-icons">device_hub</i>
      <p>Aucun contrôleur trouvé pour cette organisation</p>
    </div>
  </div>
</div>