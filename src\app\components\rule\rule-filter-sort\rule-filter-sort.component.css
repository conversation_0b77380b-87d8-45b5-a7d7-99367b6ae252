/* rule-filter-sort.component.css */

/* === SORTING AND FILTERING CONTROLS === */

.sorting-filtering-controls {
  background: var(--white);
  border-radius: 8px;
  border: 1px solid var(--card-border);
  margin: 16px 0 24px 0;
  box-shadow: var(--card-shadow);
  overflow: hidden;
}

/* === QUICK SORT SECTION === */

.quick-sort-section {
  padding: 20px 24px;
  background: var(--background);
  border-bottom: 1px solid var(--card-border);
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.sort-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
  white-space: nowrap;
}

.sort-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  flex: 1;
}

.sort-button {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
  background: var(--white) !important;
  color: var(--text-secondary) !important;
  border: 1px solid var(--card-border) !important;
  min-height: 36px !important;
}

.sort-button:hover:not(:disabled) {
  background: var(--green-light) !important;
  border-color: var(--green-main) !important;
  color: var(--green-dark) !important;
}

.sort-button.active {
  background: var(--green-main) !important;
  color: var(--white) !important;
  border-color: var(--green-main) !important;
}

.sort-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

/* Filter count badge */
.filter-count-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: var(--danger);
  color: var(--white);
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Advanced filters toggle button */
button[matTooltip="Filtres avancés"] {
  position: relative !important;
  background: var(--white) !important;
  border: 1px solid var(--card-border) !important;
  color: var(--text-secondary) !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

button[matTooltip="Filtres avancés"]:hover:not(:disabled) {
  background: var(--green-light) !important;
  border-color: var(--green-main) !important;
  color: var(--green-main) !important;
}

button[matTooltip="Filtres avancés"].active {
  background: var(--green-main) !important;
  color: var(--white) !important;
  border-color: var(--green-main) !important;
}

/* Reset button */
.reset-button {
  background: var(--white) !important;
  border: 1px solid var(--card-border) !important;
  color: var(--text-secondary) !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

.reset-button:hover:not(:disabled) {
  background: var(--beige-darl) !important;
  border-color: var(--text-secondary) !important;
  color: var(--text-primary) !important;
}

/* === APPLIED FILTERS DISPLAY === */

.applied-filters {
  padding: 16px 24px;
  background: var(--green-light);
  border-bottom: 1px solid var(--green-main);
}

.filters-label {
  font-weight: 600;
  color: var(--green-dark);
  font-size: 13px;
  margin-bottom: 12px;
}

.filter-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.filter-chip {
  display: flex;
  align-items: center;
  gap: 6px;
  background: var(--white);
  color: var(--green-dark);
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid var(--green-main);
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-chip:hover:not(.disabled) {
  background: var(--background);
}

.remove-filter {
  font-size: 14px !important;
  width: 14px !important;
  height: 14px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  border-radius: 50% !important;
}

.remove-filter:hover:not(.disabled) {
  background: var(--danger) !important;
  color: var(--white) !important;
}

.remove-filter.disabled {
  opacity: 0.5;
  cursor: not-allowed !important;
}

.clear-filters-button {
  background: var(--white) !important;
  color: var(--danger) !important;
  border: 1px solid var(--danger) !important;
  padding: 6px 12px !important;
  border-radius: 6px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
}

.clear-filters-button:hover:not(:disabled) {
  background: var(--danger) !important;
  color: var(--white) !important;
}

/* === ADVANCED FILTERS PANEL === */

.advanced-filters-panel {
  background: var(--white);
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: var(--green-main);
  color: var(--white);
  border-bottom: 1px solid var(--card-border);
}

.filters-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filters-header button {
  background: transparent !important;
  color: var(--white) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  width: 32px !important;
  height: 32px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

.filters-header button:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

/* === QUICK FILTERS SECTION === */

.quick-filters-section {
  padding: 20px 24px;
  background: var(--background);
  border-bottom: 1px solid var(--card-border);
}

.quick-filters-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
  margin-bottom: 12px;
}

.quick-filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.quick-filter-btn {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  background: var(--white) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--card-border) !important;
  transition: all 0.2s ease !important;
}

.quick-filter-btn:hover:not(:disabled) {
  background: var(--green-light) !important;
  border-color: var(--green-main) !important;
  color: var(--green-dark) !important;
}

.quick-filter-btn mat-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

/* === FILTERS GRID === */

.filters-grid {
  padding: 24px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  background: var(--background);
}

.filter-group {
  background: var(--white);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid var(--card-border);
  box-shadow: var(--card-shadow);
  transition: all 0.2s ease;
  position: relative;
}

.filter-group:hover {
  border-color: var(--green-main);
}

.filter-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
  margin-bottom: 12px;
  display: block;
}

/* === FILTER INPUT GROUPS === */

.filter-input-group {
  display: flex;
  gap: 8px;
  align-items: stretch;
  margin-bottom: 8px;
}

.operator-select,
.filter-select {
  padding: 8px 12px;
  border: 1px solid var(--card-border);
  border-radius: 6px;
  font-size: 13px;
  background: var(--white);
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.operator-select {
  min-width: 120px;
  flex-shrink: 0;
}

.filter-select.full-width {
  width: 100%;
}

.operator-select:focus,
.filter-select:focus {
  border-color: var(--green-main);
  outline: none;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.filter-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--card-border);
  border-radius: 6px;
  font-size: 13px;
  background: var(--white);
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.filter-input:focus {
  border-color: var(--green-main);
  outline: none;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.filter-input:disabled,
.operator-select:disabled,
.filter-select:disabled {
  background: var(--background);
  color: var(--text-secondary);
  cursor: not-allowed;
  opacity: 0.7;
}

/* === CLEAR FILTER BUTTON === */

.clear-filter-button {
  background: var(--white) !important;
  color: var(--danger) !important;
  border: 1px solid var(--card-border) !important;
  width: 32px !important;
  height: 32px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  align-self: flex-start !important;
  margin-top: 4px !important;
}

.clear-filter-button:hover:not(:disabled) {
  background: var(--danger) !important;
  color: var(--white) !important;
  border-color: var(--danger) !important;
}

/* === FILTERS ACTIONS === */

.filters-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: var(--background);
  border-top: 1px solid var(--card-border);
  gap: 16px;
}

.clear-all-button {
  background: var(--white) !important;
  color: var(--text-secondary) !important;
  border: 1px solid var(--card-border) !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
}

.clear-all-button:hover:not(:disabled) {
  background: var(--beige-darl) !important;
  color: var(--text-primary) !important;
}

.filters-actions button[color="primary"] {
  background: var(--green-main) !important;
  color: var(--white) !important;
  border: 1px solid var(--green-main) !important;
  padding: 8px 20px !important;
  border-radius: 6px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
}

.filters-actions button[color="primary"]:hover:not(:disabled) {
  background: var(--primary-dark) !important;
}

/* === RESPONSIVE DESIGN FOR FILTERS === */

@media (max-width: 768px) {
  .sorting-filtering-controls {
    margin: 12px 0 20px 0;
  }

  .quick-sort-section {
    padding: 16px 20px;
  }

  .sort-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .sort-options {
    flex-direction: column;
    gap: 6px;
  }

  .sort-button {
    justify-content: center !important;
  }

  .applied-filters {
    padding: 12px 20px;
  }

  .filter-chips {
    gap: 6px;
  }

  .quick-filters-section {
    padding: 16px 20px;
  }

  .quick-filter-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .quick-filter-btn {
    justify-content: center !important;
  }

  .filters-grid {
    padding: 20px;
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .filter-group {
    padding: 16px;
  }

  .filter-input-group {
    flex-direction: column;
    gap: 8px;
  }

  .operator-select {
    min-width: unset;
  }

  .filters-actions {
    padding: 16px 20px;
    flex-direction: column-reverse;
    gap: 12px;
  }

  .filters-actions button {
    width: 100% !important;
    justify-content: center !important;
  }
}

@media (max-width: 480px) {
  .sort-controls {
    text-align: center;
  }

  .sort-label {
    align-self: center;
    margin-bottom: 8px;
  }

  .filter-chips {
    justify-content: center;
  }

  .quick-filter-buttons {
    align-items: stretch;
  }

  .filters-header {
    padding: 16px 20px;
  }

  .filters-header h4 {
    font-size: 14px;
  }
}

/* === ACCESSIBILITY === */

@media (prefers-reduced-motion: reduce) {
  .filter-chip,
  .sort-button,
  .quick-filter-btn,
  .filter-group {
    transition: none !important;
  }
}

.sort-button:focus-visible,
.quick-filter-btn:focus-visible,
.filter-chip:focus-visible {
  outline: 2px solid var(--green-main);
  outline-offset: 2px;
}

.filter-input:focus-visible,
.operator-select:focus-visible,
.filter-select:focus-visible {
  outline: 2px solid var(--green-main);
  outline-offset: 1px;
}