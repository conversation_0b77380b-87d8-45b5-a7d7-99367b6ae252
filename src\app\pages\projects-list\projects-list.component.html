<!-- Frontend: src/app/pages/projects-list/projects-list.component.html -->
<div class="container">
  <div class="header">
    <h1 class="page-title">
      <mat-icon>folder</mat-icon>
      Projects Dashboard
    </h1>
    <div class="actions">
      <button
        mat-icon-button
        matTooltip="Toggle View"
        (click)="toggleViewMode()"
        class="view-toggle"
      >
        <mat-icon>{{ viewMode === 'cards' ? 'list' : 'view_module' }}</mat-icon>
      </button>
      <button mat-raised-button
        *ngIf="isAdminOrSuperAdmin"
        mat-raised-button
        color="primary"
        class="btn-add"
        (click)="navigateToAddProject()"
      >
        <mat-icon>add</mat-icon>
        Créer un projet
      </button>
    </div>
  </div>

  <!-- Cards View -->
<div class="cards-container" *ngIf="viewMode === 'cards' && !isLoading">
  <mat-card 
    class="project-card" 
    *ngFor="let project of projects; let i = index" 
    [class.enterprise-card]="!isAdminOrSuperAdmin"
    [attr.data-label]="project.label.toLowerCase()"
    [@cardAnimation]="{value: '', params: {delay: i * 100}}"
  >
    <mat-card-header>
      <mat-card-title>{{ project.name }}</mat-card-title>
      <mat-card-subtitle>{{ project.label }}</mat-card-subtitle>
      
      <button
        *ngIf="isAdminOrSuperAdmin"
        mat-icon-button
        class="menu-button"
        [matMenuTriggerFor]="menu"
        matTooltip="Actions"
      >
        <mat-icon>more_vert</mat-icon>
      </button>
      <mat-menu #menu="matMenu">
        <button mat-menu-item (click)="navigateToUpdateProject(project.id)">
          <mat-icon>edit</mat-icon>
          Mettre a jour
        </button>
        <button mat-menu-item (click)="navigateToDeleteProject(project.id)">
          <mat-icon>delete</mat-icon>
          Supprimer
        </button>
      </mat-menu>
    </mat-card-header>
    
    <mat-card-content>
      <p class="card-description">{{ project.description }}</p>
      
      <div class="card-detail">
        <mat-icon>event</mat-icon>
        <span><strong>Due Date:</strong> {{ project.dueDate | date:'mediumDate' }}</span>
      </div>
      
      <div class="card-detail">
        <mat-icon>sensors</mat-icon>
        <span><strong>Sensors:</strong> {{ project.sensorCount }}</span>
      </div>
      
      <div class="progress-container" [ngClass]="getProgressClass(project.progress)">
  <div class="progress-header">
    <span class="progress-title">Progress</span>
    <span class="progress-text" [ngClass]="getProgressClass(project.progress)">{{project.progress}}%</span>
  </div>
  <mat-progress-bar mode="determinate" [value]="project.progress"></mat-progress-bar>
</div>
      
      <div class="members-container">
        <span class="members-title">Team:</span>
        <div class="members-avatars">
          <ng-container *ngIf="project.members && project.members.length > 0">
            <div class="member-avatar" *ngFor="let member of project.members.slice(0, 3)" 
                 [matTooltip]="member.userName" matTooltipPosition="above">
              {{ member.userName.charAt(0).toUpperCase() }}
            </div>
            <div class="member-more" *ngIf="project.members.length > 3" 
                 [matTooltip]="getMemberExcessTooltip(project.members)" matTooltipPosition="above">
              +{{ project.members.length - 3 }}
            </div>
          </ng-container>
          <div class="member-avatar" *ngIf="!project.members || project.members.length === 0">
            <mat-icon style="font-size: 16px">person_off</mat-icon>
          </div>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>

  <!-- List View -->
  <div class="table-container mat-elevation-z8" *ngIf="viewMode === 'list' && !isLoading">
    <table mat-table [dataSource]="projects" class="projects-table">
      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef>Name</th>
        <td mat-cell *matCellDef="let project">{{ project.name }}</td>
      </ng-container>
      <ng-container matColumnDef="label">
        <th mat-header-cell *matHeaderCellDef>Label</th>
        <td mat-cell *matCellDef="let project">
          <mat-chip class="label" [ngClass]="project.label.toLowerCase()">
            {{ project.label }}
          </mat-chip>
        </td>
      </ng-container>
      <ng-container matColumnDef="dueDate">
        <th mat-header-cell *matHeaderCellDef>Due Date</th>
        <td mat-cell *matCellDef="let project">{{ project.dueDate | date:'mediumDate' }}</td>
      </ng-container>
      <ng-container matColumnDef="sensorCount">
        <th mat-header-cell *matHeaderCellDef>Sensors</th>
        <td mat-cell *matCellDef="let project">{{ project.sensorCount }}</td>
      </ng-container>
      <ng-container matColumnDef="progress">
        <th mat-header-cell *matHeaderCellDef>Progress</th>
        <td mat-cell *matCellDef="let project">
          <mat-progress-bar mode="determinate" [value]="project.progress"></mat-progress-bar>
          <span class="progress-text">{{ project.progress }}%</span>
        </td>
      </ng-container>
      <ng-container matColumnDef="members">
        <th mat-header-cell *matHeaderCellDef>Members</th>
        <td mat-cell *matCellDef="let project">
          <div class="members-list">{{ getMemberUserNames(project.members) }}</div>
        </td>
      </ng-container>
      <ng-container matColumnDef="createdBy">
        <th mat-header-cell *matHeaderCellDef>Created By</th>
        <td mat-cell *matCellDef="let project">
          {{ project.createdByUser?.userName || project.createdBy }}
        </td>
      </ng-container>
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Actions</th>
        <td mat-cell *matCellDef="let project">
          <ng-container *ngIf="isAdminOrSuperAdmin; else noActions">
            <button
              mat-icon-button
              [matMenuTriggerFor]="menu"
              matTooltip="Actions"
            >
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu #menu="matMenu">
              <button mat-menu-item (click)="navigateToUpdateProject(project.id)">
                <mat-icon>edit</mat-icon>
                Update
              </button>
              <button mat-menu-item (click)="navigateToDeleteProject(project.id)">
                <mat-icon>delete</mat-icon>
                Delete
              </button>
            </mat-menu>
          </ng-container>
          <ng-template #noActions>
            <span class="not-authorized">Not Authorized</span>
          </ng-template>
        </td>
      </ng-container>
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </div>

  <!-- Loading Spinner -->
  <div class="loading-spinner" *ngIf="isLoading">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <!-- No Data Message -->
  <div class="no-data" *ngIf="!isLoading && projects.length === 0">
    <mat-icon>sentiment_dissatisfied</mat-icon>
    <p>No projects found</p>
  </div>
</div>