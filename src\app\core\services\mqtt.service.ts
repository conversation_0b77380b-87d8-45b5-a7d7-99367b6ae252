// // src/app/core/services/zigbee2mqtt.service.ts
// import { Injectable, OnDestroy } from '@angular/core';
// import { BehaviorSubject, Observable, Subject, Subscription } from 'rxjs';
// import { IMqttMessage, MqttService } from 'ngx-mqtt';
// import { MatSnackBar } from '@angular/material/snack-bar';

// export interface DeviceState {
//   [key: string]: any;
//   last_seen?: string;
//   linkquality?: number;
//   battery?: number;
//   temperature?: number;
//   humidity?: number;
//   state?: string;
//   brightness?: number;
//   color_temp?: number;
//   color?: { x: number; y: number };
// }

// export interface ZigbeeDevice {
//   ieee_address: string;
//   friendly_name: string;
//   type: string;
//   networkAddress: number;
//   state?: DeviceState; // Add this line
//   definition?: {
//     model: string;
//     vendor: string;
//     description: string;
//     supports?: string;
//   };
//   power_source?: string;
//   supported_features?: string[];
// }
// export interface ZigbeeGroup {
//   ID: number;
//   friendly_name: string;
//   members: any[];
// }

// export interface ZigbeeConfig {
//   coordinator: {
//     type: string;
//     ieee_address?: string;
//   };
//   log_level: string;
//   permit_join: boolean;
//   version: string;
// }

// @Injectable({
//   providedIn: 'root',
// })
// export class Zigbee2MqttService implements OnDestroy {
//   // Observables for components to subscribe to
//   public baseTopic: string = 'site1/local1/controller1';
//   // zigbee2mqtt site1/local1/controller1
//   private bridgeStateSubject = new BehaviorSubject<string>('offline');
//   private devicesSubject = new BehaviorSubject<ZigbeeDevice[]>([]);
//   private groupsSubject = new BehaviorSubject<ZigbeeGroup[]>([]);
//   private groupsLogs = new BehaviorSubject<ZigbeeGroup[]>([]);
//   private configSubject = new BehaviorSubject<ZigbeeConfig>({
//     coordinator: { type: 'Unknown' },
//     log_level: 'debug',
//     permit_join: false,
//     version: '0.0',
//   });

//   public bridgeState$ = this.bridgeStateSubject.asObservable();
//   public devices$ = this.devicesSubject.asObservable();
//   public groups$ = this.groupsSubject.asObservable();
//   public config$ = this.configSubject.asObservable();
//   public logs$ = this.groupsLogs.asObservable();

//   // Private data storage
//   private bridgeState: string = 'offline';
//   private subscriptions = new Map<string, Subscription>();
//   private devices: ZigbeeDevice[] = [];
//   private devicesExtended = new Map<number, DeviceState>();
//   private deviceNames: string[] = [];
//   private groups: ZigbeeGroup[] = [];
//   private config: ZigbeeConfig;

//   constructor(private mqttService: MqttService, private snackBar: MatSnackBar) {
//     this.config = this.configSubject.value;
//     this.initializeSubscriptions();
//   }

//   // Initialise la souscription aux logs Bridge
//   private bridgeLogSubject = new Subject<any>();
//   public subscribeToBridgeLogs(): void {
//     this.addTopic(`${this.baseTopic}/bridge/logging`, (message) => {
//       this.bridgeLogSubject.next(message.payload.toString());
//     });
//   }

//   // Expose un Observable que tes composants peuvent écouter
//   public getBridgeLogs(): Observable<any> {
//     return this.bridgeLogSubject.asObservable();
//   }
//   private initializeSubscriptions(): void {
//     // Bridge state monitoring
//     this.addTopic(`${this.baseTopic}/bridge/state`, (message) => {
//       this.bridgeState = message.payload.toString();
//       this.bridgeStateSubject.next(this.bridgeState);
//       this.showNotification(
//         'Coordinator state: ' + this.bridgeState.toUpperCase()
//       );
//     });

//     // Bridge configuration
//     this.addTopic(`${this.baseTopic}/bridge/config`, (message) => {
//       this.config = JSON.parse(message.payload.toString());
//       this.configSubject.next(this.config);
//       this.showNotification('Bridge configuration updated');
//     });

//     // Modern zigbee2mqtt uses bridge/devices instead of bridge/config/devices
//     // this.addTopic('zigbee2mqtt/bridge/devices', (message) => {
//     //const devicesData = JSON.parse(message.payload.toString());
//     //this.devices = devicesData.filter((device: any) => device.type !== 'Coordinator');
//     // this.deviceNames = this.devices.map(device => device.friendly_name);
//     // this.devicesSubject.next(this.devices);
//     //  this.showNotification(`Devices updated: ${this.devices.length}`);
//     // });
//     this.addTopic(`${this.baseTopic}/bridge/devices`, (message) => {
//       const devicesData = JSON.parse(message.payload.toString());
//       this.devices = devicesData
//         .filter((device: any) => device.type !== 'Coordinator')
//         .map((device: any) => ({
//           ...device,
//           ieee_address: device.ieee_address,
//           friendly_name: device.friendly_name,
//           type: device.type,
//           // Add networkAddress if available, but we'll primarily use IEEE address
//           networkAddress: device.network_address || 0,
//         }));

//       this.deviceNames = this.devices.map((device) => device.friendly_name);
//       this.devicesSubject.next(this.devices);
//     });

//     // Bridge logging
//     this.addTopic(`${this.baseTopic}/bridge/log`, (message) => {
//       this.handleBridgeLog(message);
//     });

//     // All device messages
//     this.addTopic(`${this.baseTopic}/+`, (message) => {
//       this.handleDeviceMessage(message);
//     });

//     // Request initial data after connection
//     setTimeout(() => {
//       this.updateSystemView();
//     }, 1000);
//   }
//   // zigbee2mqtt.service.ts
//   public setBaseTopic(newTopic: string): void {
//     if (this.baseTopic === newTopic) {
//       return;
//     }

//     // 1) Tear down old subscriptions
//     this.subscriptions.forEach((s) => s.unsubscribe());
//     this.subscriptions.clear();

//     // 2) Switch topic root
//     this.baseTopic = newTopic;

//     // 3) Re‑establish subscriptions and pull fresh data
//     this.initializeSubscriptions();
//     this.updateSystemView(); // ask the bridge for devices/groups again
//   }

//   private handleBridgeLog(message: IMqttMessage): void {
//     try {
//       const data = JSON.parse(message.payload.toString());
//       console.log('Bridge log type:', data.type);

//       switch (data.type) {
//         case 'groups':
//           this.groups = data.message;
//           this.groupsSubject.next(this.groups);
//           this.showNotification(`Groups updated: ${this.groups.length}`);
//           break;

//         case 'device_connected':
//           this.showNotification(
//             `New device connected: ${data.message.friendly_name}`
//           );
//           break;

//         case 'pairing':
//           if (data.message === 'interview_started') {
//             this.showNotification(
//               `Pairing started: ${data.meta.friendly_name}`
//             );
//           } else if (data.message === 'interview_successful') {
//             this.showNotification(
//               `New device paired: ${data.meta.friendly_name}`
//             );
//             this.updateDevices();
//           }
//           break;

//         case 'device_removed':
//         case 'device_force_removed':
//           if (data.message === 'left_network') {
//             this.showNotification(`Device removed: ${data.meta.friendly_name}`);
//           } else {
//             this.showNotification(`Device removed: ${data.message}`);
//           }
//           this.updateDevices();
//           break;

//         case 'device_renamed':
//           this.showNotification(
//             `Device renamed from ${data.message.from} to ${data.message.to}`
//           );
//           this.updateDevices();
//           break;

//         case 'group_renamed':
//           this.showNotification(
//             `Group renamed from ${data.message.from} to ${data.message.to}`
//           );
//           this.updateGroups();
//           break;

//         default:
//           console.log(`${message.topic}: ${message.payload.toString()}`);
//       }
//     } catch (error) {
//       console.error('Error parsing bridge log:', error);
//     }
//   }

//   private handleDeviceMessage(message: IMqttMessage): void {
//     const topic = message.topic;
//     const deviceName = topic.replace(`${this.baseTopic}/`, '');

//     // Skip bridge messages
//     if (deviceName.startsWith('bridge/')) {
//       return;
//     }

//     // Find the device
//     const device = this.devices.find((d) => d.friendly_name === deviceName);
//     if (!device) {
//       return;
//     }

//     // Skip set/get commands
//     if (topic.endsWith('/set') || topic.endsWith('/get')) {
//       return;
//     }

//     try {
//       const payload = JSON.parse(message.payload.toString());

//       // Update device extended data
//       let deviceData = this.devicesExtended.get(device.networkAddress) || {};
//       console.log(device.networkAddress, 'device data');
//       deviceData = { ...deviceData, ...payload };
//       this.devicesExtended.set(device.networkAddress, deviceData);

//       // Update devices array to trigger UI updates
//       this.devicesSubject.next([...this.devices]);
//     } catch (error) {
//       console.error('Error parsing device message:', error);
//     }
//   }

//   private addTopic(
//     topic: string,
//     callback: (message: IMqttMessage) => any
//   ): void {
//     const subscription = this.mqttService
//       .observe(topic)
//       .subscribe((message: IMqttMessage) => {
//         callback(message);
//       });
//     this.subscriptions.set(topic, subscription);
//   }

//   private showNotification(message: string): void {
//     this.snackBar.open(message, 'Close', {
//       duration: 3000,
//       horizontalPosition: 'right',
//       verticalPosition: 'top',
//     });
//   }

//   // Public API methods
//   public isConnected(): boolean {
//     return this.bridgeState === 'online';
//   }

//   public getDevices(): ZigbeeDevice[] {
//     return this.devices;
//   }

//   public getGroups(): ZigbeeGroup[] {
//     return this.groups;
//   }

//   public getConfig(): ZigbeeConfig {
//     return this.config;
//   }

//   public getDeviceExtended(networkAddress: number): DeviceState | undefined {
//     return this.devicesExtended.get(networkAddress);
//   }

//   // Device control methods
//   public toggleDevice(deviceName: string): void {
//     const device = this.devices.find((d) => d.friendly_name === deviceName);
//     if (device) {
//       const currentState = this.getDeviceExtended(device.networkAddress);
//       const newState = currentState?.state === 'ON' ? 'OFF' : 'ON';
//       this.setDeviceState(deviceName, { state: newState });
//     }
//   }

//   public setBrightness(deviceName: string, brightness: number): void {
//     this.setDeviceState(deviceName, { brightness: Math.round(brightness) });
//   }

//   public setDeviceState(deviceName: string, state: any): void {
//     this.unsafePublish(
//       `${this.baseTopic}/${deviceName}/set`,
//       JSON.stringify(state)
//     );
//   }

//   // System management methods
//   public updateSystemView(): void {
//     console.log('Updating system view');
//     this.updateDevices();
//     this.updateGroups();
//   }

//   public updateDevices(): void {
//     // Modern zigbee2mqtt
//     this.unsafePublish(`${this.baseTopic}/bridge/request/devices`, '');
//   }

//   public updateGroups(): void {
//     this.unsafePublish(`${this.baseTopic}/bridge/request/groups`, '');
//   }

//   public togglePermitJoin(): void {
//     const newPermitJoin = !this.config.permit_join;
//     this.showNotification(
//       `Permit Join toggled to ${newPermitJoin ? 'ON' : 'OFF'}`
//     );
//     this.unsafePublish(
//       `${this.baseTopic}/bridge/request/permit_join`,
//       JSON.stringify({ value: newPermitJoin })
//     );
//   }

//   // Device management
//   public removeDevice(name: string, force: boolean = false): void {
//     const topic = force
//       ? `${this.baseTopic}/bridge/request/device/remove`
//       : `${this.baseTopic}/bridge/request/device/remove`;

//     this.unsafePublish(topic, JSON.stringify({ id: name, force }));
//     this.showNotification(
//       `${force ? 'Force removing' : 'Removing'} device ${name}`
//     );
//   }

//   public renameDevice(oldName: string, newName: string): void {
//     this.unsafePublish(
//       `${this.baseTopic}/bridge/request/device/rename`,
//       JSON.stringify({ from: oldName, to: newName })
//     );
//     this.showNotification(`Renaming device ${oldName} to ${newName}`);
//   }

//   // Group management
//   public renameGroup(oldName: string, newName: string): void {
//     this.unsafePublish(
//       `${this.baseTopic}/bridge/request/group/rename`,
//       JSON.stringify({ from: oldName, to: newName })
//     );
//     this.showNotification(`Renaming group ${oldName} to ${newName}`);
//   }

//   public addToGroup(device: string, group: string): void {
//     this.unsafePublish(
//       `${this.baseTopic}/bridge/request/group/members/add`,
//       JSON.stringify({ group, device })
//     );
//     this.showNotification(`Adding ${device} to group ${group}`);
//   }

//   public removeFromGroup(device: string, group: string): void {
//     this.unsafePublish(
//       `${this.baseTopic}/bridge/request/group/members/remove`,
//       JSON.stringify({ group, device })
//     );
//     this.showNotification(`Removing ${device} from group ${group}`);
//   }

//   // Utility methods
//   public unsafePublish(topic: string, message: string): void {
//     if (this.isConnected()) {
//       this.mqttService.unsafePublish(topic, message, { qos: 1, retain: false });
//     } else {
//       console.warn('MQTT not connected, cannot publish message');
//       this.showNotification('Cannot send command: Bridge offline');
//     }
//   }

//   public ngOnDestroy(): void {
//     this.subscriptions.forEach((subscription, key) => {
//       console.log('Unsubscribing', key);
//       subscription.unsubscribe();
//     });
//     this.subscriptions.clear();
//   }
// }

// src/app/core/services/zigbee2mqtt.service.ts
import { Injectable, OnDestroy } from '@angular/core';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { IMqttMessage, MqttService } from 'ngx-mqtt';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Subject } from 'rxjs';

export interface DeviceState {
  [key: string]: any;
  last_seen?: string;
  linkquality?: number;
  battery?: number;
  temperature?: number;
  humidity?: number;
  state?: string;
  brightness?: number;
  color_temp?: number;
  color?: { x: number; y: number };
}

export interface ZigbeeDevice {
  ieee_address: string;
  friendly_name: string;
  type: string;
  networkAddress: number;
  state?: DeviceState; // Add this line
  definition?: {
    model: string;
    vendor: string;
    description: string;
    supports?: string;
  };
  power_source?: string;
  supported_features?: string[];
}
export interface ZigbeeGroup {
  ID: number;
  friendly_name: string;
  members: any[];
}

export interface ZigbeeConfig {
  coordinator: {
    type: string;
    ieee_address?: string;
  };
  log_level: string;
  permit_join: boolean;
  version: string;
}

@Injectable({
  providedIn: 'root'
})

export class Zigbee2MqttService implements OnDestroy {
  
  // Observables for components to subscribe to
  private baseTopic : string =  "zigbee2mqtt";
  // zigbee2mqtt
  private bridgeStateSubject = new BehaviorSubject<string>('offline');
  private devicesSubject = new BehaviorSubject<ZigbeeDevice[]>([]);
  private groupsSubject = new BehaviorSubject<ZigbeeGroup[]>([]);
  private groupsLogs = new BehaviorSubject<ZigbeeGroup[]>([]);
  private configSubject = new BehaviorSubject<ZigbeeConfig>({
    coordinator: { type: "Unknown" },
    log_level: "debug",
    permit_join: false,
    version: "0.0"
  });

  public bridgeState$ = this.bridgeStateSubject.asObservable();
  public devices$ = this.devicesSubject.asObservable();
  public groups$ = this.groupsSubject.asObservable();
  public config$ = this.configSubject.asObservable();
  public logs$ = this.groupsLogs.asObservable();

  // Private data storage
  private bridgeState: string = 'offline';
  private subscriptions = new Map<string, Subscription>();
  private devices: ZigbeeDevice[] = [];
  private devicesExtended = new Map<number, DeviceState>();
  private deviceNames: string[] = [];
  private groups: ZigbeeGroup[] = [];
  private config: ZigbeeConfig;


  constructor(
    private mqttService: MqttService,
    private snackBar: MatSnackBar
  ) {
    this.config = this.configSubject.value;
    this.initializeSubscriptions();
  }
 // Initialise la souscription aux logs Bridge
   private bridgeLogSubject = new Subject<any>();
  public subscribeToBridgeLogs(): void {
    this.addTopic(`${this.baseTopic}/bridge/logging`, (message) => {
      this.bridgeLogSubject.next(message.payload.toString());
    });
  }

  // Expose un Observable que tes composants peuvent écouter
  public getBridgeLogs(): Observable<any> {
    return this.bridgeLogSubject.asObservable();
  }
  private initializeSubscriptions(): void {
    // Bridge state monitoring
    this.addTopic(`${this.baseTopic}/bridge/state`, (message) => {
      this.bridgeState = message.payload.toString();
      this.bridgeStateSubject.next(this.bridgeState);
      this.showNotification('Coordinator state: ' + this.bridgeState.toUpperCase());

    });

    // Bridge configuration
    this.addTopic(`${this.baseTopic}/bridge/config`, (message) => {
      this.config = JSON.parse(message.payload.toString());
      this.configSubject.next(this.config);
      this.showNotification('Bridge configuration updated');
    });

    // Modern zigbee2mqtt uses bridge/devices instead of bridge/config/devices
   // this.addTopic('zigbee2mqtt/bridge/devices', (message) => {
      //const devicesData = JSON.parse(message.payload.toString());
      //this.devices = devicesData.filter((device: any) => device.type !== 'Coordinator');
     // this.deviceNames = this.devices.map(device => device.friendly_name);
     // this.devicesSubject.next(this.devices);
    //  this.showNotification(`Devices updated: ${this.devices.length}`);
   // });
   this.addTopic(`${this.baseTopic}/bridge/devices`, (message) => {
  const devicesData = JSON.parse(message.payload.toString());
  this.devices = devicesData
    .filter((device: any) => device.type !== 'Coordinator')
    .map((device: any) => ({
      ...device,
      ieee_address: device.ieee_address,
      friendly_name: device.friendly_name,
      type: device.type,
      // Add networkAddress if available, but we'll primarily use IEEE address
      networkAddress: device.network_address || 0
    }));
  
  this.deviceNames = this.devices.map(device => device.friendly_name);
  this.devicesSubject.next(this.devices);
});

    // Bridge logging
    this.addTopic(`${this.baseTopic}/bridge/log`, (message) => {
      this.handleBridgeLog(message);
    });

    // All device messages
    this.addTopic(`${this.baseTopic}/+`, (message) => {
      this.handleDeviceMessage(message);
    });

    // Request initial data after connection
    setTimeout(() => {
      this.updateSystemView();
    }, 1000);
  }

  private handleBridgeLog(message: IMqttMessage): void {
    try {
      const data = JSON.parse(message.payload.toString());
      console.log("Bridge log type:", data.type);

      switch (data.type) {
        case 'groups':
          this.groups = data.message;
          this.groupsSubject.next(this.groups);
          this.showNotification(`Groups updated: ${this.groups.length}`);
          break;

        case 'device_connected':
          this.showNotification(`New device connected: ${data.message.friendly_name}`);
          break;

        case 'pairing':
          if (data.message === 'interview_started') {
            this.showNotification(`Pairing started: ${data.meta.friendly_name}`);
          } else if (data.message === 'interview_successful') {
            this.showNotification(`New device paired: ${data.meta.friendly_name}`);
            this.updateDevices();
          }
          break;

        case 'device_removed':
        case 'device_force_removed':
          if (data.message === 'left_network') {
            this.showNotification(`Device removed: ${data.meta.friendly_name}`);
          } else {
            this.showNotification(`Device removed: ${data.message}`);
          }
          this.updateDevices();
          break;

        case 'device_renamed':
          this.showNotification(`Device renamed from ${data.message.from} to ${data.message.to}`);
          this.updateDevices();
          break;

        case 'group_renamed':
          this.showNotification(`Group renamed from ${data.message.from} to ${data.message.to}`);
          this.updateGroups();
          break;

        default:
          console.log(`${message.topic}: ${message.payload.toString()}`);
      }
    } catch (error) {
      console.error('Error parsing bridge log:', error);
    }
  }

  private handleDeviceMessage(message: IMqttMessage): void {
    const topic = message.topic;
    const deviceName = topic.replace(`${this.baseTopic}/`, '');

    // Skip bridge messages
    if (deviceName.startsWith('bridge/')) {
      return;
    }

    // Find the device
    const device = this.devices.find(d => d.friendly_name === deviceName);
    if (!device) {
      return;
    }

    // Skip set/get commands
    if (topic.endsWith('/set') || topic.endsWith('/get')) {
      return;
    }

    try {
      const payload = JSON.parse(message.payload.toString());
      
      // Update device extended data
      let deviceData = this.devicesExtended.get(device.networkAddress) || {};
      console.log(device.networkAddress, "device data");
      deviceData = { ...deviceData, ...payload };
      this.devicesExtended.set(device.networkAddress, deviceData);

      // Update devices array to trigger UI updates
      this.devicesSubject.next([...this.devices]);
    } catch (error) {
      console.error('Error parsing device message:', error);
    }
  }

  private addTopic(topic: string, callback: (message: IMqttMessage) => any): void {
    const subscription = this.mqttService.observe(topic).subscribe((message: IMqttMessage) => {
      callback(message);
    });
    this.subscriptions.set(topic, subscription);
  }

  private showNotification(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'right',
      verticalPosition: 'top'
    });
  }

  // Public API methods
  public isConnected(): boolean {
    return this.bridgeState === 'online';
  }

  public getDevices(): ZigbeeDevice[] {
    return this.devices;
  }

  public getGroups(): ZigbeeGroup[] {
    return this.groups;
  }

  public getConfig(): ZigbeeConfig {
    return this.config;
  }

  public getDeviceExtended(networkAddress: number): DeviceState | undefined {
    return this.devicesExtended.get(networkAddress);
  }

  // Device control methods
  public toggleDevice(deviceName: string): void {
    const device = this.devices.find(d => d.friendly_name === deviceName);
    if (device) {
      const currentState = this.getDeviceExtended(device.networkAddress);
      const newState = currentState?.state === 'ON' ? 'OFF' : 'ON';
      this.setDeviceState(deviceName, { state: newState });
    }
  }

  public setBrightness(deviceName: string, brightness: number): void {
    this.setDeviceState(deviceName, { brightness: Math.round(brightness) });
  }

  public setDeviceState(deviceName: string, state: any): void {
    this.unsafePublish(`${this.baseTopic}/${deviceName}/set`, JSON.stringify(state));
  }

  // System management methods
  public updateSystemView(): void {
    console.log("Updating system view");
    this.updateDevices();
    this.updateGroups();
  }

  public updateDevices(): void {
    // Modern zigbee2mqtt
    this.unsafePublish(`${this.baseTopic}/bridge/request/devices`, "");
  }

  public updateGroups(): void {
    this.unsafePublish(`${this.baseTopic}/bridge/request/groups`, '');
  }

  public togglePermitJoin(): void {
    const newPermitJoin = !this.config.permit_join;
    this.showNotification(`Permit Join toggled to ${newPermitJoin ? 'ON' : 'OFF'}`);
    this.unsafePublish(`${this.baseTopic}/bridge/request/permit_join`, JSON.stringify({ value: newPermitJoin }));
  }

  // Device management
  public removeDevice(name: string, force: boolean = false): void {
    const topic = force ? 
      `${this.baseTopic}/bridge/request/device/remove` : 
      `${this.baseTopic}/bridge/request/device/remove`;
    
    this.unsafePublish(topic, JSON.stringify({ id: name, force }));
    this.showNotification(`${force ? 'Force removing' : 'Removing'} device ${name}`);
  }

  public renameDevice(oldName: string, newName: string): void {
    this.unsafePublish(`${this.baseTopic}/bridge/request/device/rename`, 
      JSON.stringify({ from: oldName, to: newName }));
    this.showNotification(`Renaming device ${oldName} to ${newName}`);
  }

  // Group management
  public renameGroup(oldName: string, newName: string): void {
    this.unsafePublish(`${this.baseTopic}/bridge/request/group/rename`,
      JSON.stringify({ from: oldName, to: newName }));
    this.showNotification(`Renaming group ${oldName} to ${newName}`);
  }

  public addToGroup(device: string, group: string): void {
    this.unsafePublish(`${this.baseTopic}/bridge/request/group/members/add`,
      JSON.stringify({ group, device }));
    this.showNotification(`Adding ${device} to group ${group}`);
  }

  public removeFromGroup(device: string, group: string): void {
    this.unsafePublish(`${this.baseTopic}/bridge/request/group/members/remove`,
      JSON.stringify({ group, device }));
    this.showNotification(`Removing ${device} from group ${group}`);
  }

  // Utility methods
  public unsafePublish(topic: string, message: string): void {
    if (this.isConnected()) {
      this.mqttService.unsafePublish(topic, message, { qos: 1, retain: false });
    } else {
      console.warn('MQTT not connected, cannot publish message');
      this.showNotification('Cannot send command: Bridge offline');
    }
  }

  public ngOnDestroy(): void {
    this.subscriptions.forEach((subscription, key) => {
      console.log("Unsubscribing", key);
      subscription.unsubscribe();
    });
    this.subscriptions.clear();
  }

}