import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';

interface Sensor {
  type: string;
  action?: string;
  deviceTemperature: number;
  linkQuality: number;
  battery: number;  // Add battery property
  state?: string;
  powerOutageCount?: number;
  triggerCount?: number;
  waterLeak?: string;
  occupancy?: string;
  illuminance?: number;
}
@Component({
  selector: 'app-network-monitoring',
  imports: [  CommonModule,
    MatIconModule],
  templateUrl: './network-monitoring.component.html',
  styleUrl: './network-monitoring.component.css'
})
export class NetworkMonitoringComponent {

  sensors: Sensor[] = [
    {
      type: 'double_button_switch',
      action: 'single_both',
      deviceTemperature: 25,
      linkQuality: 84,
      battery: 95
    },
    {
      type: 'sensor_door_entry',
      state: 'OPEN',
      deviceTemperature: 30,
      powerOutageCount: 7,
      triggerCount: 0,
      linkQuality: 111,
      battery: 85
    },
    {
      type: 'sensor_leak',
      deviceTemperature: 30,
      powerOutageCount: 98,
      triggerCount: 1,
      waterLeak: 'Leaking',
      linkQuality: 45,
      battery: 70  // Low battery example
    },
    {
      type: 'sensor_leak_2',
      deviceTemperature: 28,
      powerOutageCount: 12,
      triggerCount: 0,
      waterLeak: 'No Leak',
      linkQuality: 95,
      battery: 15
    },
    {
      type: 'sensor_button_lof',
      action: 'single_press',
      deviceTemperature: 26,
      linkQuality: 78,
      battery: 60
    },
    {
      type: 'sensor_button_water',
      action: 'double_press',
      deviceTemperature: 27,
      linkQuality: 92,
      battery: 88
    },
    {
      type: 'sensor_motion_lof',
      occupancy: 'Occupied',
      illuminance: 240,
      deviceTemperature: 29,
      linkQuality: 88,
      battery: 45
    }
  ];

  getSensorDisplayName(type: string): string {
    const names: {[key: string]: string} = {
      'double_button_switch': 'Double Button Switch',
      'sensor_door_entry': 'Door Entry Sensor',
      'sensor_leak': 'Water Leak Sensor',
      'sensor_leak_2': 'Water Leak Sensor 2',
      'sensor_button_lof': 'Button Sensor',
      'sensor_button_water': 'Water Button Sensor',
      'sensor_motion_lof': 'Motion Sensor'
    };
    return names[type] || type;
  }
  getBatteryIcon(level: number): string {
  if (!level) return 'battery_unknown';
  if (level >= 90) return 'battery_full';
  if (level >= 70) return 'battery_6_bar';
  if (level >= 50) return 'battery_4_bar';
  if (level >= 30) return 'battery_3_bar';
  if (level >= 20) return 'battery_2_bar';
  return 'battery_alert';
}

}
