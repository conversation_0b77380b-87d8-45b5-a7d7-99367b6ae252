<div class="container">
  <h1 class="page-title">Network Monitoring</h1>
  <!-- Rest of your content -->
    <div class="sensor-dashboard">
  <!-- Single Card Template -->
  <div *ngFor="let sensor of sensors" class="sensor-card">
    <!-- Sensor Header with Icon -->
    <div class="sensor-header">
      <div class="sensor-title">
        <mat-icon class="sensor-icon" [ngSwitch]="sensor.type">
          <span *ngSwitchCase="'double_button_switch'">toggle_on</span>
          <span *ngSwitchCase="'sensor_door_entry'">door_front</span>
          <span *ngSwitchCase="'sensor_leak'">water_drop</span>
          <span *ngSwitchCase="'sensor_leak_2'">water_drop</span>
          <span *ngSwitchCase="'sensor_button_lof'">smart_button</span>
          <span *ngSwitchCase="'sensor_button_water'">tap_and_play</span>
          <span *ngSwitchCase="'sensor_motion_lof'">motion_sensor</span>
          <span *ngSwitchDefault>sensors</span>
        </mat-icon>
        <h3>{{ getSensorDisplayName(sensor.type) }}</h3>
      </div>
      <span class="signal-quality">
        <mat-icon>signal_cellular_alt</mat-icon>
        {{ sensor.linkQuality || 'N/A' }} LQ
      </span>
          <span class="battery-level " [class.battery-low]="sensor.battery < 20">
      <mat-icon>
        {{getBatteryIcon(sensor.battery)}}
      </mat-icon>
      {{sensor.battery || 'N/A'}}%
    </span>
    </div>

    <div class="sensor-data">
      <!-- Double Button Switch -->
      <div *ngIf="sensor.type === 'double_button_switch'" class="sensor-type-data">
        <div class="data-row" *ngIf="sensor.action">
          <span class="data-label">
            <mat-icon>power_settings_new</mat-icon>
            Action:
          </span>
          <span class="data-value">{{ sensor.action }}</span>
        </div>
      </div>

      <!-- Door Entry Sensor -->
      <div *ngIf="sensor.type === 'sensor_door_entry'" class="sensor-type-data">
        <div class="data-row" *ngIf="sensor.state">
          <span class="data-label">
            <mat-icon>{{ sensor.state === 'OPEN' ? 'lock_open' : 'lock' }}</mat-icon>
            State:
          </span>
          <span class="data-value">{{ sensor.state }}</span>
        </div>
        <div class="data-row" *ngIf="sensor.deviceTemperature">
          <span class="data-label">
            <mat-icon>device_thermostat</mat-icon>
            Temperature:
          </span>
          <span class="data-value">{{ sensor.deviceTemperature }} °C</span>
        </div>
        <div class="data-row" *ngIf="sensor.powerOutageCount !== undefined">
          <span class="data-label">
            <mat-icon>bolt</mat-icon>
            Power Outages:
          </span>
          <span class="data-value">{{ sensor.powerOutageCount }}</span>
        </div>
      </div>

      <!-- Water Leak Sensor -->
      <div *ngIf="sensor.type === 'sensor_leak' || sensor.type === 'sensor_leak_2'" class="sensor-type-data">
        <div class="data-row" *ngIf="sensor.waterLeak">
          <span class="data-label">
            <mat-icon>warning</mat-icon>
            Status:
          </span>
          <span class="data-value" [class.warning]="sensor.waterLeak === 'Leaking'">
            {{ sensor.waterLeak }}
          </span>
        </div>
        <div class="data-row" *ngIf="sensor.deviceTemperature">
          <span class="data-label">
            <mat-icon>device_thermostat</mat-icon>
            Temperature:
          </span>
          <span class="data-value">{{ sensor.deviceTemperature }} °C</span>
        </div>
        <div class="data-row" *ngIf="sensor.triggerCount !== undefined">
          <span class="data-label">
            <mat-icon>notifications_active</mat-icon>
            Trigger Count:
          </span>
          <span class="data-value">{{ sensor.triggerCount }}</span>
        </div>
      </div>

      <!-- Button Sensor -->
      <div *ngIf="sensor.type === 'sensor_button_lof' || sensor.type === 'sensor_button_water'" class="sensor-type-data">
        <div class="data-row" *ngIf="sensor.action">
          <span class="data-label">
            <mat-icon>touch_app</mat-icon>
            Action:
          </span>
          <span class="data-value">{{ sensor.action }}</span>
        </div>
        <div class="data-row" *ngIf="sensor.deviceTemperature">
          <span class="data-label">
            <mat-icon>device_thermostat</mat-icon>
            Temperature:
          </span>
          <span class="data-value">{{ sensor.deviceTemperature }} °C</span>
        </div>
      </div>

      <!-- Motion Sensor -->
      <div *ngIf="sensor.type === 'sensor_motion_lof'" class="sensor-type-data">
        <div class="data-row" *ngIf="sensor.occupancy">
          <span class="data-label">
            <mat-icon>{{ sensor.occupancy === 'Occupied' ? 'directions_run' : 'timer_off' }}</mat-icon>
            Status:
          </span>
          <span class="data-value">{{ sensor.occupancy }}</span>
        </div>
        <div class="data-row" *ngIf="sensor.illuminance">
          <span class="data-label">
            <mat-icon>light_mode</mat-icon>
            Light Level:
          </span>
          <span class="data-value">{{ sensor.illuminance }} lux</span>
        </div>
        <div class="data-row" *ngIf="sensor.deviceTemperature">
          <span class="data-label">
            <mat-icon>device_thermostat</mat-icon>
            Temperature:
          </span>
          <span class="data-value">{{ sensor.deviceTemperature }} °C</span>
        </div>
      </div>
    </div>
  </div>
</div>


</div>
