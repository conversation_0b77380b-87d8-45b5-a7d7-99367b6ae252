<div class="edit-form-popup active">
  <div class="edit-form-container">
    <div class="form-header">
      <h2 class="form-title">
        <mat-icon class="title-icon">edit</mat-icon>
        Modifier le Site
      </h2>
      <button class="close-button" (click)="onClose()">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <!-- Form Errors at the top -->
    <div class="validation-errors" *ngIf="editSiteForm.invalid && (editSiteForm.touched || editSiteForm.dirty)">
      <div class="validation-errors-title">
        <mat-icon>error_outline</mat-icon>
        Erreurs de validation
      </div>
      <ul class="validation-errors-list">
        <li *ngIf="editSiteForm.get('Name')?.invalid">
          <mat-icon>error</mat-icon>
          Le nom est requis
        </li>
        <li *ngIf="editSiteForm.get('Adress')?.invalid">
          <mat-icon>error</mat-icon>
          L'adresse est requise
        </li>
        <li *ngIf="editSiteForm.get('PhoneNumber')?.invalid">
          <mat-icon>error</mat-icon>
          Le téléphone est requis
        </li>
        <li *ngIf="editSiteForm.get('Contact')?.invalid">
          <mat-icon>error</mat-icon>
          Le contact est requis
        </li>
        <li *ngIf="editSiteForm.get('Manager')?.invalid">
          <mat-icon>error</mat-icon>
          Le responsable est requis
        </li>
        <li *ngIf="editSiteForm.get('Email')?.invalid">
          <mat-icon>error</mat-icon>
          Un email valide est requis
        </li>
        <li *ngIf="editSiteForm.get('Status')?.invalid">
          <mat-icon>error</mat-icon>
          Le statut est requis
        </li>
      </ul>
    </div>

    <form [formGroup]="editSiteForm" (ngSubmit)="onSubmit()">
      <div class="form-grid">
        <!-- Required Fields -->
        <div class="form-group">
          <label for="editName">Nom <span class="required">*</span></label>
          <input id="editName" type="text" formControlName="Name" required />
        </div>
        <div class="form-group">
          <label for="editAdress">Adresse <span class="required">*</span></label>
          <input id="editAdress" type="text" formControlName="Adress" required />
        </div>
        <div class="form-group">
          <label for="editPhoneNumber">Téléphone <span class="required">*</span></label>
          <input id="editPhoneNumber" type="text" formControlName="PhoneNumber" required />
        </div>
        <div class="form-group">
          <label for="editContact">Contact <span class="required">*</span></label>
          <input id="editContact" type="text" formControlName="Contact" required />
        </div>
        <div class="form-group">
          <label for="editManager">Responsable <span class="required">*</span></label>
          <input id="editManager" type="text" formControlName="Manager" required />
          <div class="form1" *ngIf="!showOptionalFields">
            <button class="show-more-buttons" (click)="onToggleOptionalFields($event)">
              <span>{{ showOptionalFields ? "Afficher Moins" : "Afficher Plus" }}</span>
              <i class="material-icons">
                {{ showOptionalFields ? "expand_less" : "expand_more" }}
              </i>
            </button>
          </div>
        </div>
        <div class="form-group">
          <label for="editEmail">Email <span class="required">*</span></label>
          <input id="editEmail" type="email" formControlName="Email" required />
        </div>

        <!-- Optional Fields (hidden by default) -->
        <div class="optional-fields" [class.visible]="showOptionalFields">
          <div class="form-group">
            <label for="editStatus">Statut <span></span></label>
            <select id="editStatus" formControlName="Status">
              <option value="">Sélectionnez un statut</option>
              <option value="Actif">Actif</option>
              <option value="Inactif">Inactif</option>
              <option value="En maintenance">En maintenance</option>
            </select>     
          </div>
          <div class="form-group">
            <label for="editAddressComplement">Complément d'adresse</label>
            <input id="editAddressComplement" type="text" formControlName="AddressComplement" />
          </div>
          <div class="form-group">
            <label for="editDescription">Description</label>
            <textarea id="editDescription" formControlName="Description"></textarea>
          </div>
          <div class="form-group">
            <label for="editGrade">Grade</label>
            <select id="editGrade" formControlName="Grade">
              <option value="A+++">A+++</option>
              <option value="A++">A++</option>
              <option value="A+">A+</option>
              <option value="A">A</option>
              <option value="B">B</option>
              <option value="C">C</option>
              <option value="D">D</option>
            </select>
          </div>
          <div class="form-group">
            <label for="editLatitude">Latitude</label>
            <input id="editLatitude" type="number" formControlName="Latitude" />
          </div>
          <div class="form-group">
            <label for="editLongtitude">Longitude</label>
            <input id="editLongtitude" type="number" formControlName="Longtitude" />
          </div>
          <div class="form-group">
            <label for="editSurface">Surface</label>
            <input id="editSurface" type="number" formControlName="Surface" />
          </div>
          <div class="form-group">
            <label for="editImage">Image</label>

           

            <!-- Loading indicator for image -->
            <div *ngIf="isLoadingImage" class="image-loading">
              <mat-icon class="loading-spinner">refresh</mat-icon>
              <span>Chargement de l'image...</span>
            </div>

            <!-- Display existing image if it exists -->
            <div *ngIf="hasExistingImage() && !isLoadingImage" class="existing-image-container">
              <div class="image-preview">
                <img [src]="getExistingImageUrl()" alt="Site" class="existing-image">
                <div class="image-overlay">
                  <button type="button" class="remove-image-button" (click)="onRemoveExistingImage()" title="Supprimer l'image">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </div>
              <p class="image-info">Image actuelle du site</p>
            </div>

            <!-- File input for new image -->
            <div class="file-input-container">
              <input
                type="file"
                id="editImage"
                (change)="onEditImagesSelected($event)"
                accept="image/*"
                class="file-input"
              />
              <label for="editImage" class="file-input-label">
                <mat-icon>cloud_upload</mat-icon>
                {{ hasExistingImage() ? 'Changer l\'image' : 'Choisir une image' }}
              </label>
            </div>
          </div>
          <div class="form1" *ngIf="showOptionalFields">
            <button class="show-more-buttons" (click)="onToggleOptionalFields($event)">
              <span>{{ showOptionalFields ? "Afficher Moins" : "Afficher Plus" }}</span>
              <i class="material-icons">
                {{ showOptionalFields ? "expand_less" : "expand_more" }}
              </i>
            </button>
          </div>
        </div>
      </div>
      <div class="form-actions">
        <button type="button" (click)="onClose()">Annuler</button>
        <button type="submit" [disabled]="!editSiteForm.valid">Enregistrer</button>
      </div>
    </form>
  </div>
</div>