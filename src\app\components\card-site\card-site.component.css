* Dashboard Button on Image */.dashboard-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(76, 175, 80, 0.9);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 2;
  opacity: 0;
  transform: scale(0.8);
}
.dashboard-btn:hover {
  background: rgba(76, 175, 80, 1);
  transform: scale(1);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}
.dashboard-btn i {
  color: white;
  font-size: 20px;
} /* Show button on card hover */
.site-card:hover .dashboard-btn {
  opacity: 1;
  transform: scale(1);
} /* Alternative: Always visible button with lower opacity */
.dashboard-btn.always-visible {
  opacity: 0.7;
  transform: scale(1);
}
.dashboard-btn.always-visible:hover {
  opacity: 1;
}

.site-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  max-width: 300px;
  display: flex;
  flex-direction: column;
}

.site-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 5px;
  border-top: 1px solid #e5e7eb;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  justify-content: center;
  background: transparent;
}

.action-btn i {
  font-size: 20px;
}

.action-btn span {
  white-space: nowrap;
}

.action-btn.edit {
  background-color: #2196f3;
  color: white;
}

.action-btn.edit:hover {
  background-color: #1976d2;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
}

.image-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f5f5f5;
}

.image-error::after {
  content: "Image non disponible";
  color: #666;
  font-size: 14px;
}

.no-image {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f5f5f5;
  flex-direction: column;
  width: 100%;
  color: #757575;
  font-size: 0.9rem;
}

.no-image i {
  color: #ccc;
  font-size: 2rem;
  opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
  .site-actions {
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Update other action buttons to match the style */
.action-btn.view {
  background: #3498db;
  color: white;
}

.action-btn.view:hover {
  background-color: #e8f5e9;
  transform: scale(1.1);
}

.site-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Image Container */
.image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.gallery-track {
  display: flex;
  height: 100%;
  transition: transform 0.5s ease-in-out;
}

.gallery-item {
  min-width: 100%;
  height: 100%;
  position: relative;
}

.site-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Navigation Buttons */
.nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.nav-btn:hover {
  background: #ffffff;
  transform: translateY(-50%) scale(1.1);
}

.nav-btn.prev {
  left: 10px;
}

.nav-btn.next {
  right: 10px;
}

.nav-btn i {
  color: #333;
  font-size: 1.5rem;
}

/* Info Container */
.info-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.site-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.site-name {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c3e50;
}

.card-image-container {
  position: relative;
  width: 100%;
  height: 200px;
  background-color: #f5f5f5;
  overflow: hidden;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
  font-size: 14px;
}

.loading-icon {
  font-size: 24px;
  margin-bottom: 8px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.image-carousel {
  width: 100%;
  height: 100%;
  position: relative;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.carousel-controls {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
}

.carousel-btn {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.carousel-btn:hover {
  background: white;
  transform: scale(1.1);
}

.carousel-btn i {
  color: #333;
  font-size: 24px;
}

.card-header {
  padding: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.site-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
}

.site-type {
  display: inline-block;
  padding: 4px 12px;
  background: #e8f5e9;
  color: #2e7d32;
  border-radius: 20px;
  font-size: 0.875rem;
  margin-top: 8px;
}

.card-content {
  padding: 8px;
  flex: 1;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #4a5568;
  font-size: 0.875rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.icon-small {
  font-size: 18px;
  color: var(--primary);
}

.card-actions {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  margin-top: auto;
  background-color: #fafafa;
}

.action-buttons-wrapper {
  display: grid;
  align-items: end;
  justify-items: center;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  /* gap: 8px; */
  width: 80%;
}

.btn {
  width: 90%;
  min-height: 36px;
  padding: 8px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: clamp(0.75rem, 2vw, 0.875rem);
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.btn i {
  font-size: clamp(18px, 2.5vw, 24px);
  flex-shrink: 0;
}

.btn-text {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Responsive breakpoints */
@media (max-width: 350px) {
  .btn-text {
    display: none;
  }

  .btn {
    padding: 8px;
    aspect-ratio: 1;
  }

  .btn i {
    margin: 0;
  }
}

/* Keep your existing button color styles */
.btn-primary {
  background: var(--primary);
  color: white;
}

.btn-accent {
  background: #2196f3;
  color: white;
}

.btn-danger {
  background: #f44336;
  color: white;
}

/* Update hover effects */
.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary:hover {
  background: #43a047;
}

.btn-accent:hover {
  background: #1976d2;
}

.btn-danger:hover {
  background: #e53935;
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #f5f5f5;
  color: #9e9e9e;
  gap: 8px;
}

.image-placeholder i {
  font-size: 48px;
}

.image-placeholder p {
  font-size: 0.875rem;
  margin: 0;
}

.site-details {
  margin: 0px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  color: #555;
  font-size: 0.95rem;
  padding: 12px 0;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #555;
  font-size: 0.9rem;
}

.detail-item i {
  color: #2ecc71;
  font-size: 1.1rem;
  min-width: 24px;
  text-align: center;
}

/* Stats Section */
.stats-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin: 0;
  padding: 8px 0;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.stat-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #2c3e50;
}

.stat-label {
  font-size: 0.75rem;
  color: #7f8c8d;
  margin-top: 4px;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.action-btn.delete {
  background: #e74c3c;
  color: white;
}

.action-btn.add {
  background: #2ecc71;
  color: white;
}

.action-btn:hover {
  transform: translateY(-2px);
}

.action-btn.delete:hover {
  background-color: #e53935;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(239, 83, 80, 0.2);
}

/* Responsive Design */
@media (max-width: 600px) {
  .stats-section {
    grid-template-columns: 1fr;
  }

  .site-actions {
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
  }

  .card-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
    padding: 12px;
  }
  .card-actions .btn {
    width: 100%;
    justify-content: center;
    font-size: 1rem;
  }
}

.status-actif {
  background-color: #d4edda;
  color: #155724;
}

.status-inactif {
  background-color: #f8d7da;
  color: #721c24;
}

.status-maintenance {
  background-color: #fff3cd;
  color: #856404;
}
.no-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #f0f8f0;
}
.no-logo .material-icons {
  font-size: 100px;
  color: #c5c5c5;
}
