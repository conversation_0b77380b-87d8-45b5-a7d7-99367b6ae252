import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import {
  Form<PERSON>uilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ControllerServeurApiService } from '@app/core/services/administrative/controllerserveur.service';
import { ControllerServeur } from '@app/core/models/controllerServeur';
import { Licence } from '@app/core/models/licence';
import { Controller } from '@app/core/models/controller';
import { ControllerServerController } from '@app/core/models/controllerServerController';
import { ControllerServerControllerApiService } from '@app/core/services/administrative/controllerservercontroller.service';
import { ControllerApiService } from '@app/core/services/administrative/controller.service';
import { forkJoin } from 'rxjs';
import { Subscription } from '@app/core/models/subscription';
import { SubscriptionApiService } from '@app/core/services/administrative/subscription.service';
import { <PERSON><PERSON>, Page } from '@app/core/models/util/page';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { NgToastService } from 'ng-angular-popup';
// import { MatIcon } from '@angular/material/icon';

@Component({
  selector: 'app-form-cs',
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './form-cs.component.html',
  styleUrl: './form-cs.component.css',
})
export class FormCsComponent implements OnInit, OnChanges {
  @Input() clientId: string = '';
  @Input() controllerServer: ControllerServeur | null = null;
  @Input() isEditMode: boolean = false;
  @Output() formClosed = new EventEmitter<void>();
  @Output() controllerServerCreated = new EventEmitter<ControllerServeur>();
  @Output() controllerServerUpdated = new EventEmitter<ControllerServeur>();
  @Input() licences: Licence[] = [];
  availableControllers: Controller[] = [];
  selectedControllerIds: string[] = [];
  currentControllerRelations: ControllerServerController[] = [];
  isLoadingControllers: boolean = false;
  showErrorMessages: string[] = [];
  hasTriedSubmit: boolean = false;

  lister?: Lister;

  subscriptions: Subscription[] = [];

  createControllerServerForm!: FormGroup;
  isSubmitting: boolean = false;

  constructor(
    private fb: FormBuilder,
    private controllerServerService: ControllerServeurApiService,
    private controllerServerControllerService: ControllerServerControllerApiService,
    private controllerService: ControllerApiService,
    private subscriptionService: SubscriptionApiService,
    readonly toast: NgToastService
  ) {}

  ngOnInit(): void {
    console.log('Form initialized with licences:', this.licences);
    this.initializeForm();

    if (this.clientId != '') {
      this.lister = new Lister();
      this.lister.FilterParams = [
        {
          AndOr: 'AND',
          Column: 'ClientId',
          Op: 'eq',
          Value: this.clientId,
        },
      ];

      this.lister.Pagination = {
        PageSize: 500,
      };

      console.log('lister', this.lister);

      this.subscriptionService.gatePage(this.lister).subscribe({
        next: (result: Page<Subscription>) => {
          this.subscriptions = result.Content ?? [];
          this.lister = result.Lister;
          console.log(this.subscriptions, 'ssssssssssssssssssssssssssssssssss');
        },
      });
    }

    this.loadControllers();

    if (!this.isEditMode) {
      this.generateControllerServeurName();
    }

    if (this.isEditMode && this.controllerServer) {
      this.populateForm();
      this.loadControllerRelations();
    }
  }

  generateControllerServeurName(): void {
    this.controllerServerService.generateName().subscribe({
      next: (name: string) => {
        this.createControllerServerForm.patchValue({ name: name });
      },
      error: (err) => {
        console.error('Failed to generate name:', err);
      },
    });
  }

  private loadControllerRelations(): void {
    if (!this.controllerServer?.Id) return;

    console.log(
      'Loading controller relations for Controller Server ID:',
      this.controllerServer.Id
    );

    // The API endpoint seems to return ControllerServeur objects that contain ControllerServerControllers
    this.controllerServerControllerService
      .getByControllerServerId(this.controllerServer.Id)
      .subscribe({
        next: (response: any[]) => {
          console.log('Raw API response:', response);

          // The response appears to be an array of ControllerServeur objects
          // We need to extract the ControllerServerControllers from the nested structure
          let allRelations: ControllerServerController[] = [];

          if (response && response.length > 0) {
            // Each item in the response might be a ControllerServeur with ControllerServerControllers
            response.forEach((item) => {
              if (
                item.ControllerServerControllers &&
                Array.isArray(item.ControllerServerControllers)
              ) {
                // Filter relations that match our current controller server
                const relevantRelations =
                  item.ControllerServerControllers.filter(
                    (relation: any) =>
                      relation.IdControllerServeur === this.controllerServer?.Id
                  );
                allRelations = [...allRelations, ...relevantRelations];
              }
            });
          }

          // Alternative approach: If the response structure is different
          // Check if the response directly contains ControllerServerController objects
          if (allRelations.length === 0 && response && response.length > 0) {
            // Check if response items have IdController and IdControllerServeur properties
            const firstItem = response[0];
            if (
              firstItem &&
              firstItem.IdController &&
              firstItem.IdControllerServeur
            ) {
              allRelations = response.filter(
                (relation: any) =>
                  relation.IdControllerServeur === this.controllerServer?.Id
              );
            }
          }

          console.log(
            'Filtered relations for current controller server:',
            allRelations
          );

          this.currentControllerRelations = allRelations;

          // Extract controller IDs from the relations
          this.selectedControllerIds = allRelations.map(
            (relation: any) => relation.IdController
          );

          console.log('Selected controller IDs:', this.selectedControllerIds);
          console.log('Available controllers:', this.availableControllers);

          // Verify that the selected controllers exist in available controllers
          const validSelectedIds = this.selectedControllerIds.filter((id) =>
            this.availableControllers.some((controller) => controller.Id === id)
          );

          if (validSelectedIds.length !== this.selectedControllerIds.length) {
            console.warn(
              'Some selected controller IDs were not found in available controllers'
            );
            this.selectedControllerIds = validSelectedIds;
          }

          console.log(
            'Final selected controller IDs:',
            this.selectedControllerIds
          );
        },
        error: (error: any) => {
          console.error('Error loading controller server relations:', error);
        },
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['licences']) {
      console.log('Licences changed:', changes['licences'].currentValue);
    }

    if (changes['controllerServer'] || changes['isEditMode']) {
      if (this.createControllerServerForm) {
        if (this.isEditMode && this.controllerServer) {
          this.populateForm();
          // Ensure controllers are loaded before loading relations
          if (this.availableControllers.length > 0) {
            this.loadControllerRelations();
          } else {
            // If controllers haven't loaded yet, wait for them
            this.loadControllers();
          }
        } else {
          this.selectedControllerIds = [];
          this.currentControllerRelations = [];
          this.initializeForm();
        }
      }
    }
  }

  private loadControllers(): void {
    this.isLoadingControllers = true;
    this.controllerService.getAllLightByClient(this.clientId).subscribe({
      next: (servers: Controller[]) => {
        this.availableControllers = servers;
        this.isLoadingControllers = false;
        console.log('Loaded controllers:', servers);

        if (
          this.isEditMode &&
          this.controllerServer &&
          this.selectedControllerIds.length === 0
        ) {
          this.loadControllerRelations();
        }
      },
      error: (error: any) => {
        console.error('Error loading controllers:', error);
        this.isLoadingControllers = false;
      },
    });
  }

  onControllerSelect(event: Event): void {
    const target = event.target as HTMLSelectElement;
    const serverId = target.value;

    if (serverId && !this.selectedControllerIds.includes(serverId)) {
      this.selectedControllerIds.push(serverId);
      console.log('Selected controller servers:', this.selectedControllerIds);
    }

    target.value = '';
  }

  removeControllerServer(serverId: string): void {
    this.selectedControllerIds = this.selectedControllerIds.filter(
      (id) => id !== serverId
    );
    console.log(
      'Removed controller server, remaining:',
      this.selectedControllerIds
    );
  }

  isControllerServerSelected(serverId: string): boolean {
    return this.selectedControllerIds.includes(serverId);
  }

  getSelectedControllers(): Controller[] {
    return this.availableControllers.filter((server) =>
      this.selectedControllerIds.includes(server.Id)
    );
  }

  private initializeForm(): void {
    this.createControllerServerForm = this.fb.group({
      name: [
        { value: '', disabled: true },
        [Validators.required, Validators.minLength(2)],
      ],
      maxControllers: [0],
      maxSensors: [0],
      geographicZone: [''],
      commercialCondition: [''],
      triggerType: [''],
      actionType: [''],
      eventType: [''],
      status: [
        { value: 'Actif', disabled: !this.isEditMode },
        [Validators.required],
      ],
      subscriptionId: ['', [Validators.required]],
    });
  }

  private populateForm(): void {
    if (this.controllerServer) {
      console.log(
        'Populating form with controller server:',
        this.controllerServer
      );
      console.log(
        'Controller server IdLicence:',
        this.controllerServer.SubscriptionId
      );

      let licenceId = '';
      if (this.controllerServer.SubscriptionId) {
        licenceId = this.controllerServer.SubscriptionId;
      } else if (
        this.controllerServer.Subscription &&
        this.controllerServer.Subscription.Id
      ) {
        licenceId = this.controllerServer.Subscription.Id || '';
      }

      console.log('Using licence ID:', licenceId);

      this.createControllerServerForm.patchValue({
        name: this.controllerServer.Name || '',
        maxControllers: this.controllerServer.MaxControllers || 0,
        maxSensors: this.controllerServer.MaxSensors || 0,
        geographicZone: this.controllerServer.GeographicZone || '',
        commercialCondition: this.controllerServer.CommercialCondition || '',
        triggerType: this.controllerServer.TriggerType || '',
        actionType: this.controllerServer.ActionType || '',
        eventType: this.controllerServer.EventType || '',
        status: this.controllerServer.Status || '',
        subscriptionId: licenceId,
      });
    }
  }

  // Helper methods for licence handling
  getLicenceId(licence: Licence): string {
    // Handle both Id and id properties consistently
    return licence.Id || '';
  }

  getLicenceName(licence: Licence): string {
    return licence.Name || `Licence ${this.getLicenceId(licence)}`;
  }

  public showSuccess(message: string, title: string) {
    this.toast.info(message, title, 3000, false);
  }
  public showError(message: string, title: string) {
    this.toast.warning(message, title, 3000, false);
  }

  onSubmit(): void {
    console.log('Form submission started');
    console.log('Form valid:', this.createControllerServerForm.valid);
    console.log('Form value:', this.createControllerServerForm.value);

    // Set that we've tried to submit
    this.hasTriedSubmit = true;

    if (this.createControllerServerForm.valid && !this.isSubmitting) {
      // Clear any previous error messages
      this.showErrorMessages = [];
      this.isSubmitting = true;

      const formData = this.createControllerServerForm.getRawValue();
      console.log('Form data being processed:', formData);

      // Prepare the data according to your API structure
      const controllerServerData: Partial<ControllerServeur> = {
        Name: formData.name,
        MaxControllers: parseInt(formData.maxControllers) || 0,
        MaxSensors: parseInt(formData.maxSensors) || 0,
        GeographicZone: formData.geographicZone || '',
        CommercialCondition: formData.commercialCondition || '',
        TriggerType: formData.triggerType || '',
        ActionType: formData.actionType || '',
        EventType: formData.eventType || '',
        Status: formData.status,
        SubscriptionId:
          formData.subscriptionId && formData.subscriptionId !== ''
            ? formData.subscriptionId
            : null,
      };

      console.log('Controller server data to be sent:', controllerServerData);

      if (this.isEditMode && this.controllerServer) {
        const updateData = {
          ...controllerServerData,
          Id: this.controllerServer.Id,
        };

        this.controllerServerService.update(updateData).subscribe({
          next: (response: any) => {
            console.log('Controller Server updated successfully:', response);
            this.updateControllerRelations(this.controllerServer!.Id);
          },
          error: (error: any) => {
            console.error('Error updating controller server:', error);
            this.isSubmitting = false;
            alert('Erreur lors de la mise à jour du contrôleur serveur');
          },
        });
      } else {
        if (!formData.subscriptionId) {
          this.isSubmitting = false;
          this.showErrorMessages.push('Veuillez sélectionner un abonnement');
          return;
        }
        this.controllerServerService.canAdd(formData.subscriptionId).subscribe({
          next: (canAdd: boolean) => {
            if (canAdd) {
              this.controllerServerService
                .post('', controllerServerData)
                .subscribe({
                  next: (response: any) => {
                    console.log(
                      'Controller Server created successfully:',
                      response
                    );
                    this.createControllerRelations(response.Id);
                    this.controllerServerCreated.emit(response);
                    this.resetForm();
                    this.isSubmitting = false;
                  },
                  error: (err) => {
                    this.showError('Erreur lors de la création.', 'Erreur');
                  },
                });
            }
          },
          error: (err: any) => {
            const message =
              err?.error ||
              'Vous avez atteint la limite de contrôleurs serveurs.';
            this.showError(message, 'Erreur');
            this.isSubmitting = false;
          },
        });
      }
    } else {
      console.log('Form is invalid, collecting validation errors');

      // NEW: Collect all validation errors
      this.collectValidationErrors();

      // Mark all fields as touched to show individual field errors
      Object.keys(this.createControllerServerForm.controls).forEach((key) => {
        const control = this.createControllerServerForm.get(key);
        if (control && control.invalid) {
          console.log(`Field ${key} is invalid:`, control.errors);
        }
        control?.markAsTouched();
      });
    }
  }

  private collectValidationErrors(): void {
    this.showErrorMessages = [];

    Object.keys(this.createControllerServerForm.controls).forEach(
      (fieldName) => {
        const control = this.createControllerServerForm.get(fieldName);

        if (control && control.invalid) {
          const fieldLabel = this.getFieldLabel(fieldName);

          if (control.errors) {
            if (control.errors['required']) {
              this.showErrorMessages.push(`${fieldLabel} est obligatoire`);
            }
            if (control.errors['minlength']) {
              this.showErrorMessages.push(
                `${fieldLabel} doit contenir au moins ${control.errors['minlength'].requiredLength} caractères`
              );
            }
            if (control.errors['min']) {
              this.showErrorMessages.push(
                `${fieldLabel} doit être supérieur à ${
                  control.errors['min'].min - 1
                }`
              );
            }
            if (control.errors['max']) {
              this.showErrorMessages.push(
                `${fieldLabel} doit être inférieur à ${
                  control.errors['max'].max + 1
                }`
              );
            }
            if (control.errors['pattern']) {
              this.showErrorMessages.push(`${fieldLabel} n'est pas valide`);
            }
            if (control.errors['email']) {
              this.showErrorMessages.push(
                `${fieldLabel} doit être une adresse email valide`
              );
            }
          }
        }
      }
    );

    console.log('Collected validation errors:', this.showErrorMessages);
  }

  // NEW METHOD: Update controller relations in edit mode
  private updateControllerRelations(controllerServerId: string): void {
    console.log('Updating controller relations for:', controllerServerId);
    console.log('Current relations:', this.currentControllerRelations);
    console.log('Selected controller IDs:', this.selectedControllerIds);

    // Get currently associated controller IDs
    const currentControllerIds = this.currentControllerRelations.map(
      (r) => r.IdController
    );

    // Find relations to remove (existed before but not selected now)
    const relationsToRemove = this.currentControllerRelations.filter(
      (relation) => !this.selectedControllerIds.includes(relation.IdController)
    );

    // Find new relations to add (selected now but didn't exist before)
    const newControllerIds = this.selectedControllerIds.filter(
      (controllerId) => !currentControllerIds.includes(controllerId)
    );

    console.log('Relations to remove:', relationsToRemove);
    console.log('New controller IDs to add:', newControllerIds);

    // Create arrays of observables
    const deleteObservables = relationsToRemove.map((relation) =>
      this.controllerServerControllerService.delete(relation.Id || '')
    );

    const createObservables = newControllerIds.map((controllerId) => {
      const relationData: Partial<ControllerServerController> = {
        IdController: controllerId,
        IdControllerServeur: controllerServerId,
      };
      return this.controllerServerControllerService.post('', relationData);
    });

    // Combine all operations
    const allOperations = [...deleteObservables, ...createObservables];

    if (allOperations.length === 0) {
      // No changes needed
      console.log('No controller relation changes needed');
      this.controllerServerUpdated.emit(this.controllerServer!);
      this.resetForm();
      return;
    }

    // Execute all operations
    forkJoin(allOperations).subscribe({
      next: (results: any[]) => {
        console.log('Controller relations updated successfully:', results);
        this.controllerServerUpdated.emit(this.controllerServer!);
        this.resetForm();
      },
      error: (error: any) => {
        console.error('Error updating controller relations:', error);
        this.isSubmitting = false;
        alert(
          'Contrôleur mis à jour mais erreur lors de la mise à jour des relations'
        );
      },
    });
  }

  // UPDATED METHOD: Enhanced create controller relations
  private createControllerRelations(controllerServerId: string): void {
    if (this.selectedControllerIds.length === 0) {
      // No controller selections, just emit success and reset
      this.controllerServerCreated.emit({
        Id: controllerServerId,
      } as ControllerServeur);
      this.resetForm();
      return;
    }

    const relationPromises = this.selectedControllerIds.map((controllerId) => {
      const relationData: Partial<ControllerServerController> = {
        IdController: controllerId,
        IdControllerServeur: controllerServerId,
      };
      return this.controllerServerControllerService.post('', relationData);
    });

    forkJoin(relationPromises).subscribe({
      next: (relations: any[]) => {
        console.log('Controller relations created successfully:', relations);
        this.controllerServerCreated.emit({
          Id: controllerServerId,
        } as ControllerServeur);
        this.resetForm();
      },
      error: (error: any) => {
        console.error('Error creating controller relations:', error);
        this.isSubmitting = false;
        alert(
          "Contrôleur créé mais erreur lors de l'association aux contrôleurs"
        );
      },
    });
  }

  // onCancel(): void {
  //   this.resetForm();
  // }

  // private resetForm(): void {
  //   this.selectedControllerIds = [];
  //   this.currentControllerRelations = [];
  //   this.initializeForm(); // Remove the reset() call
  //   this.isSubmitting = false;
  //   this.formClosed.emit();
  // }

  private resetForm(): void {
    this.selectedControllerIds = [];
    this.currentControllerRelations = [];
    this.showErrorMessages = []; // Clear error messages
    this.hasTriedSubmit = false; // Reset submit attempt flag
    this.initializeForm();
    this.isSubmitting = false;
    this.formClosed.emit();
  }

  // NEW: Method to clear errors when user starts typing
  onFieldChange(fieldName: string): void {
    if (this.hasTriedSubmit && this.showErrorMessages.length > 0) {
      const control = this.createControllerServerForm.get(fieldName);
      if (control && control.valid) {
        // Remove error messages for this field
        const fieldLabel = this.getFieldLabel(fieldName);
        this.showErrorMessages = this.showErrorMessages.filter(
          (error) => !error.includes(fieldLabel)
        );
      }
    }
  }

  // UPDATED: Cancel method to clear error messages
  onCancel(): void {
    this.showErrorMessages = [];
    this.hasTriedSubmit = false;
    this.resetForm();
  }

  // Helper method to check if a field has errors
  hasError(fieldName: string): boolean {
    const field = this.createControllerServerForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  // Helper method to get error message
  getErrorMessage(fieldName: string): string {
    const field = this.createControllerServerForm.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors['required']) {
        return `${this.getFieldLabel(fieldName)} est obligatoire`;
      }
      if (field.errors['minLength']) {
        return `${this.getFieldLabel(fieldName)} doit contenir au moins ${
          field.errors['minLength'].requiredLength
        } caractères`;
      }
      if (field.errors['min']) {
        return `${this.getFieldLabel(fieldName)} doit être supérieur à ${
          field.errors['min'].min - 1
        }`;
      }
      if (field.errors['max']) {
        return `${this.getFieldLabel(fieldName)} doit être inférieur à ${
          field.errors['max'].max + 1
        }`;
      }
      if (field.errors['pattern']) {
        return `${this.getFieldLabel(fieldName)} n'est pas valide`;
      }
    }
    return '';
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      name: 'Le nom',
      maxSensors: 'Le nombre maximum de capteurs',
      maxControllers: 'Le nombre maximum de contrôleurs',
      commercialCondition: 'Les conditions commerciales',
      triggerType: 'Le type de déclencheur',
      geographicZone: 'La zone géographique',
      status: 'Le statut',
      idlicence: 'La licence',
      ipAddress: "L'adresse IP",
      port: 'Le port',
    };
    return labels[fieldName] || fieldName;
  }

  get formTitle(): string {
    return this.isEditMode
      ? 'Modifier le Contrôleur Serveur'
      : 'Ajouter un Contrôleur Serveur';
  }
}
