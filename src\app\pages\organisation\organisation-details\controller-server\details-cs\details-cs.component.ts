import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControllerServeur } from '@app/core/models/controllerServeur';
import { Licence } from '@app/core/models/licence';
import { ControllerServerViewData } from '@app/shared/models/ControllerServerViewData';

@Component({
  selector: 'app-details-cs',
  imports: [CommonModule],
  templateUrl: './details-cs.component.html',
  styleUrl: './details-cs.component.css',
})
export class DetailsCsComponent implements OnInit, OnChanges {
  @Input() controllerServer: ControllerServerViewData | null = null;
  @Output() detailsClosed = new EventEmitter<void>();

  selectedLicenceName: string = '';

  constructor() {}

  ngOnInit(): void {
    this.updateLicenceName();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['controllerServer'] || changes['licences']) {
      this.updateLicenceName();
    }
  }

  private updateLicenceName(): void {
    this.selectedLicenceName =
      this.controllerServer?.LicenceName ?? '';
  }

  onClose(): void {
    this.detailsClosed.emit();
  }

  // Helper methods for display
  getStatusDisplay(status: string): string {
    const statusMap: { [key: string]: string } = {
      Active: 'Actif',
      Inactive: 'Inactif',
      Maintenance: 'Maintenance',
      Retired: 'Retiré',
    };
    return statusMap[status] || status;
  }

  getStatusClass(status: string): string {
    const statusClasses: { [key: string]: string } = {
      Active: 'status-active',
      Inactive: 'status-inactive',
      Maintenance: 'status-maintenance',
      Retired: 'status-retired',
    };
    return statusClasses[status] || 'status-default';
  }

  formatDate(dateString: string): string {
    if (!dateString) return 'Non disponible';
    try {
      return new Date(dateString).toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return dateString;
    }
  }

  // Helper to check if a field has content
  hasContent(value: any): boolean {
    return value !== null && value !== undefined && value !== '';
  }
}
