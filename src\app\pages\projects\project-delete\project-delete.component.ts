// Frontend: src/app/pages/projects/project-delete/project-delete.component.ts
import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-project-delete',
  templateUrl: './project-delete.component.html',
  styleUrls: ['./project-delete.component.css'],
  standalone: true,
  imports: [CommonModule, MatButtonModule, MatIconModule]
})
export class ProjectDeleteComponent {
  projectId: string | null = null;
  loading = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private http: HttpClient,
    private snackBar: MatSnackBar
  ) {
    this.projectId = this.route.snapshot.paramMap.get('id');
  }

  confirmDelete(): void {
    if (!this.projectId) {
      this.snackBar.open('Invalid project ID', 'Close', { duration: 3000 });
      return;
    }
    this.loading = true;
    this.http.delete(`http://localhost:5256/api/projects/${this.projectId}`).subscribe({
      next: () => {
        this.snackBar.open('Project deleted successfully!', 'Close', { duration: 3000 });
        this.router.navigate(['/projects']);
        this.loading = false;
      },
      error: () => {
        this.snackBar.open('Error deleting project', 'Close', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  cancel(): void {
    this.router.navigate(['/projects']);
  }
}