/* ===== CONTAINER LAYOUT ===== */
.dialog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 90vh;
  width: 100%;
  min-height: 70vh;
  overflow: hidden;
}

.dialog-header {
  flex-shrink: 0;
  padding: 1.5rem 2rem 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: var(--surface);
  border-bottom: 1px solid var(--card-border);
  min-height: 80px;
}

.dialog-content {
  flex-grow: 1;
  overflow-y: auto;
  padding: 1rem 1.5rem;
  min-height: 0;
}

/* ===== TYPOGRAPHY ===== */
h1 {
  color: var(--primary);
  font-size: 1.875rem;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding-right: 3rem;
  line-height: 1.3;
}

h2 {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

h3 {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.125rem;
  margin-bottom: 0.75rem;
}

/* ===== STEP PROGRESS SYSTEM ===== */
.step-progress-container {
  margin-bottom: 2rem;
}

.step-progress-bar {
  width: 100%;
  height: 6px;
  background: var(--card-border);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.step-progress-fill {
  height: 100%;
  background: var(--primary);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* ===== STEP INDICATORS ===== */
.step-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  cursor: default;
}

.step-indicator.clickable {
  cursor: pointer;
}

.step-number {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: var(--card-border);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
  transition: var(--transition);
  border: 2px solid transparent;
}

.step-indicator.active .step-number {
  background: var(--primary);
  color: white;
}

.step-indicator.completed .step-number {
  background: var(--success);
  color: white;
}

.step-check {
  font-size: 1.25rem !important;
  width: 1.25rem !important;
  height: 1.25rem !important;
}

.step-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 500;
  text-align: center;
}

.step-indicator.active .step-label,
.step-indicator.completed .step-label {
  color: var(--primary);
  font-weight: 600;
}

/* ===== STEP SECTIONS ===== */
.step-section {
  border-radius: 0.75rem;
  border: 2px solid transparent;
  background: var(--surface);
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.step-section.step-current {
  border-color: var(--primary);
  box-shadow: var(--shadow-md);
}

.step-section.step-completed {
  border-color: var(--success);
}

.step-section.step-disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* ===== STEP HEADERS ===== */
.step-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--card-border);
  margin-bottom: 0;
}

.step-header.clickable {
  cursor: pointer;
}

.step-number-badge {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: var(--card-border);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
  flex-shrink: 0;
  border: 2px solid transparent;
}

.step-number-badge.active {
  background: var(--primary);
  color: white;
}

.step-number-badge.completed {
  background: var(--success);
  color: white;
}

.step-number-badge.disabled {
  opacity: 0.6;
}

.step-check-small {
  font-size: 1rem !important;
  width: 1rem !important;
  height: 1rem !important;
}

.step-progress-indicator {
  background: rgba(34, 197, 94, 0.1);
  color: var(--success);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-weight: 600;
  font-size: 0.75rem;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

/* ===== STEP CONTENT ===== */
.step-content {
  padding: 1.5rem;
  transition: all 0.3s ease;
  overflow: hidden;
  opacity: 1;
  max-height: none;
}

.step-content.collapsed {
  max-height: 0;
  padding: 0 1.5rem;
  opacity: 0;
  pointer-events: none;
}

.step-content.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* ===== DEVICE MAPPING CARDS ===== */
.device-mapping-card {
  transition: var(--transition);
  border: 2px solid var(--card-border);
  background: var(--surface);
  border-radius: 0.75rem;
  padding: 1rem;
}

.device-mapping-card.mapping-completed {
  border-color: var(--success);
  background: rgba(34, 197, 94, 0.02);
}

.mapping-success {
  background: rgba(34, 197, 94, 0.05);
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-radius: 0.5rem;
  padding: 0.75rem;
}

/* ===== PREVIEW CARDS ===== */
.rule-preview-card {
  background: rgba(59, 130, 246, 0.05);
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 0.75rem;
  padding: 1rem;
}

.preview-card {
  background: var(--surface);
  border: 2px solid var(--card-border);
  border-radius: 0.75rem;
  padding: 1rem;
}

.summary-card {
  background: rgba(59, 130, 246, 0.05);
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 0.75rem;
  padding: 1rem;
}

.topics-display {
  min-height: 50px;
  padding: 0.5rem;
  border: 1px solid var(--card-border);
  border-radius: 0.5rem;
  background: var(--surface);
}

.topic-tag {
  display: inline-block;
  background: rgba(47, 125, 51, 0.1);
  color: var(--primary);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  margin-right: 0.5rem;
  margin-bottom: 0.25rem;
  border: 1px solid rgba(47, 125, 51, 0.2);
  font-weight: 500;
  font-size: 0.875rem;
}

/* ===== FORM ELEMENTS ===== */
.custom-checkbox {
  width: 1.25rem;
  height: 1.25rem;
  background: var(--surface);
  border: 2px solid var(--card-border);
  border-radius: 0.25rem;
  appearance: none;
  cursor: pointer;
  position: relative;
  transition: var(--transition);
}

.custom-checkbox:checked {
  background: var(--primary);
  border-color: var(--primary);
}

.custom-checkbox:checked::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 0.875rem;
}

input[type="text"],
input[type="time"],
select {
  width: 100%;
  padding: 0.75rem;
  background: var(--surface);
  border: 2px solid var(--card-border);
  border-radius: 0.5rem;
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: var(--transition);
}

input[type="text"]:focus,
input[type="time"]:focus,
select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(47, 125, 51, 0.1);
}

input[type="text"]:disabled,
input[type="time"]:disabled,
select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #f5f5f5;
}

/* ===== JSON PREVIEW ===== */
.json-preview {
  background: var(--text-primary) !important;
  color: var(--success) !important;
  border: 2px solid var(--card-border);
  border-radius: 0.5rem;
  padding: 1rem;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  line-height: 1.6;
  overflow: auto;
}

/* ===== BUTTONS ===== */
button {
  padding: 0.75rem 1.25rem;
  border-radius: 0.5rem;
  border: none;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--primary);
  color: white;
  min-height: 44px; /* Better touch target */
}

button:hover:not(:disabled) {
  background: #1b5e20;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.save-button {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: white !important;
  font-weight: 600;
  min-width: 140px;
}

.save-button:hover:not(:disabled) {
  background: linear-gradient(45deg,#81c784, var(--primary)) !important;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.save-button:disabled {
  background: var(--grey-light) !important;
  color: var(--text-primary) !important;
}

.cancel-button {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: white !important;
}

.cancel-button:hover:not(:disabled) {
  background: linear-gradient(45deg, #81c784, var(--primary)) !important;
}

.download-button {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: white !important;
  border-color: var(--info) !important;
}

.download-button:hover:not(:disabled) {
  background: linear-gradient(45deg, #81c784, var(--primary)) !important;
}

.next-button {
  background: var(--primary);
  color: white;
  border: 2px solid var(--primary);
}

.next-button:hover:not(:disabled) {
  background: #1b5e20;
}

.close-button {
  background: transparent !important;
  border: none !important;
  padding: 8px !important;
  border-radius: 50% !important;
  width: 40px !important;
  height: 40px !important;
  min-width: 40px !important;
  min-height: 40px !important;
  color: var(--grey-light) !important;
}

.close-button:hover:not(:disabled) {
  background: var(--card-border) !important;
  color: var(--danger) !important;
}

/* ===== DIALOG FOOTER - FIXED ===== */
.dialog-footer {
  flex-shrink: 0;
  padding: 1.25rem 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--surface);
  border-top: 1px solid var(--card-border);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  min-height: 80px;
}

.footer-left {
  display: flex;
  color: var(--grey-light);
  justify-content: flex-start;
  flex: 1;
}

.footer-center {
  display: flex;
  gap: 0.875rem;
  color: var(--primary);
  justify-content: center;
  align-items: center;
  flex: 0 0 auto;
}

.footer-right {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  flex: 1;
}

/* ===== ERROR CONTAINER ===== */
.error-container {
  background: rgba(239, 68, 68, 0.05);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 0.5rem;
  padding: 1rem;
}

/* ===== DIALOG STYLING ===== */
::ng-deep .mat-dialog-container {
  width: 55vw !important;
  max-width: 900px !important;
  min-width: 650px !important;
  max-height: 90vh !important;
  padding: 0 !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

::ng-deep .mat-dialog-content {
  padding: 0 !important;
  margin: 0 !important;
  max-height: none !important;
  overflow: visible !important;
}

::ng-deep .mat-dialog-actions {
  padding: 0 !important;
  margin: 0 !important;
  min-height: auto !important;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  ::ng-deep .mat-dialog-container {
    width: 65vw !important;
  }
}

@media (max-width: 900px) {
  ::ng-deep .mat-dialog-container {
    width: 85vw !important;
    min-width: 400px !important;
  }

  .step-indicators {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .step-number {
    width: 2.5rem;
    height: 2.5rem;
  }

  .step-label {
    font-size: 0.7rem;
  }

  .dialog-footer {
    padding: 1rem;
  }

  .footer-center {
    gap: 0.5rem;
  }

  .footer-center button {
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .dialog-header,
  .dialog-content {
    padding: 0.75rem 1rem;
  }

  .dialog-footer {
    padding: 0.875rem 1rem;
    flex-direction: column;
    gap: 0.75rem;
    min-height: auto;
  }

  .footer-center {
    width: 100%;
    justify-content: space-between;
    gap: 0.5rem;
  }

  .footer-center button {
    flex: 1;
    min-width: 0;
    font-size: 0.75rem;
    padding: 0.75rem 0.5rem;
  }

  h1 {
    font-size: 1.5rem;
  }

  .step-number {
    width: 2rem;
    height: 2rem;
    font-size: 0.875rem;
  }

  .step-label {
    font-size: 0.65rem;
  }

  .step-content {
    padding: 1rem;
  }

  .device-mapping-card {
    padding: 0.75rem;
  }

  .loading-container {
    padding: 2rem;
    margin: 1rem;
  }
}

@media (max-width: 600px) {
  ::ng-deep .mat-dialog-container {
    width: 95vw !important;
    min-width: 320px !important;
  }

  .dialog-header {
    padding: 1rem;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
    min-height: 60px;
  }

  .dialog-header h1 {
    font-size: 1.25rem;
    padding-right: 2.5rem;
  }

  .dialog-footer {
    padding: 0.75rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .footer-center {
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
  }

  .footer-center button {
    width: 100%;
    justify-content: center;
  }

  .step-indicators {
    grid-template-columns: repeat(5, 1fr);
    gap: 0.25rem;
  }

  .step-number {
    width: 1.75rem;
    height: 1.75rem;
    font-size: 0.75rem;
  }

  .step-label {
    font-size: 0.6rem;
  }
}

/* ===== UTILITY CLASSES ===== */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-1 {
  gap: 0.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}

.mt-6 {
  margin-top: 1.5rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-2 {
  margin-top: 0.5rem;
}

.p-1 {
  padding: 0.25rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-6 {
  padding: 1.5rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .md\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
}

/* Text and layout utilities */
.block {
  display: block;
}
.text-sm {
  font-size: 0.875rem;
}
.text-xs {
  font-size: 0.75rem;
}
.text-lg {
  font-size: 1.125rem;
}
.text-2xl {
  font-size: 1.5rem;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.font-bold {
  font-weight: 700;
}
.text-center {
  text-align: center;
}

.text-gray-700,
.text-gray-800 {
  color: var(--text-primary);
}
.text-gray-600 {
  color: var(--primary);
}
.text-gray-500 {
  color: var(--grey-light);
}
.text-blue-600 {
  color: var(--primary);
}
.text-green-600 {
  color: var(--success);
}
.text-orange-600 {
  color: var(--warning);
}
.text-red-600 {
  color: var(--danger);
}

.w-full {
  width: 100%;
}
.w-3 {
  width: 0.75rem;
}
.w-4 {
  width: 1rem;
}
.w-5 {
  width: 1.25rem;
}
.w-6 {
  width: 1.5rem;
}
.w-24 {
  width: 6rem;
}

.h-3 {
  height: 0.75rem;
}
.h-4 {
  height: 1rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-12 {
  height: 3rem;
}

.min-h-\[40px\] {
  min-height: 40px;
}
.min-h-\[50px\] {
  min-height: 50px;
}
.max-h-48 {
  max-height: 12rem;
}
.max-h-64 {
  max-height: 16rem;
}

.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.items-end {
  align-items: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-end {
  justify-content: flex-end;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}
.space-y-1 > * + * {
  margin-top: 0.25rem;
}
.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.bg-gray-50 {
  background: #f9fafb;
  border: 1px solid var(--card-border);
  border-radius: 0.5rem;
}

.bg-white {
  background: var(--surface);
}
.bg-blue-50 {
  background: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.2);
}
.bg-green-50 {
  background: rgba(34, 197, 94, 0.05);
  border-color: rgba(34, 197, 94, 0.2);
}
.bg-yellow-50 {
  background: rgba(245, 158, 11, 0.05);
  border-color: rgba(245, 158, 11, 0.2);
}
.bg-red-50 {
  background: rgba(239, 68, 68, 0.05);
  border-color: rgba(239, 68, 68, 0.2);
}

.border {
  border-width: 1px;
}
.border-2 {
  border-width: 2px;
}
.border-gray-200 {
  border-color: var(--card-border);
}
.border-gray-300 {
  border-color: var(--grey-light);
}
.rounded-md {
  border-radius: 0.5rem;
}
.rounded-full {
  border-radius: 9999px;
}

.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.inset-0 {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.z-50 {
  z-index: 50;
}

.opacity-50 {
  opacity: 0.5;
}
.pointer-events-none {
  pointer-events: none;
}
.pointer-events-auto {
  pointer-events: auto;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-hidden {
  overflow: hidden;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Focus styles for accessibility */
button:focus-visible,
input:focus-visible,
select:focus-visible,
.step-indicator:focus-visible,
.step-header.clickable:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.mapping-progress {
  padding: 0.125rem 0.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 0.75rem;
  display: inline-block;
}

.mapping-progress.text-green-600 {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.mapping-progress.text-orange-600 {
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.mapping-progress.text-gray-500 {
  background: rgba(156, 163, 175, 0.1);
  border: 1px solid rgba(156, 163, 175, 0.2);
}

/* Summary section */
.summary-section {
  margin-bottom: 1.5rem;
}

/* Print styles */
@media print {
  .dialog-header,
  .dialog-footer,
  .loading-overlay-enhanced,
  .close-button {
    display: none !important;
  }

  .dialog-content {
    overflow: visible !important;
    height: auto !important;
    max-height: none !important;
    padding: 0 !important;
  }

  .step-section {
    page-break-inside: avoid;
    border: 1px solid #ccc !important;
    margin-bottom: 1rem !important;
  }

  .json-preview {
    background: white !important;
    color: black !important;
    border: 1px solid black !important;
  }

  ::ng-deep .mat-dialog-container {
    width: 100% !important;
    max-width: none !important;
    min-width: auto !important;
    max-height: none !important;
    box-shadow: none !important;
  }
}
