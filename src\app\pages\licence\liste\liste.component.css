* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #f0f8f0 0%, #e8f5e8 100%);
  color: #2d4a2d;
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #22c55e;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #16a34a;
  margin: 0;
}

.page-subtitle {
  font-size: 1rem;
  color: #6b7280;
  margin-top: 4px;
}

.add-licence-btn {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.add-licence-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(34, 197, 94, 0.4);
}

.add-licence-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.licenses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 30px;
}

.license-card {
  background: white;
  border-radius: 16px;
  padding: 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.license-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

.license-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
}

.license-header {
  padding: 20px 20px 16px;
  border-bottom: 1px solid #f1f5f9;
}

.license-info {
  margin-bottom: 16px;
}

.license-name {
  font-size: 1.375rem;
  font-weight: 700;
  color: #16a34a;
  margin: 0 0 8px 0;
}

.license-description {
  color: #64748b;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
}

.license-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.action-btn {
  background: none;
  border: none;
  padding: 8px 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1.1rem;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  transform: scale(1.1);
}

.action-btn.view:hover {
  background: #dbeafe;
  color: #2563eb;
}

.action-btn.edit:hover {
  background: #fef3c7;
  color: #d97706;
}

.action-btn.delete:hover {
  background: #fee2e2;
  color: #dc2626;
}

.action-btn.refresh:hover {
  background: #ecfdf5;
  color: #16a34a;
}

.license-content {
  padding: 16px 20px;
}

.license-options {
  margin-bottom: 16px;
}

.option-type-select {
  font-weight: 600;
  color: #2563eb;
}

.option-type-select.max-controllers {
  color: #d97706;
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.options-label {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.options-count {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #16a34a;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  border: 1px solid #86efac;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-tag {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
}

.option-tag:hover {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  border-color: #86efac;
}

.option-name {
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
}

.option-price {
  font-weight: 600;
  color: #16a34a;
  font-size: 0.9rem;
}

.no-options {
  text-align: center;
  padding: 20px;
  color: #9ca3af;
  font-style: italic;
  font-size: 0.9rem;
}

.no-options-icon {
  font-size: 1.5rem;
  margin-bottom: 8px;
  display: block;
}

.license-footer {
  padding: 16px 20px;
  background: #f8fafc;
  border-top: 1px solid #f1f5f9;
}

.license-stats {
  display: flex;
  gap: 20px;
  font-size: 0.85rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  color: #6b7280;
  font-weight: 500;
}

.stat-value {
  color: #16a34a;
  font-weight: 600;
}

.empty-state {
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.empty-state-icon {
  font-size: 5rem;
  margin-bottom: 24px;
  opacity: 0.4;
}

.empty-state-title {
  font-size: 1.5rem;
  color: #6b7280;
  margin-bottom: 12px;
  font-weight: 600;
}

.empty-state-text {
  color: #9ca3af;
  font-size: 1rem;
  margin-bottom: 32px;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.empty-state-btn {
  padding: 16px 32px;
  font-size: 1.1rem;
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-container {
  background: white;
  border-radius: 16px;
  max-width: 700px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfccb 100%);
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #16a34a;
  margin: 0;
}

.modal-body {
  padding: 24px;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 0.95rem;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
  background: white;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #22c55e;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.readonly {
  background: #f9fafb;
  color: #6b7280;
}

.options-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.options-counter {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #16a34a;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 600;
  border: 1px solid #86efac;
}

.options-container {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.option-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.option-item:last-child {
  margin-bottom: 0;
}

.option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.option-header h4 {
  color: #16a34a;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.option-fields select.form-input {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg fill='none' stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
  padding-right: 2.5rem;
}

.option-fields select.form-input:focus {
  border-color: #22c55e;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.option-fields input[type="number"] {
  -moz-appearance: textfield;
}

.option-fields input[type="number"]::-webkit-inner-spin-button,
.option-fields input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.add-option-section {
  padding: 16px;
  text-align: center;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  margin-top: 16px;
}

.add-option-btn {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-option-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.no-options-placeholder {
  text-align: center;
  padding: 40px 20px;
  color: #9ca3af;
  font-style: italic;
}

.placeholder-icon {
  font-size: 2rem;
  margin-bottom: 8px;
  display: block;
  opacity: 0.5;
}

.placeholder-text {
  font-size: 0.9rem;
}

.btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-cancel {
  background: #f9fafb;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.btn-cancel:hover {
  background: #f3f4f6;
}

.btn-remove {
  background: #fee2e2;
  color: #dc2626;
  padding: 6px 12px;
  font-size: 1.2rem;
  font-weight: bold;
  border-radius: 6px;
}

.btn-remove:hover {
  background: #fecaca;
}

.btn-danger {
  background: #dc2626;
  color: white;
}

.btn-danger:hover {
  background: #b91c1c;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.delete-modal {
  max-width: 500px;
}

.delete-content {
  padding: 24px;
}

.delete-message {
  font-size: 1.1rem;
  color: #374151;
  margin-bottom: 20px;
}

.delete-details {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.delete-details p {
  margin-bottom: 8px;
  font-size: 0.95rem;
}

.delete-details p:last-child {
  margin-bottom: 0;
}

.delete-warning {
  color: #dc2626;
  font-weight: 600;
  font-size: 0.95rem;
}

.text-red-600 {
  color: #dc2626;
}

.delete-icon {
  margin-right: 8px;
}

@media (max-width: 768px) {
  .licenses-grid {
    grid-template-columns: 1fr;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .modal-container {
    margin: 10px;
  }

  .option-fields {
    grid-template-columns: 1fr;
  }

  .license-stats {
    flex-direction: column;
    gap: 8px;
  }
}
