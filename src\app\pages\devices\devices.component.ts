import { Compo<PERSON>, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import {
  Zigbee2MqttService,
  ZigbeeDevice,
  DeviceState,
} from '../../core/services/mqtt.service';
import { MatSliderChange } from '@angular/material/slider';
import { CommonModule, DatePipe } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatSliderModule } from '@angular/material/slider';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatDividerModule } from '@angular/material/divider';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatTableModule } from '@angular/material/table'; // Import MatTableModule
import { MatButtonToggleModule } from '@angular/material/button-toggle'; // Import MatButtonToggleModule
import {
  Device,
  SensorsBackEndDataService,
  SimpleLocal,
  SimpleSite,
} from '@app/core/sensors-back-end-data.service';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { Client } from '@app/core/models/client';
import { Site } from '@app/core/models/site';
import { MultiTopicListenerService } from '@app/core/services/multi-topic-listener-mqtt.service';
import { BehaviorSubject } from 'rxjs';
import { DisplayPopUpComponent } from './display-pop-up/display-pop-up.component'
import {
  NgToastComponent,
  NgToastService,
  TOAST_POSITIONS,
} from 'ng-angular-popup';

// Define device type categories
export enum DeviceCategory {
  LIGHT = 'light',
  SWITCH = 'switch',
  SENSOR = 'sensor',
  MOTION = 'motion',
  DOOR = 'door',
  PLUG = 'plug',
  CLIMATE = 'climate',
  UNKNOWN = 'unknown',
}

// Define data types for sensor values
interface SensorData {
  key: string;
  value: any;
  unit?: string;
  icon: string;
  label: string;
  type: 'number' | 'boolean' | 'string' | 'object';
  isControl?: boolean;
  min?: number;
  max?: number;
  vendor?: string; // Optional vendor field for future use
  model?: string; // Optional model field for future use
}

@Component({
  selector: 'app-devices',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatIconModule,
    MatChipsModule,
    MatSlideToggleModule,
    MatSliderModule,
    MatButtonModule,
    MatMenuModule,
    MatProgressBarModule,
    MatDividerModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatTooltipModule,
    MatTableModule,
    MatButtonToggleModule,
    DisplayPopUpComponent,
    NgToastComponent
  ],
  templateUrl: './devices.component.html',
  styleUrls: ['./devices.component.css'],
})
export class DeviceControlComponent implements OnInit, OnDestroy {
  // Search and filter properties
  searchTerm: string = '';
  selectedCategory: string = 'all';
  selectedStatus: string = 'all';
  filteredDevices: any[] = [];
  filtredDevicesBackEnd: any[] = [];
  devices: any[] = [];
  devicesBackEnd: any[] = [];
  countDevicesSynchronised$ = new BehaviorSubject<number>(0);
  bridgeState: string = 'offline';
  private subscriptions: Subscription[] = [];
  readonly DeviceCategory = DeviceCategory;
  currentView: 'grid' | 'table' = 'grid'; // Default view
  displayedColumns: string[] = [
    'name',
    'category',
    'vendorModel',
    'lastSeen',
    'controls',
  ];
  buttonon: boolean = false;
  buttonon2: boolean = true;
  DateSynchronisation: string = '';
  AllClients: Client[] = [];
  selectedClientId: string = '';
  Sites: SimpleSite[] = [];
  selectedSiteId: string = '';
  locaux: SimpleLocal[] = [];
  selectedLocalId: string = '';
  private sub?: Subscription;
  topics: string[] = [];
  devices$ = new BehaviorSubject<any[]>([]);
  countActifDevices: number = 0;
  countInActifDevices: number = 0;
  countEnVeilleDevices: number = 0;
  countJamisSynchroniseDevices: number = 0;
  showPopup = false;
  selectedDevice: any = null;
  TOAST_POSITIONS = TOAST_POSITIONS;
  currentPage: number = 1;
  totalPages: number = 1; 

  get deviceStats() {
    return {
      total: this.devices.length,
      lights: this.devices.filter(
        (d) => this.getDeviceCategory(d) === DeviceCategory.LIGHT
      ).length,
      sensors: this.devices.filter(
        (d) => this.getDeviceCategory(d) === DeviceCategory.SENSOR
      ).length,
      switches: this.devices.filter(
        (d) => this.getDeviceCategory(d) === DeviceCategory.SWITCH
      ).length,
      plugs: this.devices.filter(
        (d) => this.getDeviceCategory(d) === DeviceCategory.PLUG
      ).length,
      motion: this.devices.filter(
        (d) => this.getDeviceCategory(d) === DeviceCategory.MOTION
      ).length,
      door: this.devices.filter(
        (d) => this.getDeviceCategory(d) === DeviceCategory.DOOR
      ).length,
      climate: this.devices.filter(
        (d) => this.getDeviceCategory(d) === DeviceCategory.CLIMATE
      ).length,
      others: this.devices.filter(
        (d) => this.getDeviceCategory(d) === DeviceCategory.UNKNOWN
      ).length,
    };
  }

  get deviceStatsBackEnd() {
    return {
      total: this.devicesBackEnd.length,
      lights: this.devicesBackEnd.filter(
        (d) => this.getDeviceCategory(d) === DeviceCategory.LIGHT
      ).length,
      sensors: this.devicesBackEnd.filter(
        (d) => this.getDeviceCategory(d) === DeviceCategory.SENSOR
      ).length,
      switches: this.devicesBackEnd.filter(
        (d) => this.getDeviceCategory(d) === DeviceCategory.SWITCH
      ).length,
      plugs: this.devicesBackEnd.filter(
        (d) => this.getDeviceCategory(d) === DeviceCategory.PLUG
      ).length,
      motion: this.devicesBackEnd.filter(
        (d) => this.getDeviceCategory(d) === DeviceCategory.MOTION
      ).length,
      door: this.devicesBackEnd.filter(
        (d) => this.getDeviceCategory(d) === DeviceCategory.DOOR
      ).length,
      climate: this.devicesBackEnd.filter(
        (d) => this.getDeviceCategory(d) === DeviceCategory.CLIMATE
      ).length,
      others: this.devicesBackEnd.filter(
        (d) => this.getDeviceCategory(d) === DeviceCategory.UNKNOWN
      ).length,
    };
  }

  // Mapping of data keys to display information (translated to French)
  private readonly dataTypeMap: { [key: string]: Partial<SensorData> } = {
    temperature: {
      icon: 'device_thermostat',
      label: 'Température',
      unit: '°C',
      type: 'number',
    },
    humidity: {
      icon: 'water_drop',
      label: 'Humidité',
      unit: '%',
      type: 'number',
    },

    pressure: { icon: 'speed', label: 'Pression', unit: 'hPa', type: 'number' },
    illuminance: {
      icon: 'light_mode',
      label: 'Illumination',
      unit: 'lux',
      type: 'number',
    },
    occupancy: {
      icon: 'directions_run',
      label: 'Mouvement',
      type: 'boolean',
    },
    contact: { icon: 'sensor_door', label: 'Contact', type: 'boolean' },
    state: {
      icon: 'power_settings_new',
      label: 'Alimentation',
      type: 'boolean',
      isControl: true,
    },
    brightness: {
      icon: 'brightness_medium',
      label: 'Luminosité',
      unit: '%',
      type: 'number',
      isControl: true,
    },
    battery: {
      icon: 'battery_full',
      label: 'Batterie',
      unit: '%',
      type: 'number',
    },
    linkquality: { icon: 'network_wifi', label: 'Signal', type: 'number' },


  };

  constructor(
    private sensorsService: SensorsBackEndDataService,
    private clientApiService: ClientApiService,
    private listener: MultiTopicListenerService,
    private toast: NgToastService
  ) {}
  rowDataFromBackend: any[] = [];

  ngOnInit(): void {
    this.loadClients();
    this.getAllCapteurs()
    // this.listener.listen('fa32efac-94f1-4173-9126-08ddbedf01c4/system_info').subscribe((msg) =>{
    //   console.log(msg);
    // })
  }

  subscribeToMultipleSensors(topics: string[]) {
    this.listener.listenMany(topics).subscribe((msg) => {
      this.devices = this.devices.map((device) => {
        if (device.Topic === msg.topic) {
          // Updated State
          const newState = msg.payload;
          console.log(msg.arrivedAt);
          const newLastSeen = msg.arrivedAt
            ? new Date(msg.arrivedAt).toLocaleString('fr-FR', {
                dateStyle: 'medium',
                timeStyle: 'medium',
              })
            : null;
          // Compute SensorData based on new state
          const sensorData = this.getSensorDataFromBack({
            ...device,
            State: newState,
            LastSeenOriginal: msg.arrivedAt,
            LastSeen: newLastSeen,
          });

          return {
            ...device,
            State: newState,
            SensorData: sensorData,
            LastSeenOriginal: msg.arrivedAt,
            LastSeen: newLastSeen,
          };
        }
        return device;
      });
      this.devices$.next(this.filteredDevices);
      this.applyFilters();
      console.log(this.devices);
      console.log(
        `[${msg.arrivedAt.toLocaleTimeString()}] ${msg.topic}`,
        msg.payload
      );
    });
  }

  getAllCapteurs(): void {
    this.getSensors('all','',{
      pageSize: 10,
      pageNumber: this.currentPage,
      searchTerm: this.searchTerm
      })
    console.log(this.filtredDevicesBackEnd);
  }

  injectionDataToRowData(devices: Device[]): void {
    const ONE_HOUR = 60 * 60 * 1000;
    const One_Day = 24 * ONE_HOUR;
    this.countActifDevices = 0;
    this.countEnVeilleDevices = 0;
    this.countInActifDevices = 0;
    this.countJamisSynchroniseDevices = 0;

    const now = Date.now();
    this.countDevicesSynchronised$.next(0); // Updates the value to 5
    this.devicesBackEnd = devices
      .map((device) => {
        try {
          const rowData: any = device.RowData ? JSON.parse(device.RowData) : {};
          const state = device.State ? JSON.parse(device.State) : null;
          if (device.Topic) {
            this.topics.push(device.Topic);
          }
          rowData.State = state;
          rowData.LastSeenOriginal = device.LastSeen;
          rowData.Topic = device.Topic; // keep raw timestamp
          rowData.Id = device.Id;
          rowData.DisplayName = device.DisplayName;
          rowData.LastSeen = device.LastSeen
            ? new Date(device.LastSeen).toLocaleString('fr-FR', {
                dateStyle: 'medium',
                timeStyle: 'medium',
              })
            : null;

          /* ---------- “Actif” flag & counter ---------- */
          const lastSeenDate = rowData.LastSeenOriginal
            ? new Date(rowData.LastSeenOriginal)
            : null;
          if (rowData.State != null) {
            if (lastSeenDate && now - lastSeenDate.getTime() < ONE_HOUR) {
              this.countDevicesSynchronised$.next(
                this.countDevicesSynchronised$.getValue() + 1
              );
              rowData.Actif = 'Actif';
              this.countActifDevices += 1;
            } else if (
              lastSeenDate &&
              now - lastSeenDate.getTime() >= ONE_HOUR &&
              now - lastSeenDate.getTime() <= One_Day
            ) {
              this.countDevicesSynchronised$.next(
                this.countDevicesSynchronised$.getValue() + 1
              );
              rowData.Actif = 'En Veille';
              this.countEnVeilleDevices += 1;
            } else {
              rowData.Actif = 'InActif';
              this.countInActifDevices += 1;
            }
          } else {
            rowData.Actif = 'Jamais Synchronisé';
            this.countJamisSynchroniseDevices += 1;
          }

          return rowData;
        } catch (e) {
          console.error('Failed to parse device:', device, e);
          return null;
        }
      })
      .filter((d) => d !== null);
  }

  clientOnChange(): void {
    this.locaux = [];
    this.selectedSiteId = '';

    if (this.selectedClientId) {
      // Load sites
      this.sensorsService.getSitesByClientId(this.selectedClientId).subscribe({
        next: (sites) => {
          this.Sites = sites;
        },
      });

      this.getSensors('client', this.selectedClientId, {
          pageSize: 100,
          pageNumber: this.currentPage,
          searchTerm: this.searchTerm,
        })
      
    } else {
      this.Sites = [];
      this.getAllCapteurs();
    }
  }

  siteOnChange(): void {
  if (this.selectedSiteId) {
    // Load locaux for selected site
    this.sensorsService.getlocauxBySiteId(this.selectedSiteId).subscribe({
      next: (locaux) => {
        this.locaux = locaux;
      },
    });
    this.getSensors('site', this.selectedSiteId, {
        pageSize: 10,
        pageNumber: this.currentPage,
        searchTerm: this.searchTerm,
      })
  } else {
    this.locaux = [];
    this.clientOnChange();
  }
}

localOnChange(): void {
  if (this.selectedLocalId) {
    this.getSensors('local', this.selectedLocalId, {
        pageSize: 10,
        pageNumber: this.currentPage,
        searchTerm: this.searchTerm,
      })
  } else {
    this.siteOnChange();
  }
}


  ngOnDestroy(): void {
    //this.subscriptions.forEach(sub => sub.unsubscribe());
    this.listener.stopAll();
  }

  loadClients(): void {
    this.clientApiService.getAll().subscribe({
      next: (clients) => {
        // Transform the API response to match your interface
        this.AllClients = clients;
        console.log('Clients loaded in devices:', this.AllClients);
      },
      error: (error) => {
        console.error('Error loading clients:', error);
      },
    });
  }

  // Filter methods
  applyFilters(): void {
    console.log(this.devices);
    this.filteredDevices = this.devices;
    this.devices$.next(this.filteredDevices);
    console.log('$devices ', this.devices$);
  }
  applyFiltersFromBackEnd(): void {
    this.filtredDevicesBackEnd = this.devicesBackEnd;
    this.DateSynchronisation = new Date().toLocaleString('fr-FR', {
      dateStyle: 'medium',
      timeStyle: 'medium',
    });
  }

  ChangeText(): void {
    this.buttonon = !this.buttonon;

    if (this.buttonon) {
      // this.zigbeeService.setBaseTopic('zigbee2mqtt');
      this.devices = this.devicesBackEnd.map((device) => ({
        ...device,
        State: null,
      }));
      console.log(this.topics);
      this.subscribeToMultipleSensors(this.topics);
      this.applyFilters();

      console.log(this.devices);
    } else {
      this.listener.stopAll();
      this.localOnChange();
    }

    // (These lines can stay where they are; they listen to the subjects,
    //  not directly to MQTT, so they’ll work after the switch.)
    // this.subscriptions.push(
    //   this.zigbeeService.devices$.subscribe(devices => {
    //     this.devices = devices;
    //     this.applyFilters();
    //   }),
    //   this.zigbeeService.bridgeState$.subscribe(state => this.bridgeState = state)
    // );
  }

  refreshDevices(): void {
    this.localOnChange();
  }

  onSearchChange(): void {
    this.localOnChange()
  }

  onCategoryChange(): void {
    this.applyFilters();
  }

  onStatusChange(): void {
    this.applyFilters();
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.applyFilters();
  }

  clearFilters(): void {
    // this.searchTerm = '';
    // this.selectedCategory = 'all';
    // this.selectedStatus = 'all';
    // this.applyFiltersFromBackEnd();
    // this.applyFilters();
    this.selectedClientId = '';
    this.selectedLocalId = '';
    this.selectedSiteId = '';
    this.getAllCapteurs();
  }

  // getDeviceStateFormBackEnd(device: SensorsBackEndDataService): DeviceState | undefined {
  //   return this.zigbeeService.getDeviceExtended(device);
  // }

  // getDeviceState(device: ZigbeeDevice): DeviceState | undefined {
  //   return this.zigbeeService.getDeviceExtended(device.networkAddress);
  // }
  //   getDeviceStateFormBackend(device: BackEndDevice): DeviceState | undefined {
  //   return this.sensorsService.getDeviceExtended(device.networkAddress);
  // }

  // isDeviceOnline(device: ZigbeeDevice): boolean {
  //   const state = this.getDeviceState(device);
  //   // Consider device online if last_seen is within the last 5 minutes (300,000 ms)
  //   return state?.last_seen
  //     ? Date.now() - new Date(state.last_seen).getTime() < 300000
  //     : false;
  // }

  trackByKey(index: number, item: SensorData): string {
    return item.key;
  }

  // isDeviceStateOn(device: ZigbeeDevice): boolean {
  //   const state = this.getDeviceState(device);
  //   if (!state) return false;
  //   // Check for both 'ON' string and true boolean
  //   return state.state === 'ON' || state.state === 'true';
  // }

  getDeviceCategory(device: any): DeviceCategory {
    const definition = device.definition;
    if (!definition) return DeviceCategory.UNKNOWN;

    const model = definition.model?.toLowerCase() || '';
    const description = definition.description?.toLowerCase() || '';
    const supports = definition.supports?.toLowerCase() || '';
    const features = device.supported_features?.join(' ').toLowerCase() || '';
    const type = device.type?.toLowerCase() || '';

    // if (type.includes('router') && (model.includes('plug') || description.includes('plug'))) {
    //   return DeviceCategory.PLUG; // Specifically handle router plugs
    // }
    if (
      type.includes('light') ||
      model.includes('bulb') ||
      description.includes('light')
    ) {
      return DeviceCategory.LIGHT;
    }
    // if (type.includes('switch') || model.includes('switch')) {
    //   return DeviceCategory.SWITCH;
    // }
    // if (type.includes('plug') || model.includes('plug')) {
    //   return DeviceCategory.PLUG;
    // }
    // if (type.includes('motion') || model.includes('motion') || description.includes('motion')) {
    //   return DeviceCategory.MOTION;
    // }
    // if (type.includes('door') || model.includes('door') || model.includes('contact')) {
    //   return DeviceCategory.DOOR;
    // }
    // if (type.includes('climate') || model.includes('temperature') || description.includes('temperature') ||
    //            model.includes('humidity') || description.includes('climate')) {
    //   return DeviceCategory.CLIMATE;
    // }
    if (
      type.includes('sensor') ||
      model.includes('sensor') ||
      description.includes('sensor')
    ) {
      return DeviceCategory.SENSOR;
    }

    // Fallback based on exposed features if type/model is generic
    if (features.includes('state') || features.includes('brightness')) {
      return DeviceCategory.LIGHT; // If it has state/brightness control, it's likely a light or controlled plug
    }
    if (
      features.includes('occupancy') ||
      features.includes('contact') ||
      features.includes('temperature') ||
      features.includes('humidity')
    ) {
      return DeviceCategory.SENSOR;
    }

    return DeviceCategory.UNKNOWN;
  }

  // getSensorData(device: ZigbeeDevice): SensorData[] {
  //   const state = this.getDeviceState(device);
  //   if (!state) return [];

  //   const sensorData: SensorData[] = [];

  //   // Skip meta fields that shouldn't be displayed as sensor data
  //   const skipFields = [
  //     'ieee_address',
  //     'friendly_name',
  //     'device',
  //     'update',
  //     'update_available',
  //     'linkquality',
  //     'last_seen',
  //     'elapsed',
  //   ];

  //   Object.entries(state).forEach(([key, value]) => {
  //     if (skipFields.includes(key) || value === null || value === undefined)
  //       return;

  //     const typeInfo = this.dataTypeMap[key] || {
  //       icon: this.getDefaultIcon(key, value),
  //       label: this.formatLabel(key),
  //       type: this.inferType(value),
  //     };

  //     sensorData.push({
  //       key,
  //       value,
  //       unit: typeInfo.unit,
  //       icon: typeInfo.icon!,
  //       label: typeInfo.label!,
  //       type: typeInfo.type!,
  //       isControl: typeInfo.isControl || false,
  //       min: typeInfo.min,
  //       max: typeInfo.max,
  //     });
  //   });

  //   // Sort data: controls first, then by importance
  //   return sensorData.sort((a, b) => {
  //     if (a.isControl && !b.isControl) return -1;
  //     if (!a.isControl && b.isControl) return 1;

  //     const importanceOrder = [
  //       'state',
  //       'brightness',
  //       'temperature',
  //       'humidity',
  //       'battery',
  //       'linkquality',
  //     ];
  //     const aIndex = importanceOrder.indexOf(a.key);
  //     const bIndex = importanceOrder.indexOf(b.key);

  //     if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
  //     if (aIndex !== -1) return -1;
  //     if (bIndex !== -1) return 1;

  //     return a.label.localeCompare(b.label);
  //   });
  // }

  getSensorDataFromBack(device: any): SensorData[] {
    const state = device.State;

    if (!state) return [];
    const sensorData: SensorData[] = [];

    // Skip meta fields that shouldn't be displayed as sensor data
    const skipFields = [
      'ieee_address',
      'friendly_name',
      'device',
      'update',
      'update_available',
      'linkquality',
      'last_seen',
      'elapsed'
    ];

    Object.entries(state).forEach(([key, value]) => {
      if (skipFields.includes(key) || value === null || value === undefined)
        return;

      const typeInfo = this.dataTypeMap[key] || {
        icon: this.getDefaultIcon(key, value),
        label: this.formatLabel(key),
        type: this.inferType(value),
      };

      sensorData.push({
        key,
        value,
        unit: typeInfo.unit,
        icon: typeInfo.icon!,
        label: typeInfo.label!,
        type: typeInfo.type!,
        isControl: typeInfo.isControl || false,
        min: typeInfo.min,
        max: typeInfo.max,
      });
    });

    // Sort data: controls first, then by importance
    return sensorData.sort((a, b) => {
      if (a.isControl && !b.isControl) return -1;
      if (!a.isControl && b.isControl) return 1;

      const importanceOrder = [
        'state',
        'brightness',
        'temperature',
        'humidity',
        'battery',
        'linkquality',
      ];
      const aIndex = importanceOrder.indexOf(a.key);
      const bIndex = importanceOrder.indexOf(b.key);

      if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
      if (aIndex !== -1) return -1;
      if (bIndex !== -1) return 1;

      return a.label.localeCompare(b.label);
    });
  }
  //   getSensorDataFromBack(device: BackEndDevice): SensorData[] {
  //   const state = this.getDeviceState(device.NetworkAddress);
  //   if (!state) return [];

  //   const sensorData: SensorData[] = [];

  //   // Skip meta fields that shouldn't be displayed as sensor data
  //   const skipFields = ['ieee_address', 'friendly_name', 'device', 'update', 'update_available', 'linkquality', 'last_seen', 'elapsed'];

  //   Object.entries(state).forEach(([key, value]) => {
  //     if (skipFields.includes(key) || value === null || value === undefined) return;

  //     const typeInfo = this.dataTypeMap[key] || {
  //       icon: this.getDefaultIcon(key, value),
  //       label: this.formatLabel(key),
  //       type: this.inferType(value)
  //     };

  //     sensorData.push({
  //       key,
  //       value,
  //       unit: typeInfo.unit,
  //       icon: typeInfo.icon!,
  //       label: typeInfo.label!,
  //       type: typeInfo.type!,
  //       isControl: typeInfo.isControl || false,
  //       min: typeInfo.min,
  //       max: typeInfo.max
  //     });
  //   });

  //   // Sort data: controls first, then by importance
  //   return sensorData.sort((a, b) => {
  //     if (a.isControl && !b.isControl) return -1;
  //     if (!a.isControl && b.isControl) return 1;

  //     const importanceOrder = ['state', 'brightness', 'temperature', 'humidity', 'battery', 'linkquality'];
  //     const aIndex = importanceOrder.indexOf(a.key);
  //     const bIndex = importanceOrder.indexOf(b.key);

  //     if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
  //     if (aIndex !== -1) return -1;
  //     if (bIndex !== -1) return 1;

  //     return a.label.localeCompare(b.label);
  //   });
  // }

  private getDefaultIcon(key: string, value: any): string {
    if (typeof value === 'boolean') return 'toggle_on';
    if (typeof value === 'number') return 'straighten';
    if (key.includes('time') || key.includes('date') || key.includes('seen'))
      return 'schedule';
    return 'info';
  }

  private formatLabel(key: string): string {
    // Basic formatting for keys not in dataTypeMap
    return key
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  private inferType(value: any): 'number' | 'boolean' | 'string' | 'object' {
    if (typeof value === 'boolean') return 'boolean';
    if (typeof value === 'number') return 'number';
    if (typeof value === 'object' && value !== null) return 'object';
    return 'string';
  }

  formatSensorValue(data: SensorData): string {
    if (data.type === 'boolean') {
      if (data.key === 'contact') return data.value ? 'Fermé' : 'Ouvert';
      if (data.key === 'occupancy' || data.key === 'motion')
        return data.value ? 'Détecté' : 'Clair';
      if (data.key === 'state') return data.value ? 'ALLUMÉ' : 'ÉTEINT';
      return data.value ? 'Oui' : 'Non';
    }

    if (data.type === 'number') {
      if (data.key === 'brightness') {
        // Brightness is usually 0-254, convert to percentage
        return `${Math.round((data.value / 254) * 100)}`;
      }
      // For battery and humidity, display as whole numbers
      if (data.key === 'battery' || data.key === 'humidity') {
        return `${Math.round(data.value)}`;
      }
      // For temperature, display with one decimal place
      if (data.key === 'temperature') {
        return `${data.value.toFixed(1)}`;
      }
      return data.value.toString();
    }

    if (data.key === 'last_seen') {
      // Use DatePipe for consistent date formatting if needed here, but done in template
      return new Date(data.value).toLocaleString('fr-FR', {
        dateStyle: 'short',
        timeStyle: 'short',
      });
    }

    if (data.type === 'object') {
      // For object values, stringify for display (can be improved)
      return JSON.stringify(data.value);
    }

    return data.value.toString();
  }

  getDeviceIcon(device: any): string {
    switch (this.getDeviceCategory(device)) {
      case DeviceCategory.LIGHT:
        return 'lightbulb';
      case DeviceCategory.SWITCH:
        return 'toggle_on';
      case DeviceCategory.PLUG:
        return 'outlet';
      case DeviceCategory.DOOR:
        return 'door_front';
      case DeviceCategory.CLIMATE:
        return 'thermostat';
      case DeviceCategory.SENSOR:
        return 'sensors';
      case DeviceCategory.UNKNOWN:
        return 'device_unknown';
      default:
        return 'devices';
    }
  }

  // Control methods
  // toggleDevice(device: ZigbeeDevice): void {
  //   // This method is generic, better to use onToggleChange directly
  //   // based on the state key
  //   const currentState = this.getDeviceState(device)?.state;
  //   this.zigbeeService.setDeviceState(device.friendly_name, {
  //     state: currentState === 'ON' ? 'OFF' : 'ON',
  //   });
  // }

  // onBrightnessChange(device: ZigbeeDevice, event: MatSliderChange | any): void {
  //   // Handle both MatSliderChange and regular input event (for table)
  //   const value = event.value ?? event.target.value;
  //   if (value !== null && value !== undefined) {
  //     this.zigbeeService.setBrightness(device.friendly_name, value);
  //   }
  // }

  // onToggleChange(device: ZigbeeDevice, data: SensorData, event: any): void {
  //   if (data.key === 'state') {
  //     this.zigbeeService.setDeviceState(device.friendly_name, {
  //       state: event.checked ? 'ON' : 'OFF',
  //     });
  //   }
  //   // Add other toggleable controls if needed (e.g., lock, open/close for blinds)
  // }

  getBatteryIcon(batteryLevel: number): string {
    if (batteryLevel > 90) return 'battery_full';
    if (batteryLevel > 70) return 'battery_6_bar';
    if (batteryLevel > 50) return 'battery_4_bar';
    if (batteryLevel > 30) return 'battery_2_bar';
    if (batteryLevel > 10) return 'battery_alert';
    return 'battery_0_bar'; // Very low battery
  }

  getBatteryColor(batteryLevel: number): string {
    if (batteryLevel > 60) return 'var(--primary)'; // Green
    if (batteryLevel > 20) return '#ff9800'; // Orange
    return '#f44336'; // Red
  }

  getSignalColor(linkQuality: number): string {
    if (linkQuality > 200) return 'var(--primary)'; // Excellent
    if (linkQuality > 120) return '#8bc34a'; // Good
    if (linkQuality > 60) return '#ff9800'; // Fair
    return '#f44336'; // Poor
  }

  // isControlEnabled(device: ZigbeeDevice): boolean {
  //   // Controls are enabled only if bridge is online and device is considered online
  //   return this.bridgeState === 'online' && this.isDeviceOnline(device);
  // }

  get availableCategories(): string[] {
    const categories = new Set(
      this.devices.map((d) => this.getDeviceCategory(d))
    );
    return Array.from(categories)
      .filter((cat) => cat !== DeviceCategory.UNKNOWN) // Filter out 'unknown' from filter options
      .sort();
  }

  toggleView(view: 'grid' | 'table'): void {
    this.currentView = view;
  }
  // hasNoControls(device: ZigbeeDevice): boolean {
  //   return this.getSensorData(device).filter((s) => s.isControl).length === 0;
  // }
  openPopupForDevice(device: any) {
    this.selectedDevice = device;
    this.showPopup = true;
  }

  closePopup() {
    this.showPopup = false;
    this.selectedDevice = null;
  }
  comfirmerPopUP(device: any, values: Record<string, any>) {
    this.sensorsService
      .updateDisplayName(device.Id, { DisplayName: values['name'] })
      .subscribe({
        next: (updatedDevice) => {
          console.log('Updated:', updatedDevice);
          this.refreshDevices();
          this.showPopup = false;
          this.showSuccess("succès", "Le nom a été changé avec succès.")
        },
        error: (err) => {
          console.error('Failed to update:', err);
          this.showError("Erreur","Une erreur est survenue lors de la mise à jour")
        },
      });
  }
  getConfirmFn(device: any) {
    return (values: Record<string, any>) => {
      this.comfirmerPopUP(device, values);
    };
  }

  showSuccess(message: string, title: string) {
    this.toast.success(message, title, 3000, false);
  }

  showError(message: string, title: string) {
    this.toast.warning(message, title, 3000, false);
  }
  onStateSelectChange(device: any, stateObj: any, selectedValue: string) {
  console.log('Dropdown changed:', selectedValue);
  this.listener.setDeviceState(device.friendly_name, selectedValue.toUpperCase()); // or just selectedValue
}



changePage(page: number) {
  if (page >= 1 && page <= this.totalPages) {
    console.log(this.currentPage)
    this.currentPage = page;
    this.localOnChange()
  }
}

get visiblePages(): (number | 'dots')[] {
  const total = this.totalPages;
  const current = this.currentPage;
  const pages: (number | 'dots')[] = [];

  // Always show first two
  pages.push(1);
  if (total >= 2) pages.push(2);

  // Show left dots if needed
  if (current > 4) {
    pages.push('dots');
  }

  // Show current -1, current, current +1 if not already in first/last
  for (let i = current - 1; i <= current + 1; i++) {
    if (i > 2 && i < total - 1) {
      pages.push(i);
    }
  }

  // Show right dots if needed
  if (current < total - 3) {
    pages.push('dots');
  }

  // Always show last two
  if (total > 3) pages.push(total - 1);
  if (total > 2) pages.push(total);

  // Remove duplicates but keep order
  return [...new Set(pages)];
}


  getSensors(
    type: 'client' | 'site' | 'local' | 'controller' | 'all',
    id: string,
    payload: any
  ): void {
    this.sensorsService.getCapteursByType(type, id, payload).subscribe({
      next: (response) => {
        const devices = response.Data || [];
        this.totalPages = response.TotalPages;
        console.log('response' , response)
        this.injectionDataToRowData(devices);
        this.applyFiltersFromBackEnd();

        if (this.buttonon) {
          this.listener.stopAll();

          this.devices = this.devicesBackEnd.map((device) => ({
            ...device,
            State: null,
          }));

          this.subscribeToMultipleSensors(this.topics);
          this.applyFilters();
        }

        console.log(this.filtredDevicesBackEnd);
      },
      error: (err) => {
        console.error('Failed to load sensors:', err);
      },
    });
  }



}

