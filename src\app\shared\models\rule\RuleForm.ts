import { Action } from "./Action";
import { Condition } from "./Condition";


export interface RuleForm {
  id: string;
  name: string;
  priority: number;
  Status: 'active' | 'inactive';
  tags: string; // Comma-separated string for input
  conditions: Condition[];
  actions: Action[];
  tagStatus: { [key: string]: string; }; // To track status of each tag
  ruleName?: string; // From getRulePreviewJson
  topicPattern?: string; // Comma-separated string for input
  subjectPattern?: string; // From getRulePreviewJson
  isActive?: boolean; // For form checkbox
  hasSchedule?: boolean;
  scheduleTime?: string;
  status?: string; // For edit form
}
