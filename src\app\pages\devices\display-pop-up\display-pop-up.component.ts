import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms'; 
import { SimpleChanges } from '@angular/core';

@Component({
  selector: 'app-display-pop-up',
  standalone: true,
  imports: [CommonModule, FormsModule ],
  templateUrl: './display-pop-up.component.html',
  styleUrls: ['./display-pop-up.component.css']
})
export class DisplayPopUpComponent {
  @Input() show = false;
  @Input() title = 'Confirmation';
  @Input() inputs: { label: string; key: string; type?: string ,value?: string}[] = [];
  @Input() confirmFn: (values: Record<string, any>) => void = () => {};
  @Input() sensorData: {friendly_name?: string; Model?:string } = {};

  @Output() close = new EventEmitter<void>();

  formValues: Record<string, any> = {};
  
  ngOnChanges(changes: SimpleChanges) {
    if (changes['inputs'] && this.inputs) {
      this.inputs.forEach(input => {
        if (!(input.key in this.formValues)) {
          this.formValues[input.key] = input.value || '';
        }
      });
    }
  }

  confirm() {
    this.confirmFn(this.formValues);
    this.closePopup();
  }

  closePopup() {
    this.formValues = {};
    this.close.emit();
  }
}
