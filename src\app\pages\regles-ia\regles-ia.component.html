<div class="regles-container">
  <!-- En-tête -->
  <div class="header">
    <div class="header-content">
      <div>
        <h1>Règles d'Automatisation IoT</h1>
        <p>G<PERSON>rez votre maison intelligente avec des automatisations alimentées par l'IA</p>
      </div>
    </div>
  </div>

  <!-- Onglets -->
  <div class="tabs">
    <button 
      [class.active]="activeTab === 'active'"
      (click)="setActiveTab('active')"
    >
      Règles Actives ({{activeRules.length}})
    </button>
    <button 
      [class.active]="activeTab === 'suggestions'"
      (click)="setActiveTab('suggestions')"
    >
      Suggestions IA ({{aiSuggestions.length}})
    </button>
  </div>

  <!-- Grille des Règles -->
  <div class="rules-grid">
    <div class="rule-card" *ngFor="let rule of activeTab === 'active' ? activeRules : aiSuggestions">
      <div class="card-header">
        <div class="icons-group">
          <div class="icon-wrapper" *ngFor="let icon of rule.icons.slice(0, 3)">
            <mat-icon>{{icon}}</mat-icon>
          </div>
        </div>
        <div class="ai-badge" *ngIf="rule.aiSuggested">
          <mat-icon>psychology</mat-icon>
          Suggéré par l'IA
        </div>
        <div class="confidence" *ngIf="!rule.status && rule.confidence">
          {{rule.confidence}}% de correspondance
        </div>
      </div>

      <h3>{{rule.name}}</h3>

      <div class="rule-details">
        <div class="trigger-section">
          <span class="label">Déclencheur</span>
          <p>{{rule.trigger}}</p>
        </div>

        <div class="actions-section">
          <span class="label">Actions</span>
          <ul>
            <li *ngFor="let action of rule.actions">
              <mat-icon>flash_on</mat-icon>
              {{action}}
            </li>
          </ul>
        </div>
      </div>

      <div class="card-footer">
        <div class="status" *ngIf="rule.status">
          <div class="status-dot"></div>
          Actif
        </div>
        <button class="btn-add" *ngIf="!rule.status">
          Ajouter la Règle
        </button>
        <button class="btn-settings">
          <mat-icon>settings</mat-icon>
        </button>
      </div>
    </div>
  </div>

  <!-- Aperçus IA -->
  <div class="ai-insights">
    <div class="insights-header">
      <mat-icon>psychology</mat-icon>
      <h2>Aperçus IA</h2>
    </div>
    
    <div class="insights-grid">
      <div class="insight-card">
        <div class="value">87%</div>
        <div class="label">Économies d'Énergie ce Mois</div>
      </div>
      <div class="insight-card">
        <div class="value">24</div>
        <div class="label">Actions Automatisées Aujourd'hui</div>
      </div>
      <div class="insight-card">
        <div class="value">5.2</div>
        <div class="label">Heures Économisées cette Semaine</div>
      </div>
    </div>
  </div>
</div>