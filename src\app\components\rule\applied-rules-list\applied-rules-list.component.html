<div class="applied-rules-container">
    <div class="rules-header">
        <h3 class="rules-title">
            <mat-icon class="title-icon">rule</mat-icon>
            Règles Appliquées
            <span class="rules-count" *ngIf="appliedRules.length > 0">({{ appliedRules.length }})</span>
        </h3>
        <button mat-raised-button color="primary" class="add-rule-btn" (click)="openRuleFormDialog()"
            [disabled]="isLoading">
            <mat-icon>add</mat-icon>
            Ajouter une règle
        </button>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="loading-container">
        <div class="spinner"></div>
        <p>Chargement des règles appliquées...</p>
    </div>

    <!-- Empty State -->
    <div *ngIf="!isLoading && appliedRules.length === 0" class="empty-state">
        <mat-icon class="empty-icon">rule_folder</mat-icon>
        <h4>Aucune règle appliquée</h4>
        <p>Ce local n'a encore aucune règle configurée.</p>
    </div>

    <!-- Rules List -->
    <div *ngIf="!isLoading && appliedRules.length > 0" class="rules-list">
        <mat-expansion-panel *ngFor="let rule of appliedRules; trackBy: trackByRuleId" class="rule-card"
            [expanded]="rule.isExpanded" (expandedChange)="onRuleExpand(rule, $event)">

            <!-- Panel Header -->
            <mat-expansion-panel-header class="rule-header">
                <mat-panel-title class="rule-title-container">
                    <div class="rule-main-info">
                        <div class="rule-name">
                            <mat-icon class="rule-icon">{{ getRuleIcon(rule.ruleData) }}</mat-icon>
                            <span class="name-text">{{ rule.ruleName }}</span>
                        </div>
                        <div class="rule-status">
                            <mat-icon class="status-icon" [class.enabled]="rule.ruleData.enabled"
                                [class.disabled]="!rule.ruleData.enabled">
                                {{ rule.ruleData.enabled ? 'check_circle' : 'cancel' }}
                            </mat-icon>
                            <span class="status-text">
                                {{ rule.ruleData.enabled ? 'Activée' : 'Désactivée' }}
                            </span>
                        </div>
                    </div>
                </mat-panel-title>

                <mat-panel-description class="rule-description">
                    <div class="rule-stats">
                        <div class="stat-item">
                            <mat-icon class="stat-icon">memory</mat-icon>
                            <span>{{ rule.controllers.length }} contrôleur(s)</span>
                        </div>
                        <div class="stat-item">
                            <mat-icon class="stat-icon">sensors</mat-icon>
                            <span>{{ getTotalCapteurs(rule) }} capteur(s)</span>
                        </div>
                        <div class="stat-item priority-badge" [attr.data-priority]="rule.ruleData.priority">
                            <mat-icon class="stat-icon">priority_high</mat-icon>
                            <span>Priorité {{ rule.ruleData.priority }}</span>
                        </div>
                    </div>
                </mat-panel-description>
            </mat-expansion-panel-header>

            <!-- Panel Content -->
            <div class="rule-content">
                <!-- Rule Summary -->
                <div class="rule-summary-section">
                    <h4 class="section-title">
                        <mat-icon>info</mat-icon>
                        Résumé de la règle
                    </h4>
                    <div class="rule-summary-content">
                        <div class="summary-item">
                            <span class="summary-label">Conditions:</span>
                            <span class="summary-value">{{ getConditionsCount(rule.ruleData) }} condition(s)</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Actions:</span>
                            <span class="summary-value">{{ rule.ruleData.actions.length }} action(s)</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Topics surveillés:</span>
                            <div class="topic-chips">
                                <mat-chip *ngFor="let topic of rule.ruleData.topic_pattern" class="topic-chip">
                                    {{ topic }}
                                </mat-chip>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Controllers and Capteurs -->
                <div class="controllers-section">
                    <h4 class="section-title">
                        <mat-icon>account_tree</mat-icon>
                        Applications par contrôleur
                    </h4>

                    <div *ngFor="let controllerApp of rule.controllers; trackBy: trackByControllerId"
                        class="controller-application">

                        <div class="controller-header">
                            <div class="controller-info">
                                <mat-icon class="controller-icon">memory</mat-icon>
                                <div class="controller-details">
                                    <h5 class="controller-name">{{ controllerApp.controller.Model }}</h5>
                                    <p class="controller-hostname">{{ controllerApp.controller.HostName }}</p>
                                </div>
                            </div>
                            <div class="controller-stats">
                                <span class="capteur-count">
                                    {{ controllerApp.capteurs.length }} capteur(s)
                                </span>
                            </div>
                        </div>

                        <!-- Capteurs List -->
                        <div class="capteurs-grid">
                            <div *ngFor="let capteur of controllerApp.capteurs; trackBy: trackByCapteurId"
                                class="capteur-card">
                                <div class="capteur-header">
                                    <mat-icon class="capteur-type-icon" [class.sensor]="isSensor(capteur)"
                                        [class.actuator]="!isSensor(capteur)">
                                        {{ getCapteurIcon(capteur) }}
                                    </mat-icon>
                                    <div class="capteur-info">
                                        <h6 class="capteur-name">{{ capteur.FriendlyName || capteur.Topic }}</h6>
                                        <p class="capteur-topic">{{ capteur.Topic }}</p>
                                    </div>
                                </div>
                                <div class="capteur-type-badge" [class.sensor-badge]="isSensor(capteur)"
                                    [class.actuator-badge]="!isSensor(capteur)">
                                    {{ capteur.TypeCapteur?.DisplayName || capteur.TypeCapteur?.Nom || 'Type non défini'
                                    }}
                                </div>
                            </div>
                        </div>

                        <!-- Connection Flow Diagram -->
                        <div class="connection-flow" *ngIf="controllerApp.capteurs.length > 0">
                            <div class="flow-title">
                                <mat-icon>device_hub</mat-icon>
                                Flux de données
                            </div>
                            <div class="flow-diagram">
                                <div class="flow-item flow-sensors">
                                    <mat-icon>sensors</mat-icon>
                                    <span>Capteurs ({{ getSensorCount(controllerApp.capteurs) }})</span>
                                </div>
                                <mat-icon class="flow-arrow">arrow_forward</mat-icon>
                                <div class="flow-item flow-controller">
                                    <mat-icon>memory</mat-icon>
                                    <span>{{ controllerApp.controller.Model }}</span>
                                </div>
                                <mat-icon class="flow-arrow">arrow_forward</mat-icon>
                                <div class="flow-item flow-actuators">
                                    <mat-icon>settings_remote</mat-icon>
                                    <span>Actionneurs ({{ getActuatorCount(controllerApp.capteurs) }})</span>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="rule-actions">
                    <!-- <button mat-button class="action-btn secondary" (click)="onViewRuleDetails(rule)">
                        <mat-icon>visibility</mat-icon>
                        Voir détails
                    </button> -->
                    <button mat-button class="action-btn secondary" (click)="onEditRule(rule)">
                        <mat-icon>edit</mat-icon>
                        Modifier
                    </button>
                    <!-- <button mat-button class="action-btn" [class.danger]="rule.ruleData.enabled"
                        [class.success]="!rule.ruleData.enabled" (click)="onToggleRule(rule)">
                        <mat-icon>{{ rule.ruleData.enabled ? 'pause' : 'play_arrow' }}</mat-icon>
                        {{ rule.ruleData.enabled ? 'Désactiver' : 'Activer' }}
                    </button>
                    <button mat-button class="action-btn danger" (click)="onDeleteRule(rule)">
                        <mat-icon>delete</mat-icon>
                        Supprimer
                    </button> -->
                </div>

            </div>
        </mat-expansion-panel>
    </div>
</div>