.statistics-header {
  background: white;
  border-radius: 8px;
  padding: 15px 20px;
  margin-bottom: 15px;
  width: 100%;
  opacity: 0;
  transform: translateY(-20px);
  animation: slideInFromTop 0.6s ease-out forwards;
}

.statistics-title {
  font-size: 18px;
  font-weight: 600;
  color: #212529;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.dashboard-container {
  padding: 15px;
  max-width: 1000px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #333;
  line-height: 1.5;
}

.dashboard-grid {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.cards-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  width: 100%;
  justify-content: center;
  margin-top: 20px;
}

.card {
  background: white;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  height: fit-content;
  min-height: 120px;
  width: 280px;
  flex: 0 0 280px;
  opacity: 0;
  transform: translateY(30px) scale(0.95);
  transition: all 0.3s ease, box-shadow 0.3s ease, transform 0.3s ease;
  animation: cardSlideIn 0.6s ease-out forwards;
}

/* Staggered animation delays for cards */
.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }
.card:nth-child(4) { animation-delay: 0.4s; }
.card:nth-child(5) { animation-delay: 0.5s; }
.card:nth-child(6) { animation-delay: 0.6s; }
.card:nth-child(7) { animation-delay: 0.7s; }
.card:nth-child(8) { animation-delay: 0.8s; }

.card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.card-title {
  font-size: 13px;
  font-weight: 500;
  color: #6c757d;
  margin-bottom: 6px;
  opacity: 0;
  animation: fadeIn 0.5s ease-out 0.3s forwards;
}

.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #212529;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transform: translateX(-10px);
  animation: slideInFromLeft 0.5s ease-out 0.4s forwards;
}

.kwh-unit {
  font-size: 14px;
  font-weight: 400;
  color: #6c757d;
  opacity: 0;
  animation: fadeIn 0.5s ease-out 0.5s forwards;
}

.percentage {
  font-size: 11px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  gap: 2px;
  opacity: 0;
  transform: scale(0.8);
  animation: scaleIn 0.4s ease-out 0.6s forwards;
  transition: transform 0.2s ease;
}

.percentage:hover {
  transform: scale(1.1);
}

.percentage.positive {
  color: #198754;
  background-color: #d1e7dd;
}

.percentage.negative {
  color: #dc3545;
  background-color: #f8d7da;
}

.na-badge {
  background-color: #fff3cd;
  color: #856404;
  font-size: 11px;
  font-weight: 500;
  padding: 3px 6px;
  border-radius: 4px;
  opacity: 0;
  animation: fadeIn 0.4s ease-out 0.6s forwards;
}

.objective-section {
  margin-top: 12px;
  opacity: 0;
  animation: fadeInUp 0.5s ease-out 0.7s forwards;
}

.objective-label {
  font-size: 11px;
  color: #6c757d;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.objective-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  transform: scale(0);
  animation: bounceIn 0.4s ease-out 0.8s forwards;
}

.objective-dot.blue { background-color: #0d6efd; }
.objective-dot.green { background-color: #20c997; }
.objective-dot.purple { background-color: #6f42c1; }
.objective-dot.yellow { background-color: #ffc107; }

.progress-container {
  display: flex;
  align-items: center;
  gap: 6px;
}

.progress-bar {
  flex: 1;
  height: 3px;
  background-color: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
  opacity: 0;
  animation: fadeIn 0.4s ease-out 0.9s forwards;
}

.progress-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(-100%);
  animation: progressSlide 1s ease-out 1s forwards;
}

.progress-fill.blue { background-color: #0d6efd; }
.progress-fill.green { background-color: #20c997; }
.progress-fill.purple { background-color: #6f42c1; }
.progress-fill.yellow { background-color: #ffc107; }

.progress-text {
  font-size: 11px;
  font-weight: 600;
  color: #495057;
  min-width: 28px;
  opacity: 0;
  animation: fadeIn 0.4s ease-out 1.1s forwards;
}

/* Charts Section Animations */
.charts-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  opacity: 0;
  animation: fadeInUp 0.8s ease-out 0.9s forwards;
}

.top-chart {
  width: 80%;
  margin: 0 auto;
  opacity: 0;
  transform: translateY(40px) scale(0.95);
  animation: chartSlideIn 0.8s ease-out 1.1s forwards;
}

.bottom-charts {
  display: flex;
  gap: 20px;
  justify-content: center;
  max-width: 900px;
  margin: 0 auto;
  opacity: 0;
  animation: fadeInUp 0.8s ease-out 1.3s forwards;
}

.bottom-charts .chart-card {
  flex: 1;
  min-width: 400px;
  opacity: 0;
  transform: translateY(40px);
  animation: chartSlideIn 0.8s ease-out forwards;
}

.bottom-charts .chart-card:nth-child(1) {
  animation-delay: 1.4s;
  transform: translateY(40px) translateX(-20px);
}

.bottom-charts .chart-card:nth-child(2) {
  animation-delay: 1.6s;
  transform: translateY(40px) translateX(20px);
}

.chart-card {
  background: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  min-height: 320px;
  width: 100%;
  max-width: 100%;
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.chart-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #212529;
  opacity: 0;
  animation: fadeIn 0.5s ease-out forwards;
}

.top-chart .chart-title {
  animation-delay: 1.3s;
}

.bottom-charts .chart-card:nth-child(1) .chart-title {
  animation-delay: 1.6s;
}

.bottom-charts .chart-card:nth-child(2) .chart-title {
  animation-delay: 1.8s;
}

.chart-menu {
  color: #6c757d;
  font-size: 20px;
  cursor: pointer;
  opacity: 0;
  transform: rotate(-90deg);
  animation: fadeInRotate 0.5s ease-out forwards;
  transition: transform 0.3s ease, color 0.3s ease;
}

.top-chart .chart-menu {
  animation-delay: 1.4s;
}

.bottom-charts .chart-card:nth-child(1) .chart-menu {
  animation-delay: 1.7s;
}

.bottom-charts .chart-card:nth-child(2) .chart-menu {
  animation-delay: 1.9s;
}

.chart-menu:hover {
  color: #495057;
  transform: rotate(90deg);
}

.chart-container {
  position: relative;
  height: 260px;
  opacity: 0;
  transform: scale(0.95);
  animation: chartContentSlideIn 0.6s ease-out forwards;
}

.top-chart .chart-container {
  animation-delay: 1.5s;
}

.bottom-charts .chart-card:nth-child(1) .chart-container {
  animation-delay: 1.8s;
}

.bottom-charts .chart-card:nth-child(2) .chart-container {
  animation-delay: 2s;
}

/* Map Section Animation */
.map-section {
  width: 100%;
  opacity: 0;
  transform: translateY(50px) scale(0.95);
  animation: mapSlideIn 1s ease-out 2.1s forwards;
}

.map-section .chart-container {
  height: 250px;
  animation-delay: 2.3s;
}

.map-section .chart-title {
  animation-delay: 2.2s;
}

.map-section .chart-menu {
  animation-delay: 2.4s;
}

/* Keyframe Animations */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes chartSlideIn {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes chartContentSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) rotateX(10deg);
  }
  to {
    opacity: 1;
    transform: scale(1) rotateX(0deg);
  }
}

@keyframes mapSlideIn {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes progressSlide {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes fadeInRotate {
  from {
    opacity: 0;
    transform: rotate(-90deg);
  }
  to {
    opacity: 1;
    transform: rotate(0deg);
  }
}

/* Responsive design */
@media (max-width: 1200px) {
  .bottom-charts {
    flex-direction: column;
  }

  .bottom-charts .chart-card {
    min-width: unset;
    transform: translateY(40px);
  }

  .bottom-charts .chart-card:nth-child(1),
  .bottom-charts .chart-card:nth-child(2) {
    transform: translateY(40px);
  }
}

@media (max-width: 768px) {
  .cards-container {
    justify-content: center;
  }

  .card {
    width: calc(50% - 8px);
    min-width: 240px;
    flex: 0 0 calc(50% - 8px);
    animation-duration: 0.4s;
  }

  .card-value {
    font-size: 18px;
  }

  .chart-container {
    height: 220px;
  }

  .map-section .chart-container {
    height: 200px;
  }

  .statistics-header {
    animation-duration: 0.4s;
  }

  .chart-card {
    animation-duration: 0.6s;
  }
}

@media (max-width: 480px) {
  .card {
    width: 100%;
    flex: 0 0 100%;
  }
}

/* Prefers reduced motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Demo styles for the example */
.card-icon {
  font-size: 18px;
}

.amount-text {
  font-weight: 600;
}

#map {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
}

canvas {
  background: #f8f9fa;
  border-radius: 4px;
  width: 100%;
  height: 100%;
}
