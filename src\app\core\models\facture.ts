import { Class } from "leaflet";
import { AuditModel } from "./models-audit/audit-model";
import { Client } from "./client";
import { Subscription } from "./subscription";
import { Licence } from "./licence";
import { Option } from "./option";

export class Facture extends AuditModel {
    Total!: number;
    Status!: string;
    IdLicence!: string;
    Licence!: Licence;
    IdSubscription!: string;
    Subscription!: Subscription; 
}