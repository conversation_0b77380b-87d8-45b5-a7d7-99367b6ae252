import { FormElement } from "@app/shared/models/form-element";
 
 
export let formElements: FormElement[] = [
  {
    name: 'Name',
    libelle: 'Raison Sociale',
    isRequired: true,
    type: 'string',
  },
  {
    name: 'Address',
    libelle: 'Address',
    isRequired: true,
    type: 'string',
  },
  {
    name: 'PhoneNumber',
    libelle: 'PhoneNumber',
    isRequired: true,
    type: 'string',
  },
  {
    name: 'SIREN',
    libelle: 'SIREN',
    isRequired: true,
    type: 'string',
  },
  {
    name: 'SIRET',
    libelle: 'SIRET',
    isRequired: true,
    type: 'string',
  },
  {
    name: 'RC',
    libelle: 'RC',
    isRequired: true,
    type: 'string',
  },
  {
    name: 'ICE',
    libelle: 'ICE',
    isRequired: true,
    type: 'string',
  },
  {
    name: 'IF',
    libelle: 'IF',
    isRequired: true,
    type: 'string',
  },
  {
    name: '<PERSON><PERSON>',
    libelle: 'Patente',
    isRequired: true,
    type: 'string',
  },
  {
    name: 'LegalForm',
    libelle: 'LegalForm',
    isRequired: true,
    type: 'string',
  },
  {
    name: 'CompanyCreationDate',
    libelle: "Date de création d'entreprise",
    isRequired: true,
    type: 'number',
  }
]