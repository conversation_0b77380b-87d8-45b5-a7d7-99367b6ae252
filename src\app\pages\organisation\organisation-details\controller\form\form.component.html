<div class="create-form-card">
  <div class="form-container">
    <div class="view-controls">
      <button
        class="view-toggle-btn"
        [class.active]="viewMode === 'existingController'"
        (click)="onViewModeChange('existingController')"
      >
        <i class="material-icons">memory</i>
        <span>Existant</span>
      </button>

      <button
        class="view-toggle-btn"
        [class.active]="viewMode === 'newController'"
        (click)="onViewModeChange('newController')"
      >
        <i class="material-icons">developer_board</i>
        <span>Anonyme</span>
      </button>
    </div>
    <div
      class="mqtt-loading-container"
      *ngIf="
        isLoadingMqttData ||
        loadingState === 'success' ||
        loadingState === 'error'
      "
      [class.success]="loadingState === 'success'"
      [class.error]="loadingState === 'error'"
    >
      <!-- Progress bar - only show during loading -->
      <div class="progress-bar-container" *ngIf="isLoadingMqttData">
        <div class="progress-bar" [style.width.%]="loadingProgress"></div>
      </div>

      <!-- Loading text -->
      <div class="loading-text" *ngIf="isLoadingMqttData">
        Écoute des données MQTT... {{ loadingProgress }}%
      </div>

      <!-- Success/Error message -->
      <div
        class="message-text"
        *ngIf="loadingState === 'success' || loadingState === 'error'"
      >
        <i class="material-icons" *ngIf="loadingState === 'success'"
          >check_circle</i
        >
        <i class="material-icons" *ngIf="loadingState === 'error'">error</i>
        {{ loadingMessage }}
      </div>
    </div>
    <form
      style="padding-top: 10px"
      [formGroup]="createControllerForm"
      (ngSubmit)="onSubmit()"
    >
      <div class="form-grid">
        <div class="form-group">
          <label for="HostName"
            >Nom
            <span *ngIf="viewMode === 'existingController'" class="required"
              >*</span
            ></label
          >
          <input
            id="HostName"
            type="text"
            formControlName="HostName"
            [class.error]="hasError('HostName')"
          />
          <div class="error-message" *ngIf="hasError('HostName')">
            {{ getErrorMessage("HostName") }}
          </div>
        </div>

        <div class="form-group">
          <label for="Model"
            >Modèle
            <span *ngIf="viewMode === 'existingController'" class="required"
              >*</span
            ></label
          >
          <input
            id="Model"
            type="text"
            formControlName="Model"
            [class.error]="hasError('Model')"
          />
          <div class="error-message" *ngIf="hasError('Model')">
            {{ getErrorMessage("Model") }}
          </div>
        </div>

        <div class="form-group">
          <label for="SerialNumber"
            >N° de Série
            <span *ngIf="viewMode === 'existingController'" class="required"
              >*</span
            ></label
          >
          <input
            id="SerialNumber"
            type="text"
            formControlName="SerialNumber"
            [class.error]="hasError('SerialNumber')"
          />
          <div class="error-message" *ngIf="hasError('SerialNumber')">
            {{ getErrorMessage("SerialNumber") }}
          </div>
        </div>

        <div class="form-group">
          <label for="MacAddress"
            >Adresse MAC
            <span *ngIf="viewMode === 'existingController'" class="required"
              >*</span
            ></label
          >
          <input
            id="MacAddress"
            type="text"
            formControlName="MacAddress"
            [class.error]="hasError('MacAddress')"
          />
          <div class="error-message" *ngIf="hasError('MacAddress')">
            {{ getErrorMessage("MacAddress") }}
          </div>
        </div>

        <div class="form-group">
          <label for="IpAddress"
            >Adresse IP
            <span *ngIf="viewMode === 'existingController'" class="required"
              >*</span
            ></label
          >
          <input
            id="IpAddress"
            type="text"
            formControlName="IpAddress"
            [class.error]="hasError('IpAddress')"
          />
          <div class="error-message" *ngIf="hasError('IpAddress')">
            {{ getErrorMessage("IpAddress") }}
          </div>
        </div>

        <div class="form-group">
          <label for="BaseTopic"
            >Base Topic
            <span *ngIf="viewMode === 'existingController'" class="required"
              >*</span
            ></label
          >
          <input
            id="BaseTopic"
            type="text"
            formControlName="BaseTopic"
            [class.error]="hasError('BaseTopic')"
          />
          <div class="error-message" *ngIf="hasError('BaseTopic')">
            {{ getErrorMessage("BaseTopic") }}
          </div>
          <button
            type="button"
            class="get-info-button"
            [disabled]="
              !createControllerForm.get('BaseTopic')?.value || isLoadingMqttData
            "
            (click)="getDataFromBaseTopic()"
          >
            <span *ngIf="isLoadingMqttData">
              <i class="material-icons spinning">sync</i>
            </span>
            <span *ngIf="!isLoadingMqttData">Récupérer infos</span>
          </button>
        </div>

        <div class="form-group">
          <label for="State"
            >Statut
            <span *ngIf="viewMode === 'existingController'" class="required"
              >*</span
            ></label
          >
          <select
            id="State"
            formControlName="State"
            [class.error]="hasError('State')"
          >
            <option value="">-- Sélectionnez un statut --</option>
            <option value="true">Actif</option>
            <option value="false">Inactif</option>
          </select>
          <div class="error-message" *ngIf="hasError('State')">
            {{ getErrorMessage("State") }}
          </div>
        </div>

        <div class="form-group">
          <label for="InstallationDate">Date d'installation</label>
          <input
            id="InstallationDate"
            type="date"
            formControlName="InstallationDate"
            [class.error]="hasError('InstallationDate')"
          />
          <div class="error-message" *ngIf="hasError('InstallationDate')">
            {{ getErrorMessage("InstallationDate") }}
          </div>
        </div>

        <div class="form-group">
          <label for="ControllerServer"
            >Serveur de Contrôle <span class="required">*</span></label
          >
          <select
            id="ControllerServer"
            formControlName="ControllerServer"
            [disabled]="isLoadingControllerServers"
            class="form-control"
            [class.error]="hasError('ControllerServer')"
          >
            <option value="">-- Sélectionnez un serveur --</option>
            <option
              *ngFor="let server of availableControllerServers"
              [value]="server.Id"
            >
              {{ server.Name || server.Status }}
            </option>
          </select>
          <div class="info-message" *ngIf="isLoadingControllerServers">
            Chargement des serveurs...
          </div>
          <div class="error-message" *ngIf="hasError('ControllerServer')">
            {{ getErrorMessage("ControllerServer") }}
          </div>
        </div>
      </div>

      <div class="form-actions">
        <button
          type="button"
          class="cancel-button"
          (click)="onCancel()"
          [disabled]="isSubmitting"
        >
          Annuler
        </button>
        <button
          type="submit"
          class="submit-button"
          [disabled]="!createControllerForm.valid || isSubmitting"
        >
          <span *ngIf="isSubmitting">Enregistrement...</span>
          <span *ngIf="!isSubmitting">Enregistrer</span>
        </button>
      </div>
    </form>
  </div>
</div>
