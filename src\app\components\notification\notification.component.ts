// notification.component.ts
import { Component, Inject } from '@angular/core';
import { MAT_SNACK_BAR_DATA, MatSnackBarRef } from '@angular/material/snack-bar';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-notification',
  standalone: true,
  imports: [CommonModule, MatIconModule, MatButtonModule],
  template: `
    <div class="notification" [class]="data.type + '-notification'" [@slideDown]>
      <div class="notification-content">
        <span class="material-icons {{ data.type }}-icon">
          {{ data.type === 'success' ? 'check_circle' : 'error' }}
        </span>
        <div class="notification-text">
          <h4>{{ data.title }}</h4>
          <p>{{ data.message }}</p>
        </div>
        <button class="close-notification-btn" (click)="snackBarRef.dismiss()">
          <span class="material-icons">close</span>
        </button>
      </div>
    </div>
  `,
  styleUrls: ['./notification.component.css'],
  animations: [
    trigger('slideDown', [
      transition(':enter', [
        style({ transform: 'translateY(-20px)', opacity: 0 }),
        animate('300ms ease-out', style({ transform: 'translateY(0)', opacity: 1 }))
      ]),
      transition(':leave', [
        animate('200ms ease-in', style({ transform: 'translateY(-20px)', opacity: 0 }))
      ])
    ])
  ]
})
export class NotificationComponent {
  constructor(
    public snackBarRef: MatSnackBarRef<NotificationComponent>,
    @Inject(MAT_SNACK_BAR_DATA) public data: any
  ) {}
}