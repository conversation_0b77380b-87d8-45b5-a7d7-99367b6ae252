.dialog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 100vh;
  padding: 16px;
  box-sizing: border-box;
}

.dialog-title {
  font-size: 1.25rem;
  font-weight: 500;
  color: #2e7d32;
  margin: 0 0 20px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
  position: sticky;
  top: 0;
  background: white;
  z-index: 1;
}

.sensor-form {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.form-scrollable {
  overflow-y: auto;
  flex: 1;
  padding-bottom: 16px;
}

/* Form field spacing and layout */
.form-field {
  width: 100%;
  margin-bottom: 16px;
}

.form-field .mat-form-field-wrapper {
  padding-bottom: 0.5em;
}

.form-field .mat-form-field-outline {
  top: 0.25em;
}

.form-row.pair {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-row.pair .form-field {
  flex: 1;
  min-width: calc(50% - 8px);
}

/* Specific field adjustments */
.description-field {
  margin-bottom: 24px;
}

.description-field textarea {
  min-height: 80px;
}

/* Label spacing */
.mat-form-field-label {
  transform: translateY(1.2em) scale(0.75);
  margin-bottom: 4px;
}

.mat-form-field-appearance-outline .mat-form-field-label {
  top: 1.5em;
}

/* Image section */
.image-section {
  margin: 24px 0 16px 0;
  padding: 16px;
  border-radius: 8px;
  background-color: #fafafa;
  border: 1px solid #e0e0e0;
}

.section-title {
  font-size: 0.9rem;
  font-weight: 500;
  color: #2e7d32;
  margin: 0 0 16px 0;
}

.current-image {
  margin: 16px 0;
  text-align: center;
}

.preview-label {
  font-size: 0.8rem;
  color: #616161;
  margin-bottom: 8px;
}

.preview-image {
  max-width: 100%;
  max-height: 120px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.gallery-title {
  font-size: 0.8rem;
  color: #616161;
  margin: 16px 0 8px 0;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  gap: 8px;
}

.gallery-item {
  cursor: pointer;
  border: 2px solid transparent;
  border-radius: 4px;
  transition: all 0.2s ease;
  padding: 2px;
}

.gallery-item:hover {
  border-color: #c8e6c9;
}

.gallery-item.selected {
  border-color: #2e7d32;
  background-color: #e8f5e9;
}

.gallery-image {
  width: 100%;
  height: 60px;
  object-fit: contain;
  border-radius: 2px;
}

.no-images {
  text-align: center;
  padding: 16px;
  color: #9e9e9e;
  font-size: 0.8rem;
}

.no-images-icon {
  font-size: 36px;
  width: 36px;
  height: 36px;
  margin-bottom: 4px;
  color: #e0e0e0;
}

.image-error {
  font-size: 0.7rem;
  margin-top: 4px;
  color: #f44336;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
  position: sticky;
  bottom: 0;
  background: white;
}

.action-button {
  min-width: 100px;
  font-size: 0.8rem;
}

.action-button mat-icon {
  margin-right: 4px;
  font-size: 16px;
}

.action-button.cancel {
  color: #b21212;
}

.action-button.submit {
  background-color: #2e7d32;
  color: white;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .form-row.pair {
    flex-direction: column;
    gap: 16px;
  }
  
  .form-row.pair .form-field {
    min-width: 100%;
  }
  
  .dialog-actions {
    justify-content: space-between;
  }
  
  .action-button {
    flex: 1;
  }
}