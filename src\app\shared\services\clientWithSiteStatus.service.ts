import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ClientWithSiteStatus } from '@app/shared/models/clientWithSiteStatus';
import { environment } from '@app/environments/environment';

@Injectable({ providedIn: 'root' })
export class ClientSiteStatusService {
  private baseUrl = environment.host + '/api/client/sitestatus';

  constructor(private http: HttpClient) {}

  getAll(clientId: string): Observable<ClientWithSiteStatus[]> {
    return this.http.get<ClientWithSiteStatus[]>(this.baseUrl + `/${clientId}`);
  }
}