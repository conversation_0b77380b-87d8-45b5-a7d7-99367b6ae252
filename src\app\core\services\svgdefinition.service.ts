// svg-definitions.service.ts
import { Injectable } from '@angular/core';

export interface SVGColors {
  fillColor: string;
  strokeColor: string;
  strokeWidth: number;
}

@Injectable({
  providedIn: 'root'
})
export class SvgDefinitionsService {

  constructor() { }

  /**
   * Retourne la définition SVG pour un type d'élément donné
   * @param type - Le type d'élément
   * @param colors - Les couleurs à appliquer
   * @returns La chaîne SVG correspondante
   */
  getSVGDefinition(type: string, colors: SVGColors): string {
    const { fillColor, strokeColor, strokeWidth } = colors;

    const svgTemplates: { [key: string]: string } = {
      // ============ MOBILIER EXISTANT ============
      'bureau': `
        <svg viewBox="0 0 120 60" xmlns="http://www.w3.org/2000/svg">
          <rect x="0" y="0" width="120" height="60" rx="5" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
          <!-- Pieds -->
          <rect x="10" y="50" width="8" height="20" fill="${this.darkenColor(fillColor)}"/>
          <rect x="102" y="50" width="8" height="20" fill="${this.darkenColor(fillColor)}"/>
          <!-- Surface de travail -->
          <rect x="20" y="10" width="80" height="30" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}"/>
          <!-- Détails -->
          <rect x="25" y="15" width="15" height="20" fill="${this.darkenColor(fillColor)}" opacity="0.3"/>
          <rect x="85" y="15" width="10" height="20" fill="${this.darkenColor(fillColor)}" opacity="0.3"/>
        </svg>
      `,

      'chaise': `
        <svg viewBox="0 0 50 60" xmlns="http://www.w3.org/2000/svg">
          <!-- Dossier -->
          <rect x="5" y="0" width="40" height="35" rx="5" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
          <!-- Siège -->
          <rect x="0" y="30" width="50" height="30" rx="3" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
          <!-- Pieds -->
          <rect x="5" y="55" width="4" height="15" fill="${this.darkenColor(fillColor)}"/>
          <rect x="41" y="55" width="4" height="15" fill="${this.darkenColor(fillColor)}"/>
          <rect x="15" y="55" width="4" height="15" fill="${this.darkenColor(fillColor)}"/>
          <rect x="31" y="55" width="4" height="15" fill="${this.darkenColor(fillColor)}"/>
        </svg>
      `,

      'lit': `
        <svg viewBox="0 0 100 140" xmlns="http://www.w3.org/2000/svg">
          <!-- Matelas -->
          <rect x="10" y="20" width="80" height="120" rx="5" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
          <!-- Tête de lit -->
          <rect x="5" y="15" width="90" height="15" rx="7" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}" stroke-width="1"/>
          <!-- Oreillers -->
          <ellipse cx="30" cy="40" rx="15" ry="8" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="1"/>
          <ellipse cx="70" cy="40" rx="15" ry="8" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="1"/>
          <!-- Pieds -->
          <rect x="15" y="135" width="8" height="10" fill="${this.darkenColor(fillColor)}"/>
          <rect x="77" y="135" width="8" height="10" fill="${this.darkenColor(fillColor)}"/>
        </svg>
      `,

      'table': `
        <svg viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
          <!-- Plateau -->
          <ellipse cx="60" cy="40" rx="55" ry="35" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
          <!-- Pied central -->
          <rect x="55" y="65" width="10" height="15" fill="${this.darkenColor(fillColor)}"/>
          <!-- Base -->
          <ellipse cx="60" cy="75" rx="25" ry="8" fill="${this.darkenColor(fillColor)}"/>
        </svg>
      `,

      // ============ MEUBLES DE CUISINE ============
      'tall-cabinet': `
        <svg viewBox="0 0 60 120" xmlns="http://www.w3.org/2000/svg">
          <rect x="5" y="5" width="50" height="110" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
          <!-- Portes -->
          <rect x="8" y="8" width="44" height="52" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}"/>
          <rect x="8" y="65" width="44" height="47" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}"/>
          <!-- Poignées -->
          <circle cx="45" cy="30" r="2" fill="${strokeColor}"/>
          <circle cx="45" cy="85" r="2" fill="${strokeColor}"/>
        </svg>
      `,

      'l-shape-counter-left': `
        <svg viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
          <path d="M5,5 L115,5 L115,115 L65,115 L65,65 L5,65 Z" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
          <!-- Détails surface -->
          <line x1="10" y1="10" x2="110" y2="10" stroke="${this.darkenColor(fillColor)}" stroke-width="1"/>
          <line x1="10" y1="60" x2="60" y2="60" stroke="${this.darkenColor(fillColor)}" stroke-width="1"/>
        </svg>
      `,

      'l-shape-counter-right': `
        <svg viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
          <path d="M5,5 L115,5 L115,65 L65,65 L65,115 L5,115 Z" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
          <!-- Détails surface -->
          <line x1="10" y1="10" x2="110" y2="10" stroke="${this.darkenColor(fillColor)}" stroke-width="1"/>
          <line x1="10" y1="60" x2="60" y2="60" stroke="${this.darkenColor(fillColor)}" stroke-width="1"/>
        </svg>
      `,

      // ============ TABLES ET SIÈGES ============
      'round-table': `
        <svg viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
          <!-- Table ronde -->
          <circle cx="60" cy="60" r="50" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
          <!-- Pied central -->
          <circle cx="60" cy="60" r="8" fill="${this.darkenColor(fillColor)}"/>
          <!-- Chaises autour -->
          <rect x="45" y="5" width="30" height="15" rx="3" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}"/>
          <rect x="5" y="45" width="15" height="30" rx="3" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}"/>
          <rect x="45" y="100" width="30" height="15" rx="3" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}"/>
          <rect x="100" y="45" width="15" height="30" rx="3" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}"/>
        </svg>
      `,

      'oval-table': `
        <svg viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
          <!-- Table ovale -->
          <ellipse cx="60" cy="40" rx="50" ry="30" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
          <!-- Pieds -->
          <ellipse cx="35" cy="40" rx="5" ry="3" fill="${this.darkenColor(fillColor)}"/>
          <ellipse cx="85" cy="40" rx="5" ry="3" fill="${this.darkenColor(fillColor)}"/>
          <!-- Chaises -->
          <rect x="45" y="5" width="30" height="10" rx="2" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}"/>
          <rect x="45" y="65" width="30" height="10" rx="2" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}"/>
          <rect x="5" y="30" width="15" height="20" rx="2" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}"/>
          <rect x="100" y="30" width="15" height="20" rx="2" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}"/>
        </svg>
      `,

      // ============ ÉLECTROMÉNAGER ============
      'double-sink': `
        <svg viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
          <!-- Plan de travail -->
          <rect x="5" y="5" width="110" height="70" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
          <!-- Éviers -->
          <rect x="15" y="15" width="40" height="30" rx="5" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}"/>
          <rect x="65" y="15" width="40" height="30" rx="5" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}"/>
          <!-- Robinets -->
          <circle cx="35" cy="15" r="3" fill="${this.darkenColor(strokeColor)}"/>
          <circle cx="85" cy="15" r="3" fill="${this.darkenColor(strokeColor)}"/>
          <!-- Évacuations -->
          <circle cx="35" cy="40" r="2" fill="${strokeColor}"/>
          <circle cx="85" cy="40" r="2" fill="${strokeColor}"/>
        </svg>
      `,

      'cooktop': `
        <svg viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
          <!-- Surface -->
          <rect x="5" y="5" width="110" height="70" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
          <!-- Quatre feux -->
          <circle cx="30" cy="30" r="12" fill="none" stroke="${this.darkenColor(strokeColor)}" stroke-width="2"/>
          <circle cx="90" cy="30" r="12" fill="none" stroke="${this.darkenColor(strokeColor)}" stroke-width="2"/>
          <circle cx="30" cy="55" r="12" fill="none" stroke="${this.darkenColor(strokeColor)}" stroke-width="2"/>
          <circle cx="90" cy="55" r="12" fill="none" stroke="${this.darkenColor(strokeColor)}" stroke-width="2"/>
          <!-- Centres des feux -->
          <circle cx="30" cy="30" r="3" fill="${this.darkenColor(strokeColor)}"/>
          <circle cx="90" cy="30" r="3" fill="${this.darkenColor(strokeColor)}"/>
          <circle cx="30" cy="55" r="3" fill="${this.darkenColor(strokeColor)}"/>
          <circle cx="90" cy="55" r="3" fill="${this.darkenColor(strokeColor)}"/>
        </svg>
      `,

      // ============ SALLE DE BAIN ============
      'toilet': `
        <svg viewBox="0 0 40 60" xmlns="http://www.w3.org/2000/svg">
          <!-- Base -->
          <ellipse cx="20" cy="45" rx="18" ry="13" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
          <!-- Réservoir -->
          <rect x="5" y="5" width="30" height="40" rx="15" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
          <!-- Cuvette -->
          <ellipse cx="20" cy="25" rx="12" ry="8" fill="${this.darkenColor(fillColor)}"/>
          <!-- Bouton chasse -->
          <rect x="18" y="8" width="4" height="6" rx="2" fill="${strokeColor}"/>
        </svg>
      `,

      'bathtub': `
        <svg viewBox="0 0 170 80" xmlns="http://www.w3.org/2000/svg">
          <!-- Baignoire -->
          <ellipse cx="85" cy="40" rx="80" ry="35" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
          <!-- Intérieur -->
          <ellipse cx="85" cy="35" rx="70" ry="25" fill="${this.lightenColor(fillColor)}"/>
          <!-- Pieds -->
          <rect x="5" y="70" width="10" height="15" rx="5" fill="${this.darkenColor(fillColor)}"/>
          <rect x="155" y="70" width="10" height="15" rx="5" fill="${this.darkenColor(fillColor)}"/>
          <!-- Robinetterie -->
          <circle cx="15" cy="25" r="3" fill="${strokeColor}"/>
          <circle cx="25" cy="25" r="3" fill="${strokeColor}"/>
          <!-- Bonde -->
          <circle cx="85" cy="50" r="3" fill="${strokeColor}"/>
        </svg>
      `,

      // ============ ÉLECTROMÉNAGER AVANCÉ ============
      'washer-dryer': `
        <svg viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
          <!-- Corps principal -->
          <rect x="5" y="5" width="110" height="110" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
          <!-- Hublot -->
          <circle cx="60" cy="65" r="30" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}" stroke-width="2"/>
          <circle cx="60" cy="65" r="25" fill="${this.lightenColor(fillColor)}"/>
          <!-- Panneau de contrôle -->
          <rect x="20" y="15" width="80" height="25" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}"/>
          <!-- Boutons -->
          <circle cx="35" cy="27" r="4" fill="${strokeColor}"/>
          <circle cx="50" cy="27" r="3" fill="${strokeColor}"/>
          <circle cx="65" cy="27" r="3" fill="${strokeColor}"/>
          <circle cx="80" cy="27" r="3" fill="${strokeColor}"/>
          <!-- Écran -->
          <rect x="85" y="22" width="12" height="8" fill="#000" stroke="${strokeColor}"/>
        </svg>
      `,

      'refrigerator': `
        <svg viewBox="0 0 60 120" xmlns="http://www.w3.org/2000/svg">
          <!-- Corps principal -->
          <rect x="5" y="5" width="50" height="110" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
          <!-- Compartiment congélateur -->
          <rect x="8" y="8" width="44" height="35" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}"/>
          <!-- Compartiment réfrigérateur -->
          <rect x="8" y="48" width="44" height="64" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}"/>
          <!-- Poignées -->
          <rect x="47" y="20" width="3" height="15" rx="1" fill="${strokeColor}"/>
          <rect x="47" y="70" width="3" height="15" rx="1" fill="${strokeColor}"/>
          <!-- Détails -->
          <rect x="12" y="55" width="36" height="2" fill="${this.darkenColor(fillColor)}"/>
          <rect x="12" y="75" width="36" height="2" fill="${this.darkenColor(fillColor)}"/>
          <rect x="12" y="95" width="36" height="2" fill="${this.darkenColor(fillColor)}"/>
        </svg>
      `,
      // ============ ÉLECTROMÉNAGER ============
'ceiling-fan': `
  <svg viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">
    <!-- Centre moteur -->
    <circle cx="40" cy="40" r="6" fill="${strokeColor}"/>
    <!-- Pales -->
    <path d="M 40 40 L 10 30 Q 4 40 10 50 L 40 40" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <path d="M 40 40 L 70 30 Q 76 40 70 50 L 40 40" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <path d="M 40 40 L 50 10 Q 40 4 30 10 L 40 40" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <path d="M 40 40 L 50 70 Q 40 76 30 70 L 40 40" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
  </svg>
`,

      'dishwasher': `
  <svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Corps principal -->
    <rect x="4" y="4" width="52" height="52" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Panneau de contrôle -->
    <rect x="8" y="8" width="44" height="6" fill="${this.darkenColor(fillColor)}"/>
    <!-- Voyant -->
    <circle cx="48" cy="11" r="2" fill="#fff"/>
    <!-- Grilles intérieures -->
    <g stroke="${strokeColor}" stroke-width="1">
      <line x1="12" y1="20" x2="48" y2="20"/>
      <line x1="12" y1="28" x2="48" y2="28"/>
      <line x1="12" y1="36" x2="48" y2="36"/>
    </g>
  </svg>
`,

      'oven': `
  <svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Corps principal -->
    <rect x="4" y="4" width="52" height="52" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Four -->
    <rect x="8" y="8" width="44" height="24" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="1"/>
    <!-- Plaques -->
    <circle cx="16" cy="44" r="4" fill="none" stroke="${strokeColor}" stroke-width="1"/>
    <circle cx="30" cy="44" r="4" fill="none" stroke="${strokeColor}" stroke-width="1"/>
    <circle cx="44" cy="44" r="4" fill="none" stroke="${strokeColor}" stroke-width="1"/>
    <!-- Bouton -->
    <circle cx="52" cy="12" r="2" fill="${strokeColor}"/>
  </svg>
`,

      'washing-machine': `
  <svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Corps principal -->
    <rect x="4" y="4" width="52" height="52" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Tambour -->
    <circle cx="30" cy="34" r="16" fill="none" stroke="${strokeColor}" stroke-width="4"/>
    <circle cx="30" cy="34" r="8" fill="none" stroke="${strokeColor}" stroke-width="2"/>
    <!-- Boutons de contrôle -->
    <circle cx="12" cy="12" r="3" fill="${strokeColor}"/>
    <circle cx="22" cy="12" r="3" fill="${this.darkenColor(fillColor)}"/>
  </svg>
`,

      'microwave': `
  <svg viewBox="0 0 70 50" xmlns="http://www.w3.org/2000/svg">
    <!-- Corps principal -->
    <rect x="4" y="10" width="62" height="36" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Fenêtre -->
    <rect x="8" y="14" width="40" height="28" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="1"/>
    <!-- Panneau de contrôle -->
    <circle cx="56" cy="20" r="2" fill="${strokeColor}"/>
    <circle cx="56" cy="28" r="2" fill="${strokeColor}"/>
    <circle cx="56" cy="36" r="2" fill="${strokeColor}"/>
    <!-- Plateau tournant -->
    <circle cx="24" cy="28" r="1" fill="${this.darkenColor(fillColor)}"/>
  </svg>
`,

      // ============ SALLE DE BAIN ============
      'sink-simple': `
  <svg viewBox="0 0 60 50" xmlns="http://www.w3.org/2000/svg">
    <!-- Bassin -->
    <ellipse cx="30" cy="30" rx="24" ry="16" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Robinet -->
    <circle cx="30" cy="24" r="2" fill="${strokeColor}"/>
    <line x1="30" y1="10" x2="30" y2="24" stroke="${strokeColor}" stroke-width="4"/>
  </svg>
`,

      'sink-double': `
  <svg viewBox="0 0 80 50" xmlns="http://www.w3.org/2000/svg">
    <!-- Bassin gauche -->
    <ellipse cx="24" cy="30" rx="20" ry="12" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Bassin droit -->
    <ellipse cx="56" cy="30" rx="20" ry="12" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Robinets -->
    <circle cx="24" cy="24" r="2" fill="${strokeColor}"/>
    <circle cx="56" cy="24" r="2" fill="${strokeColor}"/>
    <line x1="24" y1="10" x2="24" y2="24" stroke="${strokeColor}" stroke-width="2"/>
    <line x1="56" y1="10" x2="56" y2="24" stroke="${strokeColor}" stroke-width="2"/>
  </svg>
`,



      'shower': `
  <svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Receveur -->
    <rect x="10" y="10" width="40" height="40" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Pommeau -->
    <circle cx="30" cy="16" r="4" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}" stroke-width="2"/>
    <!-- Gouttes d'eau -->
    <g stroke="${this.darkenColor(fillColor)}" stroke-width="1">
      <line x1="26" y1="24" x2="26" y2="28"/>
      <line x1="30" y1="24" x2="30" y2="28"/>
      <line x1="34" y1="24" x2="34" y2="28"/>
      <line x1="26" y1="32" x2="26" y2="36"/>
      <line x1="30" y1="32" x2="30" y2="36"/>
      <line x1="34" y1="32" x2="34" y2="36"/>
    </g>
  </svg>
`,



      'urinal': `
  <svg viewBox="0 0 40 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Corps -->
    <path d="M 10 16 Q 10 10 16 10 L 24 10 Q 30 10 30 16 L 30 40 Q 30 46 24 46 L 16 46 Q 10 46 10 40 Z" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Bouton chasse -->
    <circle cx="20" cy="6" r="2" fill="${strokeColor}"/>
  </svg>
`,

      // ============ CHAMBRE ============
      'bed-single': `
  <svg viewBox="0 0 70 50" xmlns="http://www.w3.org/2000/svg">
    <!-- Matelas -->
    <rect x="6" y="10" width="58" height="36" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Tête de lit -->
    <rect x="6" y="10" width="58" height="12" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="1"/>
    <!-- Séparation -->
    <line x1="6" y1="22" x2="64" y2="22" stroke="${strokeColor}" stroke-width="2"/>
  </svg>
`,

      'bed-double': `
  <svg viewBox="0 0 80 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Matelas -->
    <rect x="4" y="10" width="72" height="44" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Tête de lit -->
    <rect x="4" y="10" width="72" height="16" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="1"/>
    <!-- Séparation -->
    <line x1="4" y1="26" x2="76" y2="26" stroke="${strokeColor}" stroke-width="2"/>
    <!-- Division du matelas -->
    <line x1="40" y1="26" x2="40" y2="54" stroke="${strokeColor}" stroke-width="1"/>
  </svg>
`,

      'nightstand': `
  <svg viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
    <!-- Corps -->
    <rect x="6" y="6" width="28" height="28" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Tiroir -->
    <rect x="10" y="10" width="20" height="6" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="1"/>
    <!-- Poignée -->
    <circle cx="28" cy="13" r="1" fill="${strokeColor}"/>
  </svg>
`,

      'wardrobe': `
  <svg viewBox="0 0 50 70" xmlns="http://www.w3.org/2000/svg">
    <!-- Corps -->
    <rect x="4" y="4" width="42" height="62" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Séparation centrale -->
    <line x1="25" y1="4" x2="25" y2="66" stroke="${strokeColor}" stroke-width="2"/>
    <!-- Poignées -->
    <circle cx="16" cy="30" r="1" fill="${strokeColor}"/>
    <circle cx="34" cy="30" r="1" fill="${strokeColor}"/>
  </svg>
`,

      'dresser': `
  <svg viewBox="0 0 60 40" xmlns="http://www.w3.org/2000/svg">
    <!-- Corps -->
    <rect x="4" y="4" width="52" height="32" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Tiroirs -->
    <line x1="4" y1="16" x2="56" y2="16" stroke="${strokeColor}" stroke-width="1"/>
    <line x1="4" y1="28" x2="56" y2="28" stroke="${strokeColor}" stroke-width="1"/>
    <!-- Poignées -->
    <circle cx="50" cy="10" r="1" fill="${strokeColor}"/>
    <circle cx="50" cy="22" r="1" fill="${strokeColor}"/>
    <circle cx="50" cy="32" r="1" fill="${strokeColor}"/>
  </svg>
`,

      'plant': `
  <svg viewBox="0 0 40 50" xmlns="http://www.w3.org/2000/svg">
    <!-- Pot -->
    <rect x="14" y="36" width="12" height="10" fill="#8B4513" stroke="${strokeColor}" stroke-width="2"/>
    <!-- Feuillage principal -->
    <ellipse cx="20" cy="24" rx="12" ry="16" fill="#228B22"/>
    <!-- Feuillages secondaires -->
    <path d="M 8 20 Q 20 10 32 20" fill="#32CD32"/>
    <path d="M 12 30 Q 20 16 28 30" fill="#90EE90"/>
  </svg>
`,

      // ============ STRUCTURE BÂTIMENT ============
      'spiral-stair': `
  <svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Base circulaire -->
    <circle cx="30" cy="30" r="24" fill="none" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Spirale -->
    <path d="M 30 6 A 24 24 0 0 1 54 30" fill="none" stroke="${strokeColor}" stroke-width="6"/>
    <!-- Marches -->
    <g stroke="${strokeColor}" stroke-width="2">
      <line x1="30" y1="14" x2="46" y2="30"/>
      <line x1="30" y1="20" x2="40" y2="34"/>
      <line x1="30" y1="26" x2="34" y2="38"/>
    </g>
  </svg>
`,

      'elevator': `
  <svg viewBox="0 0 50 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Gaine -->
    <rect x="4" y="4" width="42" height="52" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Cabine -->
    <rect x="8" y="8" width="34" height="44" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="1"/>
    <!-- Portes -->
    <rect x="12" y="12" width="12" height="16" fill="none" stroke="${strokeColor}" stroke-width="4"/>
    <rect x="26" y="12" width="12" height="16" fill="none" stroke="${strokeColor}" stroke-width="4"/>
    <!-- Boutons -->
    <circle cx="18" cy="36" r="2" fill="${strokeColor}"/>
    <circle cx="32" cy="36" r="2" fill="${strokeColor}"/>
  </svg>
`,

      'structural-column': `
  <svg viewBox="0 0 30 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Colonne -->
    <rect x="8" y="4" width="14" height="52" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Chapiteau -->
    <rect x="4" y="4" width="22" height="6" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="2"/>
    <!-- Base -->
    <rect x="4" y="50" width="22" height="6" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="2"/>
  </svg>
`,

      'beam': `
  <svg viewBox="0 0 80 24" xmlns="http://www.w3.org/2000/svg">
    <!-- Poutre principale -->
    <rect x="4" y="8" width="72" height="8" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Supports -->
    <rect x="4" y="4" width="6" height="16" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="2"/>
    <rect x="70" y="4" width="6" height="16" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="2"/>
  </svg>
`,

      'technical-shaft': `
  <svg viewBox="0 0 40 50" xmlns="http://www.w3.org/2000/svg">
    <!-- Gaine -->
    <rect x="4" y="4" width="32" height="42" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Étagements -->
    <g stroke="${strokeColor}" stroke-width="1">
      <line x1="4" y1="12" x2="36" y2="12"/>
      <line x1="4" y1="20" x2="36" y2="20"/>
      <line x1="4" y1="28" x2="36" y2="28"/>
      <line x1="4" y1="36" x2="36" y2="36"/>
    </g>
    <!-- Label -->
    <text x="20" y="30" text-anchor="middle" font-family="Arial" font-size="8" fill="${strokeColor}">GT</text>
  </svg>
`,

      'ventilation-duct': `
  <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg">
    <!-- Conduit -->
    <rect x="4" y="10" width="52" height="10" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Grilles -->
    <g stroke="${strokeColor}" stroke-width="1">
      <line x1="10" y1="10" x2="10" y2="20"/>
      <line x1="20" y1="10" x2="20" y2="20"/>
      <line x1="30" y1="10" x2="30" y2="20"/>
      <line x1="40" y1="10" x2="40" y2="20"/>
      <line x1="50" y1="10" x2="50" y2="20"/>
    </g>
    <!-- Flèche de direction -->
    <path d="M 56 15 L 64 15 M 60 11 L 64 15 L 60 19" stroke="${strokeColor}" stroke-width="2" fill="none"/>
  </svg>
`,

      // ============ ARMOIRES ET BIBLIOTHÈQUES ============
      'simple-cabinet': `
  <svg viewBox="0 0 40 70" xmlns="http://www.w3.org/2000/svg">
    <!-- Corps -->
    <rect x="4" y="4" width="32" height="62" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Poignée -->
    <circle cx="32" cy="34" r="1" fill="${strokeColor}"/>
  </svg>
`,

      'corner-cabinet': `
  <svg viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg">
    <!-- Corps en angle -->
    <path d="M 4 4 L 4 46 L 24 46 L 46 24 L 46 4 Z" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Ligne de séparation -->
    <line x1="4" y1="4" x2="46" y2="46" stroke="${strokeColor}" stroke-width="1"/>
    <!-- Poignée -->
    <circle cx="36" cy="14" r="1" fill="${strokeColor}"/>
  </svg>
`,

      'bookshelf': `
  <svg viewBox="0 0 50 70" xmlns="http://www.w3.org/2000/svg">
    <!-- Corps -->
    <rect x="4" y="4" width="42" height="62" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Étagères -->
    <g stroke="${strokeColor}" stroke-width="2">
      <line x1="4" y1="16" x2="46" y2="16"/>
      <line x1="4" y1="28" x2="46" y2="28"/>
      <line x1="4" y1="40" x2="46" y2="40"/>
      <line x1="4" y1="52" x2="46" y2="52"/>
    </g>
  </svg>
`,

      'tv-stand': `
  <svg viewBox="0 0 70 30" xmlns="http://www.w3.org/2000/svg">
    <!-- Corps principal -->
    <rect x="4" y="10" width="62" height="16" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Compartiment TV -->
    <rect x="8" y="14" width="24" height="8" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="1"/>
    <!-- Compartiments -->
    <rect x="36" y="14" width="12" height="8" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="1"/>
    <rect x="52" y="14" width="12" height="8" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="1"/>
  </svg>
`,

      'display-case': `
  <svg viewBox="0 0 50 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Corps -->
    <rect x="4" y="4" width="42" height="52" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Vitrine -->
    <rect x="8" y="8" width="34" height="20" fill="#e6f3ff" stroke="${strokeColor}" stroke-width="1"/>
    <!-- Séparation -->
    <line x1="4" y1="28" x2="46" y2="28" stroke="${strokeColor}" stroke-width="2"/>
    <!-- Poignées -->
    <circle cx="40" cy="18" r="1" fill="${strokeColor}"/>
    <circle cx="40" cy="42" r="1" fill="${strokeColor}"/>
  </svg>
`,

      'modular-storage': `
  <svg viewBox="0 0 60 50" xmlns="http://www.w3.org/2000/svg">
    <!-- Module gauche -->
    <rect x="4" y="4" width="24" height="42" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Module droit haut -->
    <rect x="32" y="4" width="24" height="20" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Module droit bas -->
    <rect x="32" y="28" width="24" height="18" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Séparation -->
    <line x1="4" y1="24" x2="28" y2="24" stroke="${strokeColor}" stroke-width="1"/>
  </svg>
`,

      // ============ TAPIS ET REVÊTEMENTS ============
      'round-carpet-rosette': `
  <svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Tapis principal -->
    <circle cx="30" cy="30" r="24" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Motifs concentriques -->
    <circle cx="30" cy="30" r="16" fill="none" stroke="${this.darkenColor(fillColor)}" stroke-width="2"/>
    <circle cx="30" cy="30" r="8" fill="none" stroke="${this.darkenColor(fillColor)}" stroke-width="1"/>
    <!-- Motifs radiaux -->
    <g stroke="${this.darkenColor(fillColor)}" stroke-width="1">
      <line x1="30" y1="6" x2="30" y2="54"/>
      <line x1="6" y1="30" x2="54" y2="30"/>
      <line x1="15" y1="15" x2="45" y2="45"/>
      <line x1="45" y1="15" x2="15" y2="45"/>
    </g>
  </svg>
`,

      'rectangular-carpet': `
  <svg viewBox="0 0 70 50" xmlns="http://www.w3.org/2000/svg">
    <!-- Tapis principal -->
    <rect x="4" y="4" width="62" height="42" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="${strokeWidth}" rx="4"/>
    <!-- Bordures -->
    <rect x="8" y="8" width="54" height="34" fill="none" stroke="${this.darkenColor(fillColor)}" stroke-width="2"/>
    <rect x="12" y="12" width="46" height="26" fill="none" stroke="${this.darkenColor(fillColor)}" stroke-width="1"/>
  </svg>
`,

      'diamond-pattern-carpet': `
  <svg viewBox="0 0 60 50" xmlns="http://www.w3.org/2000/svg">
    <!-- Tapis base -->
    <rect x="4" y="4" width="52" height="42" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Motifs losanges -->
    <g stroke="${this.darkenColor(fillColor)}" stroke-width="1" fill="none">
      <path d="M 16 12 L 30 24 L 44 12 L 30 24 L 16 36 L 30 24 L 44 36"/>
      <path d="M 30 4 L 44 16 L 30 28 L 16 16 Z"/>
      <path d="M 30 22 L 44 34 L 30 46 L 16 34 Z"/>
    </g>
  </svg>
`,

      'floor-covering': `
  <svg viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg">
    <!-- Base -->
    <rect x="4" y="4" width="42" height="42" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Quadrillage -->
    <g stroke="${this.darkenColor(fillColor)}" stroke-width="1">
      <line x1="4" y1="12" x2="46" y2="12"/>
      <line x1="4" y1="20" x2="46" y2="20"/>
      <line x1="4" y1="28" x2="46" y2="28"/>
      <line x1="4" y1="36" x2="46" y2="36"/>
      <line x1="12" y1="4" x2="12" y2="46"/>
      <line x1="20" y1="4" x2="20" y2="46"/>
      <line x1="28" y1="4" x2="28" y2="46"/>
      <line x1="36" y1="4" x2="36" y2="46"/>
    </g>
  </svg>
`,

      'doormat': `
  <svg viewBox="0 0 60 30" xmlns="http://www.w3.org/2000/svg">
    <!-- Paillasson -->
    <rect x="4" y="8" width="52" height="14" fill="#8B4513" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Fibres -->
    <g stroke="#654321" stroke-width="1">
      <line x1="8" y1="12" x2="52" y2="12"/>
      <line x1="8" y1="16" x2="52" y2="16"/>
      <line x1="8" y1="20" x2="52" y2="20"/>
    </g>
  </svg>
`,

      // ============ JARDIN ============
      'swimming-pool': `
  <svg viewBox="0 0 70 50" xmlns="http://www.w3.org/2000/svg">
    <!-- Piscine -->
    <ellipse cx="35" cy="25" rx="30" ry="20" fill="#87CEEB" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Bordure intérieure -->
    <ellipse cx="35" cy="25" rx="24" ry="14" fill="none" stroke="#4682B4" stroke-width="2"/>
    <!-- Ondulations -->
    <g stroke="#4682B4" stroke-width="1">
      <path d="M 10 24 Q 35 16 60 24"/>
      <path d="M 10 32 Q 35 24 60 32"/>
    </g>
  </svg>
`,

      'fountain': `
  <svg viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg">
    <!-- Bassin -->
    <circle cx="25" cy="34" r="16" fill="#87CEEB" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Centre -->
    <circle cx="25" cy="34" r="6" fill="#4682B4" stroke="${strokeColor}" stroke-width="2"/>
    <!-- Colonne -->
    <rect x="22" y="10" width="6" height="24" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}" stroke-width="2"/>
    <!-- Jets d'eau -->
    <g stroke="#4682B4" stroke-width="1">
      <line x1="23" y1="14" x2="23" y2="18"/>
      <line x1="25" y1="12" x2="25" y2="16"/>
      <line x1="27" y1="14" x2="27" y2="18"/>
    </g>
  </svg>
`,

      'tree': `
  <svg viewBox="0 0 40 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Tronc -->
    <rect x="16" y="40" width="8" height="16" fill="#8B4513" stroke="${strokeColor}" stroke-width="2"/>
    <!-- Feuillage principal -->
    <circle cx="20" cy="30" r="16" fill="#228B22"/>
    <!-- Feuillage moyen -->
    <circle cx="20" cy="24" r="10" fill="#32CD32"/>
    <!-- Feuillage supérieur -->
    <circle cx="20" cy="16" r="6" fill="#90EE90"/>
  </svg>
`,

      'bush': `
  <svg viewBox="0 0 50 40" xmlns="http://www.w3.org/2000/svg">
    <!-- Base -->
    <ellipse cx="25" cy="30" rx="20" ry="12" fill="#228B22"/>
    <!-- Parties latérales -->
    <ellipse cx="16" cy="24" rx="12" ry="8" fill="#32CD32"/>
    <ellipse cx="34" cy="24" rx="12" ry="8" fill="#32CD32"/>
    <!-- Sommet -->
    <ellipse cx="25" cy="20" rx="8" ry="6" fill="#90EE90"/>
  </svg>
`,

      'wooden-deck': `
  <svg viewBox="0 0 60 50" xmlns="http://www.w3.org/2000/svg">
    <!-- Base -->
    <rect x="4" y="4" width="52" height="42" fill="#DEB887" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Planches -->
    <g stroke="#8B7355" stroke-width="2">
      <line x1="4" y1="12" x2="56" y2="12"/>
      <line x1="4" y1="20" x2="56" y2="20"/>
      <line x1="4" y1="28" x2="56" y2="28"/>
      <line x1="4" y1="36" x2="56" y2="36"/>
    </g>
  </svg>
`,

      'gazebo': `
  <svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Base circulaire -->
    <circle cx="30" cy="30" r="24" fill="none" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Plancher -->
    <circle cx="30" cy="30" r="16" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="2"/>
    <!-- Structure -->
    <g stroke="#8B4513" stroke-width="4">
      <line x1="30" y1="14" x2="30" y2="46"/>
      <line x1="14" y1="30" x2="46" y2="30"/>
      <line x1="19" y1="19" x2="41" y2="41"/>
      <line x1="41" y1="19" x2="19" y2="41"/>
    </g>
    <!-- Centre -->
    <circle cx="30" cy="30" r="4" fill="#8B4513"/>
  </svg>
`,

      'flower-bed': `
  <svg viewBox="0 0 60 40" xmlns="http://www.w3.org/2000/svg">
    <!-- Terre -->
    <ellipse cx="30" cy="30" rx="24" ry="12" fill="#8B4513" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Fleurs roses -->
    <g fill="#FF69B4">
      <circle cx="16" cy="24" r="3"/>
      <circle cx="30" cy="20" r="3"/>
      <circle cx="44" cy="24" r="3"/>
    </g>
    <!-- Fleurs jaunes -->
    <g fill="#FFD700">
      <circle cx="22" cy="30" r="2"/>
      <circle cx="38" cy="30" r="2"/>
    </g>
    <!-- Tiges -->
    <g stroke="#228B22" stroke-width="2">
      <line x1="16" y1="24" x2="16" y2="32"/>
      <line x1="30" y1="20" x2="30" y2="32"/>
      <line x1="44" y1="24" x2="44" y2="32"/>
    </g>
  </svg>
`,

      'garden-path': `
  <svg viewBox="0 0 70 40" xmlns="http://www.w3.org/2000/svg">
    <!-- Chemin -->
    <path d="M 4 36 Q 20 4 36 20 Q 52 36 66 4" fill="none" stroke="#A0522D" stroke-width="8"/>
    <!-- Pierres -->
    <g fill="#696969">
      <circle cx="12" cy="28" r="2"/>
      <circle cx="24" cy="12" r="2"/>
      <circle cx="36" cy="20" r="2"/>
      <circle cx="48" cy="28" r="2"/>
      <circle cx="60" cy="12" r="2"/>
    </g>
  </svg>
`,

      'garden-bench': `
  <svg viewBox="0 0 60 40" xmlns="http://www.w3.org/2000/svg">
    <!-- Assise -->
    <rect x="8" y="16" width="44" height="6" fill="#8B4513" stroke="${strokeColor}" stroke-width="2"/>
    <!-- Dossier -->
    <rect x="8" y="10" width="44" height="6" fill="#A0522D" stroke="${strokeColor}" stroke-width="2"/>
    <!-- Pieds -->
    <g stroke="#8B4513" stroke-width="4">
      <line x1="12" y1="16" x2="12" y2="32"/>
      <line x1="48" y1="16" x2="48" y2="32"/>
      <line x1="8" y1="10" x2="8" y2="22"/>
      <line x1="52" y1="10" x2="52" y2="22"/>
    </g>
  </svg>
`,

      'garden-lighting': `
  <svg viewBox="0 0 30 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Poteau -->
    <line x1="15" y1="10" x2="15" y2="50" stroke="${strokeColor}" stroke-width="4"/>
    <!-- Luminaire -->
    <circle cx="15" cy="10" r="6" fill="#FFD700" stroke="${strokeColor}" stroke-width="2"/>
    <!-- Rayons lumineux -->
    <g stroke="#FFD700" stroke-width="1" opacity="0.6">
      <line x1="8" y1="16" x2="4" y2="20"/>
      <line x1="22" y1="16" x2="26" y2="20"/>
      <line x1="15" y1="2" x2="15" y2="0"/>
      <line x1="4" y1="10" x2="1" y2="10"/>
      <line x1="26" y1="10" x2="29" y2="10"/>
    </g>
    <!-- Base -->
    <rect x="12" y="50" width="6" height="6" fill="${this.darkenColor(fillColor)}"/>
  </svg>
`,

      // ============ ÉLÉMENTS SUPPLÉMENTAIRES ============
      'arc': `
  <svg viewBox="0 0 100 80" xmlns="http://www.w3.org/2000/svg">
    <!-- Arc -->
    <path d="M 20 70 Q 50 10 80 70" fill="none" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
  </svg>
`,

      'circle': `
  <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
    <!-- Cercle -->
    <circle cx="50" cy="50" r="30" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
  </svg>
`,

      'hatched-area': `
  <svg viewBox="0 0 100 80" xmlns="http://www.w3.org/2000/svg">
    <!-- Zone -->
    <rect x="20" y="20" width="60" height="40" fill="none" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Hachures -->
    <g stroke="${strokeColor}" stroke-width="1">
      <line x1="20" y1="26" x2="80" y2="26"/>
      <line x1="20" y1="32" x2="80" y2="32"/>
      <line x1="20" y1="38" x2="80" y2="38"/>
      <line x1="20" y1="44" x2="80" y2="44"/>
      <line x1="20" y1="50" x2="80" y2="50"/>
      <line x1="20" y1="56" x2="80" y2="56"/>
    </g>
  </svg>
`,

      'reference-cross': `
  <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
    <!-- Croix principale -->
    <line x1="30" y1="50" x2="70" y2="50" stroke="${strokeColor}" stroke-width="4"/>
    <line x1="50" y1="30" x2="50" y2="70" stroke="${strokeColor}" stroke-width="4"/>
    <!-- Diagonales -->
    <line x1="30" y1="30" x2="70" y2="70" stroke="${strokeColor}" stroke-width="2"/>
    <line x1="70" y1="30" x2="30" y2="70" stroke="${strokeColor}" stroke-width="2"/>
  </svg>
`,

      // ============ PORTES ET FENÊTRES ============
      'simple-door': `
  <svg viewBox="0 0 100 80" xmlns="http://www.w3.org/2000/svg">
    <!-- Montants -->
    <rect x="20" y="10" width="6" height="60" fill="${strokeColor}"/>
    <rect x="74" y="10" width="6" height="60" fill="${strokeColor}"/>
    <!-- Arc d'ouverture -->
    <path d="M 26 70 Q 50 30 74 70" fill="none" stroke="${strokeColor}" stroke-width="4"/>
  </svg>
`,

      'double-door': `
  <svg viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
    <!-- Montants -->
    <rect x="20" y="10" width="6" height="60" fill="${strokeColor}"/>
    <rect x="94" y="10" width="6" height="60" fill="${strokeColor}"/>
    <!-- Arcs d'ouverture -->
    <path d="M 26 70 Q 40 30 54 70" fill="none" stroke="${strokeColor}" stroke-width="4"/>
    <path d="M 66 70 Q 80 30 94 70" fill="none" stroke="${strokeColor}" stroke-width="4"/>
  </svg>
`,

      'sliding-door': `
  <svg viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
    <!-- Rails -->
    <rect x="20" y="10" width="80" height="6" fill="${strokeColor}"/>
    <rect x="20" y="64" width="80" height="6" fill="${strokeColor}"/>
    <!-- Panneaux -->
    <rect x="30" y="16" width="30" height="48" fill="none" stroke="${strokeColor}" stroke-width="4"/>
    <rect x="60" y="16" width="30" height="48" fill="none" stroke="${strokeColor}" stroke-width="4"/>
    <!-- Flèche de mouvement -->
    <path d="M 70 40 L 80 40 M 76 36 L 80 40 L 76 44" stroke="${strokeColor}" stroke-width="3" fill="none"/>
  </svg>
`,

      'simple-window': `
  <svg viewBox="0 0 100 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Cadre -->
    <rect x="20" y="24" width="60" height="36" fill="none" stroke="${strokeColor}" stroke-width="6"/>
    <!-- Croisillons -->
    <line x1="50" y1="24" x2="50" y2="60" stroke="${strokeColor}" stroke-width="2"/>
    <line x1="20" y1="42" x2="80" y2="42" stroke="${strokeColor}" stroke-width="2"/>
  </svg>
`,

      'hinged-window': `
  <svg viewBox="0 0 100 80" xmlns="http://www.w3.org/2000/svg">
    <!-- Cadre -->
    <rect x="20" y="20" width="50" height="40" fill="none" stroke="${strokeColor}" stroke-width="4"/>
    <!-- Arc d'ouverture -->
    <path d="M 70 20 L 84 30 L 70 60" fill="none" stroke="${strokeColor}" stroke-width="4"/>
    <!-- Charnière -->
    <line x1="70" y1="20" x2="70" y2="60" stroke="${strokeColor}" stroke-width="2"/>
  </svg>
`,

      'round-window': `
  <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
    <!-- Cadre circulaire -->
    <circle cx="50" cy="50" r="24" fill="none" stroke="${strokeColor}" stroke-width="4"/>
    <!-- Croisillons -->
    <line x1="26" y1="50" x2="74" y2="50" stroke="${strokeColor}" stroke-width="2"/>
    <line x1="50" y1="26" x2="50" y2="74" stroke="${strokeColor}" stroke-width="2"/>
    <!-- Indication d'ouverture -->
    <line x1="50" y1="50" x2="74" y2="50" stroke="${strokeColor}" stroke-width="4"/>
  </svg>
`,

      'staircase': `
  <svg viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
    <!-- Marches -->
    <path d="M 10 70 L 26 70 L 26 60 L 42 60 L 42 50 L 58 50 L 58 40 L 74 40 L 74 30 L 90 30 L 90 20 L 106 20 L 106 10" fill="none" stroke="${strokeColor}" stroke-width="4"/>
    <!-- Ligne de projection -->
    <path d="M 10 70 L 90 20" stroke="${strokeColor}" stroke-width="1" stroke-dasharray="4,4"/>
  </svg>
`,

      'ramp': `
  <svg viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
    <!-- Rampe -->
    <path d="M 10 70 Q 60 30 110 10" fill="none" stroke="${strokeColor}" stroke-width="6"/>
    <!-- Garde-corps -->
    <g stroke="${strokeColor}" stroke-width="2">
      <line x1="24" y1="60" x2="24" y2="70"/>
      <line x1="50" y1="44" x2="50" y2="54"/>
      <line x1="76" y1="28" x2="76" y2="38"/>
      <line x1="96" y1="18" x2="96" y2="28"/>
    </g>
  </svg>
`,

      // ============ MURS ET STRUCTURES ============
      'simple-wall': `
  <svg viewBox="0 0 100 80" xmlns="http://www.w3.org/2000/svg">
    <!-- Mur -->
    <rect x="10" y="10" width="80" height="60" fill="none" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
  </svg>
`,

      'l-wall': `
  <svg viewBox="0 0 100 80" xmlns="http://www.w3.org/2000/svg">
    <!-- Mur en L -->
    <path d="M 20 10 L 80 10 L 80 40 L 50 40 L 50 70 L 20 70 Z" fill="none" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
  </svg>
`,

      't-wall': `
  <svg viewBox="0 0 100 80" xmlns="http://www.w3.org/2000/svg">
    <!-- Mur en T -->
    <path d="M 10 10 L 90 10 L 90 30 L 60 30 L 60 70 L 40 70 L 40 30 L 10 30 Z" fill="none" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
  </svg>
`,

      'wall-openings': `
  <svg viewBox="0 0 100 80" xmlns="http://www.w3.org/2000/svg">
    <!-- Mur principal -->
    <rect x="10" y="10" width="80" height="60" fill="none" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Ouvertures -->
    <rect x="20" y="10" width="16" height="60" fill="white" stroke="${strokeColor}" stroke-width="2"/>
    <rect x="42" y="10" width="16" height="60" fill="white" stroke="${strokeColor}" stroke-width="2"/>
    <rect x="64" y="10" width="16" height="60" fill="white" stroke="${strokeColor}" stroke-width="2"/>
  </svg>
`,

      'column': `
  <svg viewBox="0 0 40 100" xmlns="http://www.w3.org/2000/svg">
    <!-- Colonne -->
    <line x1="20" y1="10" x2="20" y2="90" stroke="${strokeColor}" stroke-width="8"/>
  </svg>
`,

      'beam-horizontal': `
  <svg viewBox="0 0 100 40" xmlns="http://www.w3.org/2000/svg">
    <!-- Poutre -->
    <line x1="10" y1="20" x2="90" y2="20" stroke="${strokeColor}" stroke-width="8"/>
  </svg>
`,
'kitchen-cabinet-simple': `
  <svg viewBox="0 0 80 40" xmlns="http://www.w3.org/2000/svg">
    <!-- Meuble de base -->
    <rect x="5" y="5" width="70" height="30" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Porte -->
    <rect x="8" y="8" width="64" height="24" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="1"/>
    <!-- Poignée -->
    <circle cx="65" cy="20" r="1.5" fill="${strokeColor}"/>
  </svg>
`,

'kitchen-cabinet-corner-left': `
  <svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Meuble d'angle gauche -->
    <path d="M 5 5 L 5 55 L 35 55 L 55 35 L 55 5 Z" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Ligne de séparation -->
    <line x1="5" y1="5" x2="55" y2="55" stroke="${strokeColor}" stroke-width="1"/>
    <!-- Poignée -->
    <circle cx="45" cy="15" r="1.5" fill="${strokeColor}"/>
  </svg>
`,
'kitchen-cabinet-corner-right': `
  <svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Meuble d'angle droit -->
    <path d="M 55 5 L 55 55 L 25 55 L 5 35 L 5 5 Z" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Ligne de séparation -->
    <line x1="55" y1="5" x2="5" y2="55" stroke="${strokeColor}" stroke-width="1"/>
    <!-- Poignée -->
    <circle cx="15" cy="15" r="1.5" fill="${strokeColor}"/>
  </svg>
`,
'kitchen-counter-l-left': `
  <svg viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">
    <!-- Plan de travail en L gauche -->
    <path d="M 5 5 L 75 5 L 75 35 L 35 35 L 35 75 L 5 75 Z" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Bordure -->
    <path d="M 5 5 L 75 5 L 75 35 L 35 35 L 35 75 L 5 75 Z" fill="none" stroke="${this.darkenColor(fillColor)}" stroke-width="2"/>
  </svg>
`,
'kitchen-counter-l-right': `
  <svg viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">
    <!-- Plan de travail en L droit -->
    <path d="M 75 5 L 5 5 L 5 35 L 45 35 L 45 75 L 75 75 Z" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Bordure -->
    <path d="M 75 5 L 5 5 L 5 35 L 45 35 L 45 75 L 75 75 Z" fill="none" stroke="${this.darkenColor(fillColor)}" stroke-width="2"/>
  </svg>
`,
'kitchen-sink-round': `
  <svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Évier rond -->
    <circle cx="30" cy="30" r="22" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Bord intérieur -->
    <circle cx="30" cy="30" r="18" fill="none" stroke="${this.darkenColor(fillColor)}" stroke-width="2"/>
    <!-- Robinet -->
    <circle cx="30" cy="20" r="2" fill="${strokeColor}"/>
    <line x1="30" y1="10" x2="30" y2="20" stroke="${strokeColor}" stroke-width="3"/>
  </svg>
`,
'kitchen-counter-straight': `
  <svg viewBox="0 0 100 40" xmlns="http://www.w3.org/2000/svg">
    <!-- Plan de travail droit -->
    <rect x="5" y="10" width="90" height="20" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Bordure -->
    <rect x="5" y="10" width="90" height="20" fill="none" stroke="${this.darkenColor(fillColor)}" stroke-width="2"/>
  </svg>
`,
'kitchen-island': `
  <svg viewBox="0 0 80 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Îlot central -->
    <rect x="10" y="10" width="60" height="40" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Plan de travail -->
    <rect x="8" y="8" width="64" height="44" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="2"/>
    <!-- Détails -->
    <rect x="15" y="15" width="20" height="10" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}" stroke-width="1"/>
    <rect x="45" y="15" width="20" height="10" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}" stroke-width="1"/>
  </svg>
`,
'kitchen-peninsula': `
  <svg viewBox="0 0 100 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Péninsule -->
    <path d="M 5 10 L 95 10 L 95 30 L 60 30 L 60 50 L 5 50 Z" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Plan de travail -->
    <path d="M 5 10 L 95 10 L 95 30 L 60 30 L 60 50 L 5 50 Z" fill="none" stroke="${this.darkenColor(fillColor)}" stroke-width="2"/>
  </svg>
`,
'dining-table-square': `
  <svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Table carrée -->
    <rect x="10" y="10" width="40" height="40" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Pieds -->
    <circle cx="18" cy="18" r="2" fill="${this.darkenColor(fillColor)}"/>
    <circle cx="42" cy="18" r="2" fill="${this.darkenColor(fillColor)}"/>
    <circle cx="18" cy="42" r="2" fill="${this.darkenColor(fillColor)}"/>
    <circle cx="42" cy="42" r="2" fill="${this.darkenColor(fillColor)}"/>
  </svg>
`,
'dining-table-round': `
  <svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Table ronde -->
    <circle cx="30" cy="30" r="22" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Pied central -->
    <circle cx="30" cy="30" r="8" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}" stroke-width="2"/>
  </svg>
`,
'dining-table-oval': `
  <svg viewBox="0 0 80 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Table ovale -->
    <ellipse cx="40" cy="30" rx="30" ry="20" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Pieds -->
    <ellipse cx="20" cy="25" rx="3" ry="2" fill="${this.darkenColor(fillColor)}"/>
    <ellipse cx="60" cy="25" rx="3" ry="2" fill="${this.darkenColor(fillColor)}"/>
    <ellipse cx="20" cy="35" rx="3" ry="2" fill="${this.darkenColor(fillColor)}"/>
    <ellipse cx="60" cy="35" rx="3" ry="2" fill="${this.darkenColor(fillColor)}"/>
  </svg>
`,
'dining-table-rectangular': `
  <svg viewBox="0 0 100 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Table rectangulaire -->
    <rect x="10" y="15" width="80" height="30" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Pieds -->
    <rect x="18" y="20" width="4" height="4" fill="${this.darkenColor(fillColor)}"/>
    <rect x="78" y="20" width="4" height="4" fill="${this.darkenColor(fillColor)}"/>
    <rect x="18" y="36" width="4" height="4" fill="${this.darkenColor(fillColor)}"/>
    <rect x="78" y="36" width="4" height="4" fill="${this.darkenColor(fillColor)}"/>
  </svg>
`,
'cooktop-2-burners': `
  <svg viewBox="0 0 60 40" xmlns="http://www.w3.org/2000/svg">
    <!-- Table de cuisson 2 feux -->
    <rect x="5" y="5" width="50" height="30" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Feux -->
    <circle cx="20" cy="20" r="6" fill="none" stroke="${strokeColor}" stroke-width="2"/>
    <circle cx="40" cy="20" r="6" fill="none" stroke="${strokeColor}" stroke-width="2"/>
    <circle cx="20" cy="20" r="3" fill="none" stroke="${strokeColor}" stroke-width="1"/>
    <circle cx="40" cy="20" r="3" fill="none" stroke="${strokeColor}" stroke-width="1"/>
  </svg>
`,
'cooktop-4-burners': `
  <svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
    <!-- Table de cuisson 4 feux -->
    <rect x="5" y="5" width="50" height="50" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Feux -->
    <circle cx="20" cy="20" r="5" fill="none" stroke="${strokeColor}" stroke-width="2"/>
    <circle cx="40" cy="20" r="5" fill="none" stroke="${strokeColor}" stroke-width="2"/>
    <circle cx="20" cy="40" r="5" fill="none" stroke="${strokeColor}" stroke-width="2"/>
    <circle cx="40" cy="40" r="5" fill="none" stroke="${strokeColor}" stroke-width="2"/>
  </svg>
`,
'kitchen-hood': `
  <svg viewBox="0 0 80 40" xmlns="http://www.w3.org/2000/svg">
    <!-- Hotte aspirante -->
    <rect x="10" y="8" width="60" height="24" fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Grille -->
    <rect x="15" y="12" width="50" height="16" fill="${this.darkenColor(fillColor)}" stroke="${strokeColor}" stroke-width="1"/>
    <!-- Lignes de ventilation -->
    <g stroke="${strokeColor}" stroke-width="1">
      <line x1="20" y1="16" x2="60" y2="16"/>
      <line x1="20" y1="20" x2="60" y2="20"/>
      <line x1="20" y1="24" x2="60" y2="24"/>
    </g>
  </svg>
`,

'sink': `
  <svg viewBox="0 0 80 50" xmlns="http://www.w3.org/2000/svg">
    <!-- Évier double -->
    <rect x="5" y="10" width="70" height="30" fill="${this.lightenColor(fillColor)}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
    <!-- Séparation -->
    <line x1="40" y1="10" x2="40" y2="40" stroke="${strokeColor}" stroke-width="2"/>
    <!-- Évacuations -->
    <circle cx="22" cy="32" r="2" fill="${this.darkenColor(fillColor)}"/>
    <circle cx="58" cy="32" r="2" fill="${this.darkenColor(fillColor)}"/>
    <!-- Robinet -->
    <circle cx="40" cy="5" r="2" fill="${strokeColor}"/>
  </svg>
`,


    };

    return svgTemplates[type] || '';
  }

  /**
   * Génère un aperçu SVG miniature pour l'icône (sans couleurs personnalisées)
   * @param type - Le type d'élément
   * @returns SVG optimisé pour affichage en icône
   */
  getSVGIcon(type: string): string {
    const iconTemplates: { [key: string]: string } = {
      'bureau': `
        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <rect x="2" y="8" width="20" height="10" rx="1" fill="currentColor"/>
          <rect x="3" y="16" width="2" height="4" fill="currentColor"/>
          <rect x="19" y="16" width="2" height="4" fill="currentColor"/>
        </svg>
      `,
      'chaise': `
        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <rect x="6" y="3" width="12" height="8" rx="1" fill="currentColor"/>
          <rect x="4" y="9" width="16" height="6" rx="1" fill="currentColor"/>
          <rect x="6" y="15" width="2" height="6" fill="currentColor"/>
          <rect x="16" y="15" width="2" height="6" fill="currentColor"/>
        </svg>
      `,
      'round-table': `
        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="8" fill="currentColor"/>
          <rect x="4" y="10" width="4" height="2" fill="currentColor"/>
          <rect x="16" y="10" width="4" height="2" fill="currentColor"/>
          <rect x="10" y="2" width="2" height="4" fill="currentColor"/>
          <rect x="10" y="18" width="2" height="4" fill="currentColor"/>
        </svg>
      `,
      'toilet': `
        <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <ellipse cx="12" cy="18" rx="6" ry="4" fill="currentColor"/>
          <rect x="8" y="4" width="8" height="12" rx="4" fill="currentColor"/>
          <ellipse cx="12" cy="10" rx="4" ry="2" fill="white"/>
        </svg>
      `,

      // Ajoutez d'autres icônes selon vos besoins...
    };

    return iconTemplates[type] || `
      <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <rect x="4" y="4" width="16" height="16" rx="2" fill="currentColor"/>
      </svg>
    `;
  }

  /**
   * Assombrit une couleur
   */
  private darkenColor(color: string): string {
    if (!color || !color.startsWith('#')) return '#000000';

    const hex = color.replace('#', '');
    const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - 50);
    const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - 50);
    const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - 50);

    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }

  /**
   * Éclaircit une couleur
   */
  private lightenColor(color: string): string {
    if (!color || !color.startsWith('#')) return '#FFFFFF';

    const hex = color.replace('#', '');
    const r = Math.min(255, parseInt(hex.substr(0, 2), 16) + 50);
    const g = Math.min(255, parseInt(hex.substr(2, 2), 16) + 50);
    const b = Math.min(255, parseInt(hex.substr(4, 2), 16) + 50);

    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }
}