export class ControllerServerHierarchyDto {
  ControllerServerId!: string;
  ControllerServerName!: string;
  MaxControllers!: number;
  MaxSensors!: number;
  GeographicZone!: string;
  CommercialCondition!: string;
  TriggerType!: string;
  ActionType!: string;
  EventType!: string;
  ControllerServerStatus!: string;
  IdLicence!: string;
  ControllerServerCreatedAt!: string;
  ControllerServerLastUpdatedAt!: string;
  LicenceId!: string;
  LicenceName!: string;
  LicenceDescription!: string;
  RelationId!: string | null;
  IdController!: string | null;
  ControllerId!: string | null;
  ControllerHostName!: string | null;
  ControllerModel!: string | null;
  ControllerSerialNumber!: string | null;
  ControllerMacAddress!: string | null;
  ControllerIpAddress!: string | null;
  ControllerBaseTopic!: string | null;
  ControllerState!: boolean | null;
  ControllerLastConnection!: string | null;
  ClientId!: string;
  ClientName!: string;
}

export interface GroupedControllerServer {
  Id: string;
  Name: string;
  MaxControllers: number;
  MaxSensors: number;
  GeographicZone: string;
  CommercialCondition: string;
  TriggerType: string;
  ActionType: string;
  EventType: string;
  Status: string;
  IdLicence: string;
  CreatedAt: string;
  LastUpdatedAt: string;
  Licence: {
    Id: string;
    Name: string;
    Description: string;
  };
  ControllerServerControllers: Array<{
    Id: string;
    IdController: string;
    Controller: {
      Id: string;
      HostName: string;
      Model: string;
      SerialNumber: string;
      MacAddress: string;
      IpAddress: string;
      BaseTopic: string;
      State: boolean;
      LastConnection: string | null;
    };
  }>;
}
