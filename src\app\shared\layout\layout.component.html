<!-- src/app/shared/components/layout/layout.component.html
<app-header></app-header>
<app-sidebar (sidebarToggled)="onSidebarToggled($event)"></app-sidebar>

<div class="main-content" [ngClass]="{'sidebar-collapsed': isSidebarCollapsed}">
  <router-outlet></router-outlet>
</div> -->

<!-- src/app/shared/layout/layout.component.html -->
<!-- src/app/shared/components/layout/layout.component.html -->
<app-header></app-header>
<app-sidebar (sidebarToggled)="onSidebarToggled($event)"></app-sidebar>

<div class="main-content" [ngClass]="{'sidebar-collapsed': isSidebarCollapsed}">
  <router-outlet></router-outlet>
</div>