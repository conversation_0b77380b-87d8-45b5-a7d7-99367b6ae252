import { Client } from "./client";
import { ControllerServeur } from "./controllerServeur";
import { Facture } from "./facture";
import { Licence } from "./licence";
import { AuditModel } from "./models-audit/audit-model";

export class Subscription extends AuditModel {
  DateDebut!: Date | null;
  DateFin!: Date | null;
  Status!: string | null;
  ClientId!: string;
  Client!: Client;
  LicenceId!: string;
  Licence!: Licence;
  Price!: number | null;
  PaymentFrequency!: string | null;
  Facture!: Facture[];
  ControllerServeurs?: ControllerServeur[]
}
