// notification.service.ts
import { Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { NotificationComponent } from '@app/components/notification/notification.component';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  constructor(private snackBar: MatSnackBar) {}

  showSuccess(message: string = 'Suppression effectuée avec succès'): void {
    this.snackBar.openFromComponent(NotificationComponent, {
      duration: 4000,
      data: {
        type: 'success',
        title: 'Opération réussie',
        message: message
      },
      horizontalPosition: 'right',
      verticalPosition: 'top',
      panelClass: ['success-notification'],
      viewContainerRef: undefined
    });
  }

  showError(message: string = 'Ce local ne peut pas être supprimé car il est associé avec d\'autres entités'): void {
    this.snackBar.openFromComponent(NotificationComponent, {
      duration: 5000,
      data: {
        type: 'error',
        title: '<PERSON>rreur',
        message: message
      },
      horizontalPosition: 'right',
      verticalPosition: 'top',
      panelClass: ['error-notification', 'transparent-snackbar'],
      viewContainerRef: undefined
    });
  }
}