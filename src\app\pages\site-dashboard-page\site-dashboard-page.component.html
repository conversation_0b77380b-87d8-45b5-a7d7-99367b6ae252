<div class="dashboard-container">
  <div class="statistics-header">
    <h2 class="statistics-title">Statistique du site:</h2>
  </div>
  <div class="dashboard-grid">
    <!-- Cards Container -->
    <div class="cards-container">
      <div class="card" *ngFor="let card of cards.slice(0, 4)">
        <div class="card-header">
          <div>
            <div class="card-title">{{ card.title }}</div>
            <div class="card-value">
              {{ card.amount }}
              <span class="kwh-unit" *ngIf="card.unit">{{ card.unit }}</span>
            </div>
          </div>
          <div
            class="percentage"
            [ngClass]="getPercentageClass(card.isPositive)"
            *ngIf="card.percentage !== null">
            {{ getPercentageIcon(card.isPositive) }} {{ Math.abs(card.percentage!) }}%
          </div>
          <div class="na-badge" *ngIf="card.percentage === null">N/A</div>
        </div>
        <div class="objective-section">
          <div class="objective-label">
            <div class="objective-dot" [ngClass]="getObjectiveDotClass(card.objectiveColor)"></div>
            <span>Objectif</span>
          </div>
          <div class="progress-container">
            <div class="progress-bar">
              <div
                class="progress-fill"
                [ngClass]="getProgressFillClass(card.objectiveColor)"
                [style.width.%]="card.progress">
              </div>
            </div>
            <div class="progress-text">{{ card.progress }}%</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Chart Card -->
    <div class="chart-card">
      <div class="chart-header">
        <h3 class="chart-title">Dépense consommation</h3>
        <div class="chart-menu">⋯</div>
      </div>
      <div class="chart-legend">
        <div class="legend-item">
          <div class="legend-dot blue"></div>
          <span>Dépense</span>
        </div>
        <div class="legend-item">
          <div class="legend-dot light-blue"></div>
          <span>Économisations</span>
        </div>
      </div>
      <div class="chart-container">
        <canvas id="consumptionChart"></canvas>
      </div>
    </div>
  </div>

  <div class="cards-container">
    <div class="card" *ngFor="let card of cards.slice(4, 8)">
      <div class="card-header">
        <div>
          <div class="card-title">{{ card.title }}</div>
          <div class="card-value">
            {{ card.amount }}
            <span class="kwh-unit" *ngIf="card.unit">{{ card.unit }}</span>
          </div>
        </div>
        <div
          class="percentage"
          [ngClass]="getPercentageClass(card.isPositive)"
          *ngIf="card.percentage !== null">
          {{ getPercentageIcon(card.isPositive) }} {{ Math.abs(card.percentage!) }}%
        </div>
        <div class="na-badge" *ngIf="card.percentage === null">N/A</div>
      </div>
      <div class="objective-section">
        <div class="objective-label">
          <div class="objective-dot" [ngClass]="getObjectiveDotClass(card.objectiveColor)"></div>
          <span>Objectif</span>
        </div>
        <div class="progress-container">
          <div class="progress-bar">
            <div
              class="progress-fill"
              [ngClass]="getProgressFillClass(card.objectiveColor)"
              [style.width.%]="card.progress">
            </div>
          </div>
          <div class="progress-text">{{ card.progress }}%</div>
        </div>
      </div>
    </div>
  </div>
</div>


