import { Injectable, On<PERSON><PERSON>roy } from '@angular/core';
import { IMqttMessage, MqttService } from 'ngx-mqtt';
import {
  Observable,
  Subject,
  Subscription,
  merge,
  filter
} from 'rxjs';

export interface TopicMessage {
  topic: string;
  payload: any;
  arrivedAt: Date;
}

@Injectable({ providedIn: 'root' })
export class MultiTopicListenerService implements OnDestroy {
  private subs = new Map<string, Subscription>();
  private all$ = new Subject<TopicMessage>();

  constructor(private mqtt: MqttService) {}

  /** Listen to ONE MQTT filter (may contain + / # wildcards) */
  listen(filterStr: string): Observable<TopicMessage> {
    if (!this.subs.has(filterStr)) {
      const sub = this.mqtt.observe(filterStr).subscribe(m => this.push(m));
      this.subs.set(filterStr, sub);
    }

    // only deliver messages whose topic matches the filter (wildcards honoured)
    return this.all$.pipe(
      filter(msg => this.matches(filterStr, msg.topic))
    );
  }

  /** Listen to a SET of filters at once */
  listenMany(filters: string[]): Observable<TopicMessage> {
    return merge(...filters.map(f => this.listen(f)));
  }

  /** Unsubscribe helpers -------------------------------------------------- */
  stop(filterStr: string) {
    this.subs.get(filterStr)?.unsubscribe();
    this.subs.delete(filterStr);
  }
  stopAll() {
    this.subs.forEach(s => s.unsubscribe());
    this.subs.clear();
  }
  ngOnDestroy() { this.stopAll(); }

  /** --------------------------------------------------------------------- */
  private push(msg: IMqttMessage) {
    const raw = msg.payload.toString();
    let parsed: any = raw;
    try { parsed = JSON.parse(raw); } catch {}
    this.all$.next({ topic: msg.topic, payload: parsed, arrivedAt: new Date() });
  }

  /** Quick MQTT wildcard matcher (# and + only) */
  private matches(filter: string, topic: string): boolean {
    if (filter === '#') return true;

    const flv = filter.split('/');
    const tlv = topic.split('/');

    for (let i = 0; i < flv.length; i++) {
      const f = flv[i];
      const t = tlv[i];

      if (f === '#') return true;                   // match the rest
      if (f === '+') continue;                      // match single level
      if (t === undefined || f !== t) return false; // mismatch
    }
    return flv.length === tlv.length;
  }
  setDeviceState(deviceId: string, state: string) {
  const topic = `zigbee2mqtt/${deviceId}/set`;
  const payload = JSON.stringify({ state });
  this.mqtt.unsafePublish(topic, payload, { qos: 0, retain: false });
  }
  
}
