import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  Output,
  SimpleChanges,
  OnInit,
  OnChanges,
} from '@angular/core';
import { Licence } from '@app/core/models/licence';
import { Subscription } from '@app/core/models/subscription';

@Component({
  selector: 'app-detail',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './detail.component.html',
  styleUrls: ['./detail.component.css'], // fixed from styleUrl to styleUrls
})
export class DetailComponent implements OnInit, OnChanges {
  @Input() subscription: Subscription | null = null;
  @Input() licences: Licence[] = [];
  @Output() detailsClosed = new EventEmitter<void>();

  selectedSubscription: Subscription | null = null;
  selectedLicenceName = '';

  ngOnInit(): void {
    this.updateLicenceName();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['subscription'] || changes['licences']) {
      this.updateLicenceName();
    }
  }

  private updateLicenceName(): void {
    if (
      this.subscription &&
      this.subscription.LicenceId &&
      this.licences.length > 0
    ) {
      const licence = this.licences.find(
        (l) =>
          l.Id === this.subscription!.LicenceId
      );
      this.selectedLicenceName = licence
        ? licence.Name || 'Licence inconnue'
        : 'Licence non trouvée';
    } else if (this.subscription && (this.subscription as any).licence) {
      // If licence object is directly attached to subscription
      this.selectedLicenceName =
        (this.subscription as any).licence.Name || 'Licence sans nom';
    } else {
      this.selectedLicenceName = 'Aucune licence assignée';
    }
  }

  closeDetails(): void {
    this.detailsClosed.emit();
  }

  formatDate(dateString: string | Date | undefined | null): string {
    if (!dateString) return 'Non disponible';
    try {
      return new Date(dateString).toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return String(dateString);
    }
  }
}
