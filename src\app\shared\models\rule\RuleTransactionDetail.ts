export interface RuleTransactionDetail {
  RuleId: string;
  RuleEnabled: boolean;
  TransactionId: string;
  ControllerInControl: boolean;
  ControllerId: string | null;
  ControllerIdController: string | null;
  ControllerLocalId: string;
  TransactionCreatedAt: string;
  TransactionCreatedBy: string;
  TransactionLastUpdatedAt: string | null;
  TransactionLastUpdatedBy: string | null;
  RuleTransactionId: string;
  ApplicationTimestamp: string;
  ApplicationCreatedBy: string;
  ApplicationLastUpdatedBy: string;
  LocalName: string;
  SiteName: string;
  SiteAddress: string;
  ControllerBaseTopic?: string; // Optional since it might not always be present
}
