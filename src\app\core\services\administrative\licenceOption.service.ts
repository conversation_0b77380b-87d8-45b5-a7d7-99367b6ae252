import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiService } from '../api.service';
import { LicenceOption } from '@app/core/models/licenceOption';
 
@Injectable({ providedIn: 'root' })
export class LicenceOptionApiService extends ApiService<LicenceOption> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("licence-option");
  }
}