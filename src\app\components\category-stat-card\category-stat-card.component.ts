import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import {
  OrganisationService,
  OrganisationType,
} from '../../core/services/organisation.service';
import { Router } from '@angular/router';
import { Organisation } from '@app/core/models/organisation';

@Component({
  selector: 'app-category-stat-card',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './category-stat-card.component.html',
  styleUrls: ['./category-stat-card.component.css'],
})
export class CategoryStatCardComponent implements OnInit {
  @Input() organisationType!: OrganisationType; // The organisation type
  @Input() clientCount!: number; // The count of clients/organisations of this type
  @Output() typeClick = new EventEmitter<string>(); // Emit when card is clicked
  @Output() viewDetails = new EventEmitter<string>(); // Emit when details button is clicked
  @Input() organisation!: Organisation;

  icon: string = 'location_on';
  cardColor: string = 'var(--primary)';
  displayLabel: string = '';

  // Organization type labels mapping
  private organisationTypeLabels: { [key in OrganisationType]: string } = {
    Ecole: 'École',
    Hopital: 'Hôpital',
    Entrepot: 'Entrepôt',
    Bureau: 'Bureau',
    Usine: 'Usine',
    Magasin: 'Magasin',
    Residence: 'Résidence',
    CentreCommercial: 'Centre Commercial',
    Restaurant: 'Restaurant',
    Hotel: 'Hôtel',
    Maison: 'Maison',
  };

  constructor(
    readonly organisationService: OrganisationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.icon = this.organisationService.getIconForOrganisationType(
      this.organisationType
    );
    this.cardColor = this.organisationService.getColorForOrganisationType(
      this.organisationType
    );
    this.displayLabel =
      // this.organisationTypeLabels[this.organisationType] ||
      // this.organisationType;
      this.organisation.Nom || this.organisationType;
  }

  getClientCount(): number {
    return this.organisation.Clients?.length ?? 0;
  }

  get logoUrl(): string {
    return this.organisation.LogoOrganisation || '';
  }

  hasLogo(): boolean {
    return !!this.organisation.LogoOrganisation;
  }

  // get displayLabel(): string {
  //   return this.organisation.Nom;
  // }

  onCardClick(): void {
    this.typeClick.emit(this.organisationType);
  }

  onDetailsClick(event: Event): void {
    event.stopPropagation(); // Prevent triggering the card click
    this.viewDetails.emit(this.organisationType);
  }
}
