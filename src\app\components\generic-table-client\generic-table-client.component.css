/* generic-table.component.css */

.subscription-table-container {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  margin-top: 20px;
  width: 100%;
}

.table-color-bar {
  height: 0.5rem;
  background: linear-gradient(to right, var(--primary), rgba(52, 211, 153, 0.747));
}

.table-wrapper {
  overflow-x: auto;
}

.generic-table {
  width: 100%;
  border-collapse: collapse;
}

.generic-table th {
  padding: 1rem 1.5rem;
  text-align: left;
  font-size: 0.875rem;
  font-weight: 600;
  color: #111827;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-family: "Montserrat", sans-serif;
  text-align: center;
}

.generic-table td {
  padding: 1rem 1.5rem;
  font-size: 0.875rem;
  color: #4b5563;
  border-bottom: 1px solid #e5e7eb;
  font-family: "Lato", sans-serif;
  vertical-align: middle;
  text-align: center;
}

.generic-table tr:last-child td {
  border-bottom: none;
}

.generic-table tr:hover {
  background-color: #f9fafb;
}

/* Style for the three-dot button */
.more-actions-button {
  min-width: 40px;
  width: 40px;
  height: 40px;
  padding: 0;
  line-height: 1;
  border: none;
  background: transparent;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%; /* Makes it round */
}

.more-actions-button:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.more-actions-button mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #6b7280; /* A neutral color for the three-dot icon */
}

/* Styles for individual items within the MatMenu */
.menu-action-item {
  width: 100%; /* Ensure menu item takes full width of menu */
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem; /* Space between icon and text */
  font-size: 0.875rem;
  background: none;
  border: none;
  cursor: pointer;
}

.menu-action-item:hover {
  background-color: #f3f4f6; /* Light gray on hover */
}

.menu-action-item mat-icon {
  font-size: 18px; /* Slightly smaller icons for menu items */
  width: 18px;
  height: 18px;
}

/* Color styles for each action icon within the menu */
.menu-action-item .action-view {
  /* Targets mat-icon with class 'action-view' inside 'menu-action-item' */
  color: blue;
}

.menu-action-item .action-edit {
  color: orange;
}

.menu-action-item .action-delete {
  color: red;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .generic-table td,
  .generic-table th {
    padding: 0.75rem 1rem;
  }
}

.status-cell {
  text-align: center;
}

.status-active {
  color: #28a745;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  background-color: rgba(40, 167, 69, 0.1);
  display: inline-block;
  max-width: 60px;
}

.status-inactive {
  color: #dc3545;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  background-color: rgba(220, 53, 69, 0.1);
  display: inline-block;
  min-width: 60px;
}

.status-maintenance {
  color: #044c8f;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  background-color: rgba(45, 122, 160, 0.1);
  display: inline-block;
  min-width: 60px;
}

.status-error {
  color: #fd7e14;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  background-color: rgba(253, 126, 20, 0.1);
  display: inline-block;
  min-width: 60px;
}

.status-retire {
  color: #ff9900;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  background-color: #ffe1c4;
  display: inline-block;
  min-width: 60px;
}


.status-green {
  color: #28a745;
  font-weight: bold;
}

.status-red {
  color: #dc3545;
  font-weight: bold;
}

.status-blue {
  color: #044c8f;
  font-weight: bold;
}

.status-installation {
  color: #ff9900 !important;
  font-weight: bold !important;
}

.separator {
  margin: 0 4px;
  color: #888;
  font-weight: bold;
}

.status-background{
    background-color: rgba(40, 167, 69, 0.1);
    width: fit-content;
    text-align: center;
    margin: 0 auto;
    padding: 4px 10px;
    border-radius: 12px;
}

/* Target Type Styles */
.target-type-cell {
  text-align: center;
}

.target-type-client {
  color: #6366f1;
  font-weight: 600;
  padding: 4px 12px;
  border-radius: 12px;
  background-color: rgba(99, 102, 241, 0.1);
  display: inline-block;
  min-width: 60px;
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.target-type-site {
  color: #059669;
  font-weight: 600;
  padding: 4px 12px;
  border-radius: 12px;
  background-color: rgba(5, 150, 105, 0.1);
  display: inline-block;
  min-width: 60px;
  border: 1px solid rgba(5, 150, 105, 0.2);
}

.target-type-local {
  color: #d97706;
  font-weight: 600;
  padding: 4px 12px;
  border-radius: 12px;
  background-color: rgba(217, 119, 6, 0.1);
  display: inline-block;
  min-width: 60px;
  border: 1px solid rgba(217, 119, 6, 0.2);
}