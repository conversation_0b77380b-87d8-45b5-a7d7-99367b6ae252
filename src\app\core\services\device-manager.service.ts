// device-manager.service.ts
import { Injectable, OnDestroy } from '@angular/core';
import { IMqttMessage, MqttService } from 'ngx-mqtt';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { Local } from '../models/local';

export interface Device {
  id: string;
  name: string;
  type: string;
  status: any;
  properties: any;
  lastSeen: Date;
}

@Injectable({ providedIn: 'root' })
export class DeviceManagerService implements OnDestroy {
  private currentLocalId?: string;
  private currentTopic?: string;
  private devicesSubject = new BehaviorSubject<Device[]>([]);
  private subscription?: Subscription;
  private devices: Device[] = [];

  constructor(private mqttService: MqttService) {}

  subscribeToLocal(local: Local): Observable<Device[]> {
    // Unsubscribe from previous topic if exists
    this.unsubscribeFromCurrentTopic();

    this.currentLocalId = local.Id;
    // this.currentTopic = `${local.BaseTopicMQTT}/#`;
    // this.currentTopic = `${local.BaseTopicMQTT}/#`;
    
    // Subscribe to the local's topic
    // this.subscription = this.mqttService.observe(this.currentTopic).subscribe(
    //   (message: IMqttMessage) => this.handleDeviceMessage(message)
    // );
    // this.subscription = this.mqttService.observe(this.currentTopic).subscribe(
    //   (message: IMqttMessage) => this.handleDeviceMessage(message)
    // );

    // // Request devices list
    // this.mqttService.unsafePublish(
    //   // `${local.BaseTopicMQTT}/bridge/request/devices`, 
    //   JSON.stringify({})
    // );

    return this.devicesSubject.asObservable();
  }

  private handleDeviceMessage(message: IMqttMessage): void {
    const topic = message.topic;
    
    // Handle bridge messages (devices list)
    if (topic.endsWith('/bridge/devices')) {
      try {
        const devicesData = JSON.parse(message.payload.toString());
        this.processDevicesList(devicesData);
      } catch (error) {
        console.error('Error parsing devices list:', error);
      }
      return;
    }

    // Handle regular device messages
    try {
      const payload = JSON.parse(message.payload.toString());
      const deviceName = topic.replace(`${this.currentTopic?.replace('/#', '')}/`, '');
      
      this.updateDeviceStatus(deviceName, payload);
    } catch (error) {
      console.error('Error parsing device message:', error);
    }
  }

  private processDevicesList(devices: any[]): void {
    this.devices = devices
      .filter(device => device.type !== 'Coordinator')
      .map(device => ({
        id: device.ieee_address,
        name: device.friendly_name,
        type: this.determineDeviceType(device),
        status: 'unknown',
        properties: {},
        lastSeen: new Date()
      }));
    
    this.devicesSubject.next([...this.devices]);
  }

  private updateDeviceStatus(deviceName: string, payload: any): void {
    const device = this.devices.find(d => d.name === deviceName);
    if (!device) return;

    device.lastSeen = new Date();
    device.properties = { ...device.properties, ...payload };
    
    // Update status based on payload
    if (payload.hasOwnProperty('state')) {
      device.status = payload.state;
    } else if (payload.hasOwnProperty('occupancy')) {
      device.status = payload.occupancy ? 'occupied' : 'vacant';
    }
    
    this.devicesSubject.next([...this.devices]);
  }

  private determineDeviceType(device: any): string {
    if (device.definition?.model.includes('light')) return 'light';
    if (device.definition?.model.includes('sensor')) return 'sensor';
    if (device.definition?.model.includes('switch')) return 'switch';
    if (device.definition?.model.includes('motion')) return 'motion';
    return 'unknown';
  }

  private unsubscribeFromCurrentTopic(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
      this.subscription = undefined;
    }
    this.devices = [];
    this.devicesSubject.next([]);
  }

  ngOnDestroy(): void {
    this.unsubscribeFromCurrentTopic();
  }
}