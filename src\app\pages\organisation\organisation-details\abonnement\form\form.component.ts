import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Client } from '@app/core/models/client';
import { Licence } from '@app/core/models/licence';
import { Organisation } from '@app/core/models/organisation';
import { Subscription } from '@app/core/models/subscription';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { LicenceApiService } from '@app/core/services/administrative/licence.service';
import { SubscriptionApiService } from '@app/core/services/administrative/subscription.service';

@Component({
  selector: 'app-form',
  standalone: true,
  imports: [FormsModule, CommonModule],
  templateUrl: './form.component.html',
  styleUrls: ['./form.component.css'], // fixed from styleUrl
})
export class FormComponent implements OnInit {
  @Input() subscription: Subscription = this.getEmptySubscription();
  @Input() clients: Client[] = [];
  @Input() licences: Licence[] = [];
  @Output() formSubmit = new EventEmitter<Subscription>();

  selectedSubscription: Subscription | null = null;

  isSubmitting = false;

  constructor(
    private subscriptionService: SubscriptionApiService,
    private licenceService: LicenceApiService,
    private clientService: ClientApiService
  ) {}

  ngOnInit(): void {
    // If you want to load clients and licences here independently, keep these:
    if (this.clients.length === 0) {
      this.clientService
        .getAll()
        .subscribe((clients) => (this.clients = clients));
    }
    if (this.licences.length === 0) {
      this.licenceService
        .getAll()
        .subscribe((licences) => (this.licences = licences));
    }
  }

  submit(): void {
    if (!this.subscription) return;

    this.isSubmitting = true;

    this.subscriptionService.create(this.subscription).subscribe({
      next: (res) => {
        console.log('Subscription created:', res);
        this.formSubmit.emit(res); // Notify parent about new/updated subscription
        this.resetForm();
      },
      error: (err) => {
        console.error('Error creating subscription:', err);
      },
      complete: () => {
        this.isSubmitting = false;
      },
    });
  }

  resetForm(): void {
    this.subscription = this.getEmptySubscription();
  }

  private getEmptySubscription(): Subscription {
    return {
      Id: '',
      DateDebut: null,
      DateFin: null,
      Status: '',
      ClientId: '',
      Client: {
        BusinessSector: '',
        Id: '',
        Name: '',
        Address: '',
        PhoneNumber: '',
        SIREN: '',
        SIRET: '',
        RC: '',
        ICE: '',
        IF: '',
        Patente: '',
        LegalForm: '',
        CompanyCreationDate: new Date(),
        ContactName: '',
        ContactEmail: '',
        Email: '',
        Brand: '',
        CompanyType: '',
        CompanySize: 0,
        City: '',
        Region: '',
        Country: '',
        Filiale: '',
        Status: '',
        RecusiveClientId: '',
        ActiveEquipment: 0,
        InactiveEquipment: 0,
        IdOrganisation: '',
        Organisation: {} as Organisation,
        Sites: [],
        Licences: [],
      
      },
      LicenceId: '',
      Licence: {} as Licence,
      Price: null,
      PaymentFrequency: '',
      Factures: [],
    };
  }
  // In your parent component
  close() {
    this.selectedSubscription = null;
  }
}
