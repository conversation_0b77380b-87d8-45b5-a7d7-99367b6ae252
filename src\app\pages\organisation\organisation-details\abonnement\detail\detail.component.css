.site-management-container {
  width: 95%;
  max-width: 800px;
  margin: 30px auto;
  padding: 25px;
  background: linear-gradient(145deg, #ffffff, #f9f9f9);
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  animation: fadeIn 0.4s ease-in;
}

/* Header Section */
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.popup-title {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.title-icon {
  font-size: 26px;
  color: var(--primary);
  background: linear-gradient(45deg, var(--primary), #81c784);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.close-icon {
  background: none;
  border: none;
  font-size: 24px;
  color: #718096;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--primary);
  padding: 5px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: #f5f5f5;
  color: #333;
}

.close-icon:hover {
  background-color: #f7fafc;
  color: #2d3748;
}

.form-container {
  width: 100%;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
  align-items: end;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 100%;
  margin-bottom: 0;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
}

.form-group input,
.form-group select,
.form-group textarea {
  height: 42px;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  color: #4a5568;
  background-color: #f8fafc;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-group input:disabled,
.form-group select:disabled {
  background-color: #f8fafc;
  color: #718096;
  cursor: not-allowed;
}

.form-group textarea {
  height: auto;
  min-height: 100px;
  resize: vertical;
}

.required {
  color: #ef4444;
  margin-left: 4px;
}

/* Section Styling */
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin: 30px 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-icon {
  font-size: 20px;
  color: var(--primary);
}

/* Client Info Styling */
.client-info {
  background: #f0fff4;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid var(--primary);
  margin-bottom: 20px;
}

.client-name {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.client-details {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.5;
}

/* Factures Table */
.factures-container {
  margin-top: 20px;
}

.factures-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.factures-table th {
  background: linear-gradient(45deg, var(--primary), #81c784);
  color: white;
  padding: 12px;
  text-align: left;
  font-weight: 500;
  font-size: 14px;
}

.factures-table td {
  padding: 12px;
  border-bottom: 1px solid #e2e8f0;
  font-size: 14px;
  color: #4a5568;
}

.factures-table tr:hover {
  background-color: #f7fafc;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-active {
  background-color: #c6f6d5;
  color: #22543d;
}

.status-inactive {
  background-color: #fed7d7;
  color: #742a2a;
}

.status-pending {
  background-color: #feebc8;
  color: #7c2d12;
}

/* Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
  margin-top: 30px;
}

.form-actions button {
  padding: 10px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn-secondary {
  padding: 10px 20px;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.btn-primary {
  background: linear-gradient(45deg, var(--primary), #81c784);
  color: white;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.2);
}

.btn-primary:hover {
  background: linear-gradient(45deg, #81c784, var(--primary));
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(76, 175, 80, 0.3);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions button {
    width: 100%;
  }

  .factures-table {
    font-size: 12px;
  }

  .factures-table th,
  .factures-table td {
    padding: 8px;
  }
}

/* Popup Overlay */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}
