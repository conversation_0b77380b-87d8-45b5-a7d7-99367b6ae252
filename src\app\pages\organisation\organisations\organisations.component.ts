// import { Component, OnInit } from '@angular/core';
// import { CommonModule } from '@angular/common';
// import { Router } from '@angular/router';
// import {
//   FormsModule,
//   ReactiveFormsModule,
//   FormGroup,
//   FormControl,
//   Validators,
// } from '@angular/forms';
// import { CardOrganisationComponent } from '../../../components/card-organisation/card-organisation.component';
// import { CategoriesSitesComponent } from '../categories-sites/categories-sites.component';
// import {
//   Organisation,
//   OrganisationService,
//   OrganisationType,
// } from '../../../core/services/organisation.service';
// import {
//   SiteApiService,

// } from '../../../core/services/administrative/site.service';
// import {
//   trigger,
//   state,
//   style,
//   transition,
//   animate,
// } from '@angular/animations';
// import { TableConfig } from '@app/core/models/table-config.module';

// type ViewMode = 'cards' | 'table';

// @Component({
//   selector: 'app-organisations',
//   standalone: true,
//   imports: [
//     CommonModule,
//     FormsModule,
//     ReactiveFormsModule,
//     CardOrganisationComponent,
//     CategoriesSitesComponent,
//   ],
//   templateUrl: './organisations.component.html',
//   styleUrls: ['./organisations.component.css'],
//   animations: [
//     trigger('tableRowAnimation', [
//       state('void', style({ opacity: 0, transform: 'translateY(20px)' })),
//       transition('void => *', animate('300ms ease-in')),
//     ]),
//     trigger('fadeIn', [
//       state('void', style({ opacity: 0 })),
//       transition('void => *', animate('400ms 300ms ease-in')),
//     ]),
//   ],
// })

// export class OrganisationsComponent implements OnInit {
//   organisations: Organisation[] = [];
//   filteredOrganisations: Organisation[] = [];
//   searchTerm: string = '';

//   isLoading: boolean = true;
//   organisationStats: { type: OrganisationType; count: number }[] = [];
//   searchOrganisationType: string = '';
//   selectedType: string = '';
//   selectedTypeForClientsList: string | null = null;

//   showAllCategories: boolean = false;
//   readonly initialDisplayCount: number = 6;

//   viewMode: ViewMode = 'cards';

//   totalEnergySaved: number = 128500; // kWh
//   successfulClients: number = 42;
//   ongoingProjects: number = 7;
//   projectsInExecution: number = 5;
//   clientSatisfactionRate: number = 97;
//   partnersCount: number = 12;

//   currentPage: number = 1;
//   pageSize: number = 5;
//   totalPages: number = 1;
//   totalCount: number = 0;

//   get displayedStats(): { type: OrganisationType; count: number }[] {
//     return this.showAllCategories
//       ? this.organisationStats
//       : this.organisationStats.slice(0, this.initialDisplayCount);
//   }

//   tableConfig: TableConfig = {
//     columns: [
//       {
//         key: 'nom',
//         label: 'Nom',
//         sortable: true,
//         filterType: 'text',
//         filterPlaceholder: 'Rechercher par nom',
//       },
//       {
//         key: 'type',
//         label: 'Type',
//         sortable: true,
//         filterType: 'select',
//         filterOptions: [
//           { value: '', label: 'Tous les types' },
//           { value: 'Ecole', label: 'École' },
//           { value: 'Hopital', label: 'Hôpital' },
//           { value: 'Entrepot', label: 'Entrepôt' },
//           { value: 'Bureau', label: 'Bureau' },
//           { value: 'Usine', label: 'Usine' },
//           { value: 'Magasin', label: 'Magasin' },
//           { value: 'Residence', label: 'Résidence' },
//           { value: 'CentreCommercial', label: 'Centre Commercial' },
//           { value: 'Restaurant', label: 'Restaurant' },
//           { value: 'Hotel', label: 'Hôtel' },
//           { value: 'Maison', label: 'Maison' },
//         ],
//       },
//       {
//         key: 'emailAddress',
//         label: 'Email',
//         filterType: 'text',
//         filterPlaceholder: 'Rechercher par email',
//       },
//       {
//         key: 'nombreEmployees',
//         label: 'Employés',
//         type: 'number',
//         sortable: true,
//         filterType: 'number',
//         filterPlaceholder: 'Nombre min',
//       },
//     ],
//     actions: {
//       view: true,
//       edit: true,
//       delete: true,
//     },
//     pageSize: 10,
//     pageSizeOptions: [5, 10, 25, 50],
//   };

//   toggleCategoriesDisplay(): void {
//     this.showAllCategories = !this.showAllCategories;
//   }
//   // Organization types with display labels
//   organisationTypes = [
//     { value: 'Ecole' as OrganisationType, label: 'École' },
//     { value: 'Hopital' as OrganisationType, label: 'Hôpital' },
//     { value: 'Entrepot' as OrganisationType, label: 'Entrepôt' },
//     { value: 'Bureau' as OrganisationType, label: 'Bureau' },
//     { value: 'Usine' as OrganisationType, label: 'Usine' },
//     { value: 'Magasin' as OrganisationType, label: 'Magasin' },
//     { value: 'Residence' as OrganisationType, label: 'Résidence' },
//     {
//       value: 'CentreCommercial' as OrganisationType,
//       label: 'Centre Commercial',
//     },
//     { value: 'Restaurant' as OrganisationType, label: 'Restaurant' },
//     { value: 'Hotel' as OrganisationType, label: 'Hôtel' },
//     { value: 'Maison' as OrganisationType, label: 'Maison' },
//   ];

//   createOrganisationForm = new FormGroup({
//     nom: new FormControl('', [Validators.required, Validators.minLength(2)]),
//     type: new FormControl<OrganisationType>('Bureau', [Validators.required]),
//     emailAddress: new FormControl('', [Validators.email]),
//     nombreEmployees: new FormControl(0, [Validators.min(0)]),
//     nombreEquipement: new FormControl(0, [Validators.min(0)]),
//     latitude: new FormControl<number>(0, [Validators.required]),
//     longitude: new FormControl<number>(0, [Validators.required]),
//     consommation: new FormControl<number>(0, [Validators.min(0)]),
//   });

//   uploadedLogo: File | undefined;
//   showCreateForm: boolean = false;

//     constructor(
//     readonly organisationService: OrganisationService,
//     readonly siteService: SiteApiService,
//     readonly router: Router,
//   ) {}

//   ngOnInit(): void {
//     this.loadOrganisations();
//   }

//   loadOrganisations(): void {
//     this.isLoading = true;
//     this.organisationService
//       .getOrganisationsPage(this.currentPage, this.pageSize)
//       .subscribe({
//         next: (response) => {
//           console.log('API Response:', response); // Add this log
//           this.organisations = response.items;
//           this.filteredOrganisations = [...this.organisations];
//           this.totalPages = response.pagination.totalPages;
//           this.totalCount = response.pagination.totalCount;

//           // Add these logs
//           console.log('Total Pages:', this.totalPages);
//           console.log('Total Count:', this.totalCount);
//           console.log('Current Page:', this.currentPage);
//           console.log('Organisations:', this.organisations);

//           this.calculateOrganisationStats();
//           this.isLoading = false;
//         },
//         error: (error) => {
//           console.error('Error loading organisations:', error);
//           // this.notificationService.show({
//           //   type: 'error',
//           //   title: 'Erreur',
//           //   message: 'Impossible de charger les organisations',
//           //   duration: 5000,
//           // });
//           this.isLoading = false;
//         },
//       });
//   }

//   onPageChange(event: { pageIndex: number; pageSize: number } | number): void {
//     if (typeof event === 'number') {
//       this.currentPage = event;
//     } else {
//       this.currentPage = event.pageIndex + 1;
//       this.pageSize = event.pageSize;
//     }
//     this.loadOrganisations();
//   }

//   nextPage(): void {
//     if (this.currentPage < this.totalPages) {
//       this.onPageChange(this.currentPage + 1);
//     }
//   }

//   previousPage(): void {
//     if (this.currentPage > 1) {
//       this.onPageChange(this.currentPage - 1);
//     }
//   }

//   submitCreateForm(): void {
//     if (this.createOrganisationForm.valid) {
//       const formValue = this.createOrganisationForm.value;
//       const newOrganisation: Partial<Organisation> = {
//         nom: formValue.nom ?? '',
//         type: formValue.type ?? 'Bureau',
//         emailAddress: formValue.emailAddress ?? '',
//         nombreEmployees: formValue.nombreEmployees ?? 0,
//         nombreEquipement: formValue.nombreEquipement ?? 0,
//         latitude: formValue.latitude ?? 0,
//         longitude: formValue.longitude ?? 0,
//         consommation: formValue.consommation ?? 0,
//         sites: [],
//       };

//       console.log('Creating organisation:', newOrganisation);
//       console.log('With logo:', this.uploadedLogo);

//       this.organisationService
//         .createOrganisation(newOrganisation, this.uploadedLogo)
//         .subscribe({
//           next: (createdOrg) => {
//             console.log('Organisation created successfully:', createdOrg);
//             this.organisations.push(createdOrg);
//             this.filteredOrganisations = [...this.organisations];
//           //  this.calculateSiteStats();
//             this.hideAddOrganisationForm();
//             // this.notificationService.show({
//             //   type: 'success',
//             //   title: 'Succès',
//             //   message: 'Organisation créée avec succès',
//             //   duration: 3000,
//             // });
//           },
//           error: (error) => {
//             console.error('Erreur lors de la création:', error);
//             let errorMessage = "Erreur lors de la création de l'organisation.";
//             if (error.error && typeof error.error === 'string') {
//               errorMessage += ` ${error.error}`;
//             }
//             // this.notificationService.show({
//             //   type: 'error',
//             //   title: 'Erreur',
//             //   message: errorMessage,
//             //   duration: 5000,
//             // });
//           },
//         });
//     } else {
//       console.log('Form is invalid:', this.createOrganisationForm.errors);
//       this.markFormGroupTouched(this.createOrganisationForm);
//     }
//   }

//   private markFormGroupTouched(formGroup: FormGroup): void {
//     Object.keys(formGroup.controls).forEach((key) => {
//       const control = formGroup.get(key);
//       control?.markAsTouched();
//     });
//   }

//  /* calculateSiteStats(): void {
//     // Liste de tous les types d'organisation possibles
//     const allTypes = this.organisationTypes.map((t) => t.value);

//     // Pour chaque type, compte le nombre de sites de toutes les organisations de ce type
//     this.siteStats = allTypes.map((type) => {
//       // Organisations de ce type
//       const orgsOfType = this.organisations.filter((org) => org.type === type);
//       // Nombre total de sites pour ce type
//       const count = orgsOfType.reduce(
//         (acc, org) => acc + (org.sites ? org.sites.length : 0),
//         0
//       );
//       return { type, count };
//     });
//   }*/

//   calculateOrganisationStats(): void {
//     this.organisationStats = this.organisationService.getOrganisationsByType(
//       this.organisations
//     );
//   }

//   filterOrganisations(): void {
//     if (!this.searchTerm && !this.searchOrganisationType) {
//       this.filteredOrganisations = [...this.organisations];
//       return;
//     }

//     const searchTermLower = this.searchTerm.toLowerCase();
//     const typeFilter = this.searchOrganisationType.toLowerCase();

//     this.filteredOrganisations = this.organisations.filter(
//       (org) =>
//         (this.searchTerm
//           ? org.nom.toLowerCase().includes(searchTermLower)
//           : true) &&
//         (this.searchOrganisationType
//           ? org.type.toLowerCase() === typeFilter
//           : true) /* &&
//       (this.searchTerm && org.emailAddress ? org.emailAddress.toLowerCase().includes(searchTermLower) : true)*/
//     );
//   }

//   clearTypeFilter(): void {
//     this.searchOrganisationType = '';
//     this.filterOrganisations();
//   }

//   setSelectedType(type: string): void {
//     this.selectedType = type;
//     this.scroll(); // Trigger the details button click
//   }

//   onOrganisationTypeSelected(type: string): void {
//     this.searchOrganisationType = type;
//     this.selectedType = type;
//     this.filterOrganisations(); // Trigger filtering
//     this.scroll(); // Scroll to the section
//     console.log("selected TYPE"+ this.selectedType);
//   }

//   onDetailsButtonClick(type: string): void {
//     this.selectedTypeForClientsList = type; // Set the type to be passed to ClientsListComponent
//     // Optionally, navigate to a different route or change view mode if needed
//     this.viewMode = 'table'; // For example, switch to table view
//     this.scroll();
//   }


//   scroll() {
//     const element = document.getElementById('section');
//     if (element) {
//       element.scrollIntoView({ behavior: 'smooth' });
//     }
//   }

//   toggleViewMode(): void {
//     this.viewMode = this.viewMode === 'cards' ? 'table' : 'cards';
//   }

//   showAddOrganisationForm(): void {
//     this.showCreateForm = true;
//   }

//   hideAddOrganisationForm(): void {
//     this.showCreateForm = false;
//     this.createOrganisationForm.reset({
//       nom: '',
//       type: 'Bureau',
//       emailAddress: '',
//       nombreEmployees: 0,
//       nombreEquipement: 0,
//       latitude: 0,
//       longitude: 0,
//       consommation: 0,
//     });
//     this.uploadedLogo = undefined;
//   }

//   onFileSelected(event: any): void {
//     if (event.target.files && event.target.files.length > 0) {
//       this.uploadedLogo = event.target.files[0];
//       console.log('Logo file selected:', this.uploadedLogo);
//     } else {
//       this.uploadedLogo = undefined;
//     }
//   }

//   editOrganisation(id: number): void {
//     this.router.navigate(['/organisations/edit', id]);
//   }

//   viewOrganisationDetails(id: number): void {
//     this.router.navigate(['/organisation-details', id]);
//   }

//   deleteOrganisation(id: number): void {
//     if (confirm('Êtes-vous sûr de vouloir supprimer cette organisation ?')) {
//       this.organisationService.deleteOrganisation(id).subscribe({
//         next: () => {
//           this.organisations = this.organisations.filter(
//             (org) => org.id !== id
//           );
//           this.filteredOrganisations = this.filteredOrganisations.filter(
//             (org) => org.id !== id
//           );
//         //  this.calculateSiteStats();
//           // this.notificationService.show({
//           //   type: 'success',
//           //   title: 'Succès',
//           //   message: 'Organisation supprimée avec succès',
//           //   duration: 3000,
//           // });
//         },
//         error: (error) => {
//           console.error('Erreur lors de la suppression:', error);
//           // this.notificationService.show({
//           //   type: 'error',
//           //   title: 'Erreur',
//           //   message: "Erreur lors de la suppression de l'organisation",
//           //   duration: 5000,
//           // });
//         },
//       });
//     }
//   }

//   getOrganisationTypeLabel(type: OrganisationType): string {
//     const typeObj = this.organisationTypes.find((t) => t.value === type);
//     return typeObj ? typeObj.label : type;
//   }

//   getPageNumbers(): number[] {
//     if (this.totalPages <= 7) {
//       return Array.from({ length: this.totalPages }, (_, i) => i + 1);
//     }

//     if (this.currentPage <= 4) {
//       return [1, 2, 3, 4, 5, -1, this.totalPages];
//     }

//     if (this.currentPage >= this.totalPages - 3) {
//       return [
//         1,
//         -1,
//         this.totalPages - 4,
//         this.totalPages - 3,
//         this.totalPages - 2,
//         this.totalPages - 1,
//         this.totalPages,
//       ];
//     }

//     return [
//       1,
//       -1,
//       this.currentPage - 1,
//       this.currentPage,
//       this.currentPage + 1,
//       -1,
//       this.totalPages,
//     ];
//   }

//   onTableAction(event: { action: string; item: any }) {
//     switch (event.action) {
//       case 'view':
//         this.viewOrganisationDetails(event.item.id);
//         break;
//       case 'edit':
//         this.editOrganisation(event.item.id);
//         break;
//       case 'delete':
//         this.deleteOrganisation(event.item.id);
//         break;
//     }
//   }

//   onSortChange(event: { active: string; direction: string }) {
//     const sortField = event.active;
//     const sortDirection = event.direction;

//     this.filteredOrganisations.sort((a, b) => {
//       const valueA = a[sortField as keyof Organisation];
//       const valueB = b[sortField as keyof Organisation];

//       if (valueA === undefined || valueB === undefined) return 0;

//       // Handle different types of values
//       if (typeof valueA === 'string' && typeof valueB === 'string') {
//         return sortDirection === 'asc'
//           ? valueA.localeCompare(valueB)
//           : valueB.localeCompare(valueA);
//       }

//       // Handle numeric values
//       const numA = Number(valueA);
//       const numB = Number(valueB);

//       if (!isNaN(numA) && !isNaN(numB)) {
//         return sortDirection === 'asc' ? numA - numB : numB - numA;
//       }

//       // Default comparison for other types
//       if (valueA < valueB) {
//         return sortDirection === 'asc' ? -1 : 1;
//       }
//       if (valueA > valueB) {
//         return sortDirection === 'asc' ? 1 : -1;
//       }
//       return 0;
//     });
//   }

//   onFilterChange(filters: { [key: string]: any }) {
//     this.filteredOrganisations = this.organisations.filter((org) => {
//       return Object.entries(filters).every(([key, value]) => {
//         if (!value) return true;

//         const orgValue = org[key as keyof Organisation];
//         if (orgValue === undefined) return false;

//         switch (key) {
//           case 'nom':
//           case 'emailAddress':
//             return orgValue
//               .toString()
//               .toLowerCase()
//               .includes(value.toLowerCase());

//           case 'type':
//             return orgValue === value;

//           case 'nombreEmployees':
//             return Number(orgValue) >= Number(value);

//           default:
//             return true;
//         }
//       });
//     });
//   }
// }