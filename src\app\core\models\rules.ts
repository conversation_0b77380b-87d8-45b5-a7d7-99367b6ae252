// src/app/core/models/rules.ts
import { ControllerServerRule } from "./controllerServerRule";
import { RuleTag } from "./ruleTag";
import { RuleTransaction } from "./ruleTransaction";

export interface Rules {
    Id: string;
    RawData: string;
    enabled: boolean;
    Summary: string;
    priority: number;
    controllerServerRules: ControllerServerRule[];
    ruleTransactions: RuleTransaction[];
    ruleTags: RuleTag[];
}
