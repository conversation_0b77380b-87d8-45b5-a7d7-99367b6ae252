/* Container */
.logs-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* Page Header */
.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #16a34a, #22c55e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  color: var(--gray-600, #4b5563);
  font-size: 1.1rem;
  font-weight: 400;
}

/* Filter Card */
.filter-card {
  background: #fff;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  border: 1px solid var(--gray-200, #e5e7eb);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
}

/* Filter Grid */
.filter-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

/* Form Group */
.form-group {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 5rem; /* Fixed height for input alignment */
}

/* Form Label */
.form-label {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-700, #374151);
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.form-label mat-icon {
  font-size: 1rem;
  line-height: 1;
  display: inline-flex;
  align-self: baseline;
  margin: 0;
  position: static;
}

/* Required Indicator */
.required {
  color: #ef4444;
}

/* Form Control */
.form-control {
  flex-grow: 1;
  height: 100%;
  padding: 0 1rem;
  border: 2px solid var(--gray-200, #e5e7eb);
  border-radius: 8px;
  font-size: 0.95rem;
  background: #fff;
  color: var(--gray-900, #111827);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  box-sizing: border-box;
}

.form-control:focus {
  outline: none;
  border-color: #16a34a;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.15);
}

.form-control:hover {
  border-color: var(--gray-300, #d1d5db);
}

/* Search Wrapper */
.search-wrapper {
  position: relative;
  width: 100%;
  height: 5rem;
}

.search-wrapper input {
  padding-right: 3rem;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border: 2px solid var(--gray-200, #e5e7eb);
  border-radius: 8px;
  font-size: 0.95rem;
  color: var(--gray-900, #111827);
  background: #fff;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.search-wrapper input:focus {
  outline: none;
  border-color: #16a34a;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.15);
}

.search-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400, #9ca3af);
  font-size: 1.25rem;
  pointer-events: none;
}

/* Filter Actions */
.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--gray-200, #e5e7eb);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: transform 0.15s ease, box-shadow 0.15s ease;
  user-select: none;
}

.btn mat-icon {
  font-size: 1rem;
  vertical-align: middle;
  display: inline-flex;
  align-items: center;
  line-height: 1;
  margin: 0;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #16a34a, #22c55e);
  color: #fff;
  box-shadow: 0 2px 6px rgba(22, 163, 74, 0.4);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 14px rgba(22, 163, 74, 0.6);
}

.btn-secondary {
  background: var(--gray-100, #f3f4f6);
  color: var(--gray-700, #374151);
  border: 1px solid var(--gray-200, #e5e7eb);
  box-shadow: inset 0 0 0 0 transparent;
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
}

.btn-secondary:hover:not(:disabled) {
  background: var(--gray-200, #e5e7eb);
  box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.05);
}

/* Loading State */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: #fff;
  border-radius: 12px;
  gap: 1rem;
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #e5e7eb;
  border-top-color: #16a34a;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Logs Grid */
.log-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  animation: fadeIn 0.5s ease-out;
}

/* Log Card - Improved */
.log-card {
  position: relative;
  padding: 1.5rem 1.75rem;
  border-radius: 16px;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: transform 0.25s ease, box-shadow 0.25s ease,
    border-color 0.25s ease;
  overflow: hidden;
  height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  user-select: none;
}

.log-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
  border-color: #16a34a;
}

/* Left colored border for log level */
.log-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 6px;
  border-radius: 16px 0 0 16px;
  background-color: #d1d5db;
  transition: background-color 0.3s ease;
  z-index: 10;
}

.log-card.info::before {
  background-color: #22c55e;
}
.log-card.warning::before {
  background-color: #f59e0b;
}
.log-card.error::before {
  background-color: #ef4444;
}
.log-card.debug::before {
  background-color: #10b981;
}

/* Card Content */
.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.log-level {
  padding: 0.4rem 1rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.07em;
  user-select: none;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.05);
}

.log-level.info {
  background-color: #d4f8dc;
  color: #15803d;
  box-shadow: 0 0 10px #22c55e50;
}
.log-level.warning {
  background-color: #fff4d6;
  color: #b45309;
  box-shadow: 0 0 10px #f59e0b50;
}
.log-level.error {
  background-color: #fed7d7;
  color: #b91c1c;
  box-shadow: 0 0 10px #ef444450;
}
.log-level.debug {
  background-color: #d1fae5;
  color: #047857;
  box-shadow: 0 0 10px #10b98150;
}

.log-timestamp {
  font-size: 0.75rem;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  user-select: none;
  white-space: nowrap;
}

/* Log message with smooth truncation */
.log-message {
  font-size: 0.9rem;
  color: #374151;
  line-height: 1.4;
  margin-bottom: 0.75rem;
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  user-select: text;
}

/* Meta Tags */
.log-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.meta-tag {
  background: #f9fafb;
  color: #4b5563;
  padding: 0.3rem 0.6rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 500;
  user-select: none;
  box-shadow: 0 1px 3px rgb(0 0 0 / 0.05);
}

.meta-key {
  font-weight: 700;
  color: #374151;
}

.meta-value {
  margin-left: 0.3rem;
}

/* Log Topic */
.log-topic {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #4b5563;
  background: #f3f4f6;
  padding: 0.45rem 0.75rem;
  border-radius: 8px;
  user-select: none;
  font-weight: 500;
  box-shadow: inset 0 0 5px rgb(0 0 0 / 0.03);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  user-select: none;
}

.empty-icon mat-icon {
  font-size: 3rem;
  color: #9ca3af;
  margin-bottom: 1rem;
}

.empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.empty-description {
  color: #4b5563;
  font-weight: 400;
}

/* Modal */
.log-modal-backdrop {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease-in-out;
}

.log-modal {
  background: #fff;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  animation: fadeInUp 0.3s ease-in-out;
  position: relative;
}

.log-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.close-btn {
  background: transparent;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  transition: color 0.2s ease;
}

.close-btn:hover {
  color: #374151;
}

/* --- Improved Modal Body --- */
.log-modal-body p {
  margin-bottom: 1rem;
  color: #1f2937;
  font-size: 0.95rem;
  user-select: text;
}

/* Modal Sections */
.modal-section {
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

.modal-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.modal-section h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  user-select: none;
}

/* Log Level Badge in Modal */
.modal-level-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-weight: 700;
  font-size: 0.9rem;
  text-transform: uppercase;
  user-select: none;
}

.modal-level-badge.info {
  background-color: rgba(34, 197, 94, 0.15);
  color: #16a34a;
}

.modal-level-badge.warning {
  background-color: rgba(245, 158, 11, 0.15);
  color: #d97706;
}

.modal-level-badge.error {
  background-color: rgba(239, 68, 68, 0.15);
  color: #dc2626;
}

.modal-level-badge.debug {
  background-color: rgba(16, 185, 129, 0.15);
  color: #0f766e;
}

/* Message formatting */
.modal-message {
  background-color: #f9fafb;
  padding: 1rem;
  border-radius: 8px;
  font-family: monospace, monospace;
  font-size: 0.9rem;
  color: #374151;
  white-space: pre-wrap;
  word-wrap: break-word;
  user-select: text;
  max-height: 300px;
  overflow-y: auto;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.05);
}

/* Payload container */
.modal-payload {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 0.5rem 1rem;
  background-color: #f3f4f6;
  border-radius: 8px;
  padding: 1rem;
  max-height: 300px;
  overflow-y: auto;
  font-size: 0.9rem;
  color: #374151;
}

.payload-item {
  display: contents;
}

.payload-key {
  font-weight: 600;
  color: #1f2937;
  user-select: text;
}

.payload-value {
  white-space: pre-wrap;
  user-select: text;
  word-break: break-word;
}

/* Topic */
.log-modal-body section p {
  font-size: 0.95rem;
  color: #1f2937;
  user-select: text;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(2rem);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .logs-container {
    padding: 1rem;
  }

  .page-title {
    font-size: 2rem;
  }

  .filter-card {
    padding: 1.5rem;
  }

  .filter-grid {
    grid-template-columns: 1fr;
  }

  .filter-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .log-grid {
    grid-template-columns: 1fr;
  }

  .log-modal {
    width: 95%;
    max-width: none;
    padding: 1rem 1.25rem;
  }
}
