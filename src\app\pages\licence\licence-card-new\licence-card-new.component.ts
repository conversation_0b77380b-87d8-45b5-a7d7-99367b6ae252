import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { Client } from '@app/core/models/client';
import { LicenceApiService } from '@app/core/services/administrative/licence.service';
import { LicenceOptionApiService } from '@app/core/services/administrative/licenceOption.service';
import { OptionApiService } from '@app/core/services/administrative/option.service';
import { Licence } from '@app/core/models/licence';
import { LicenceOption } from '@app/core/models/licenceOption';
import { Option } from '@app/core/models/option';
import { Subscription } from '@app/core/models/subscription';
import { SubscriptionApiService } from '@app/core/services/administrative/subscription.service';
import { SubscribedOptionsApiService } from '@app/core/services/administrative/subscribedOptions.service';
import { SubscribedOptions } from '@app/core/models/subscribedoptions';
import { NgxLoadingModule } from 'ngx-loading';
import { Router } from '@angular/router';

@Component({
  selector: 'app-licence',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NgxLoadingModule,
  ],
  templateUrl: './licence-card-new.component.html',
  styleUrls: ['./licence-card-new.component.css'],
  animations: [
    trigger('slideInOut', [
      state('void', style({
        transform: 'translateY(-10px)',
        opacity: 0
      })),
      state('*', style({
        transform: 'translateY(0)',
        opacity: 1
      })),
      transition('void <=> *', animate('200ms ease-in-out'))
    ]),
    trigger('growIn', [
      state('void', style({
        transform: 'scale(0.8)',
        opacity: 0
      })),
      state('*', style({
        transform: 'scale(1)',
        opacity: 1
      })),
      transition('void <=> *', animate('150ms ease-in-out'))
    ]),
    trigger('fadeInOut', [
      state('void', style({
        opacity: 0,
        transform: 'scale(0.9)'
      })),
      state('*', style({
        opacity: 1,
        transform: 'scale(1)'
      })),
      transition('void <=> *', animate('300ms ease-in-out'))
    ]),
    trigger('slideDown', [
      state('void', style({
        transform: 'translateY(-20px)',
        opacity: 0
      })),
      state('*', style({
        transform: 'translateY(0)',
        opacity: 1
      })),
      transition('void <=> *', animate('400ms ease-out'))
    ])
  ]
})
export class LicenceCardNewComponent implements OnInit {
  constructor(
    private clientApiService: ClientApiService,
    private licenceApiService: LicenceApiService,
    private licenceOptionApiService: LicenceOptionApiService,
    private optionApiService: OptionApiService,
    private subscriptionApiService: SubscriptionApiService,
    private subscribedOptionsApiService: SubscribedOptionsApiService,
    private router: Router
  ) {}

  searchQuery = '';
  showDropdown = false;
  isLoading = false;
  selectedPaymentFrequency: string = 'Mensuel';
  customDateFin: string = '';
  customMonths: number = 1;
  subscriptionIdFromRoute: string | null = null; 
  showConfirmationPopup = false;
  selectedLicenseForConfirmation: Licence | null = null;
  showSuccessNotification = false;
  showChooseClientError = false;
  showNoOptionCheckedPopup = false;
  _pendingRestoreLicence: Licence | null = null;
  _pendingRestoreType: 'affecter' | null = null;

  // Pagination state
  pageSize = 3;
  currentPage = 0;
  get totalPages() {
    return Math.ceil(this.licences.length / this.pageSize);
  }
  get pagedLicences() {
    const start = this.currentPage * this.pageSize;
    return this.licences.slice(start, start + this.pageSize);
  }
  
  clients: Client[] = [];
  filteredClients: Client[] = [];
  licences: Licence[] = [];
  options: Option[] = [];
  licenceOptions: LicenceOption[] = []; 
  subscriptions: Subscription[] = [];
  clientSubscription: Subscription | null = null;
  checkedOptions: { [licenceId: string]: Set<string> } = {};
  initialCheckedOptions: { [licenceId: string]: Set<string> } = {};
  selectedClient: Client | null = null;

  fetchClients(): Promise<void> {
    return new Promise((resolve) => {
      this.clientApiService.getAll().subscribe({
        next: (data: Client[]) => {
          this.clients = data.map(clients => ({
            ...clients,
            Name: clients.Name,
            ClientLogo: clients.ClientLogo
          }));
          this.filteredClients = [...this.clients];
          resolve();
        },
        error: (error) => {
          console.error('Error fetching clients:', error);
          resolve();
        }
      });
    });
  }

  fetchLicences(): Promise<void> {
    return new Promise((resolve) => {
      this.licenceApiService.getAll().subscribe({
        next: (data: Licence[]) => {
          this.licences = data.map(licence => ({
            ...licence,
            name: licence.Name,
            description: licence.Description
          }));
          resolve();
        },
        error: (error) => {
          console.error('Error fetching licences:', error);
          resolve();
        }
      });
    });
  }

  fetchOptions(): Promise<void> {
    return new Promise((resolve) => {
      this.optionApiService.getAll().subscribe({
        next: (data: Option[]) => {
          this.options = data.map(options => ({
            ...options,
            name: options.Name,
            price: options.Price
          }));
          resolve();
        },
        error: (error) => {
          console.error('Error fetching options:', error);
          resolve();
        }
      });
    });
  }

  fetchLicenceOptions(): Promise<void> {
    return new Promise((resolve) => {
      this.licenceOptionApiService.getAll().subscribe({
        next: (data: LicenceOption[]) => {
          this.licenceOptions = data;
          resolve();
        },
        error: (error) => {
          console.error('Error fetching licence options:', error);
          resolve();
        }
      });
    });
  }

  fetchSubscriptions(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.subscriptionApiService.getAll().subscribe({
        next: async (data: Subscription[]) => {
          this.subscriptions = data;
          resolve();
        },
        error: (error) => {
          console.error('Error fetching subscriptions:', error);
          reject(error);
        }
      });
    });
  }

  toggleOption(licence: Licence, optionId: string, event: Event) {
    const licenceId = licence.Id;
    if (!this.checkedOptions[licenceId]) {
      this.checkedOptions[licenceId] = new Set();
    }
    if (this.checkedOptions[licenceId].has(optionId)) {
      this.checkedOptions[licenceId].delete(optionId);
    } else {
      this.checkedOptions[licenceId].add(optionId);
    }
  }

  selectClient(client: Client) {
    this.selectedClient = client;
    this.searchQuery = '';
    this.showDropdown = false;
    this.filteredClients = [];

    // Clear all previous states immediately
    this.checkedOptions = {};
    this.initialCheckedOptions = {};

    // Set ALL options as checked by default for ALL licences
    this.licences.forEach(licence => {
      const allOptionIds = this.getOptionsForLicence(licence).map(opt => opt.Id);
      this.checkedOptions[licence.Id] = new Set(allOptionIds);
      this.initialCheckedOptions[licence.Id] = new Set(allOptionIds);
    });
  }

    goBackToSubscriptionList(): void {
    this.router.navigate(['/licence']);
  }

  getSelectedOptionsForConfirmation(): Option[] {
    if (!this.selectedLicenseForConfirmation) return [];
    
    const licenceId = this.selectedLicenseForConfirmation.Id;
    const checkedSet = this.checkedOptions[licenceId] || new Set();
    
    // Return only options that are both linked to the licence AND checked
    return this.getOptionsForLicence(this.selectedLicenseForConfirmation)
      .filter(option => checkedSet.has(option.Id));
  }

  clearSelection() {
    // Reset to all options checked for all licences
    this.licences.forEach(licence => {
      const allOptionIds = this.getOptionsForLicence(licence).map(opt => opt.Id);
      this.checkedOptions[licence.Id] = new Set(allOptionIds);
      this.initialCheckedOptions[licence.Id] = new Set(allOptionIds);
    });
    
    this.selectedClient = null;
    
    // Reset payment frequency and dates when clearing selection
    this.selectedPaymentFrequency = 'Mensuel';
    this.customDateFin = '';
  }

  public selectLicense(licence: Licence) {
    if (!this.selectedClient) {
      this.showChooseClientError = true;
      setTimeout(() => { this.showChooseClientError = false; }, 2500);
      return;
    }
    
    const checked = this.checkedOptions[licence.Id] || new Set();
    
    if (checked.size === 0) {
      this.showNoOptionCheckedPopup = true;
      this._pendingRestoreLicence = licence;
      this._pendingRestoreType = 'affecter';
      return;
    }
    
    this.selectedLicenseForConfirmation = licence;
    this.showConfirmationPopup = true;
  }

  closeNoOptionCheckedPopup() {
    this.showNoOptionCheckedPopup = false;
    if (this._pendingRestoreLicence) {
      const licence = this._pendingRestoreLicence;
      const initial = this.initialCheckedOptions[licence.Id] || new Set();
      this.checkedOptions[licence.Id] = new Set(Array.from(initial));
      this._pendingRestoreLicence = null;
      this._pendingRestoreType = null;
    }
  }

  async confirmLicenseApplication() {
    if (!this.selectedClient || !this.selectedLicenseForConfirmation) {
      console.error('Missing client or license');
      return;
    }

    const selectedOptions = this.getSelectedOptionsForConfirmation();

    if (selectedOptions.length === 0) {
      console.error('No options selected for licence');
      this.showNoOptionCheckedPopup = true;
      return;
    }

    try {
      this.isLoading = true; // Show loading state

      let dateDebut: Date, dateFin: Date;
      if (this.selectedPaymentFrequency === 'custom' || this.selectedPaymentFrequency === 'Personnalisé') {
        dateDebut = new Date();
        dateFin = this.customDateFin ? new Date(this.customDateFin) : new Date();
      } else {
        dateDebut = new Date();
        dateFin = this.calculateDateFin(dateDebut, this.selectedPaymentFrequency, this.customMonths);
      }
      
      const dateDebutStr = this.formatDate(dateDebut);
      const dateFinStr = this.formatDate(dateFin);

      const clientId = this.selectedClient.Id;
      const licenceId = this.selectedLicenseForConfirmation.Id;
      const selectedOptionIds = selectedOptions.map(option => option.Id);

      let totalPrice: number = selectedOptions.reduce((sum, opt) => sum + (opt.Price || 0), 0);

      const subscription   = {
        DateDebut: dateDebutStr,
        DateFin: dateFinStr,
        ClientId: clientId,
        LicenceId: licenceId,
        Price: totalPrice,
        Status: 'En attente',
        PaymentFrequency: this.selectedPaymentFrequency
      };

      console.log('Creating subscription:', subscription); // Debug log

      const createdSub = await this.subscriptionApiService.create(subscription).toPromise();
      console.log('Created subscription:', createdSub); // Debug log
      
      if (createdSub && createdSub.Id) {
        await this.saveSubscribedOptionsForLicence(createdSub.Id, selectedOptionIds);
        console.log('Saved subscribed options'); // Debug log

        // Close popup FIRST
        this.showConfirmationPopup = false;
        this.selectedLicenseForConfirmation = null;
        
        // Then show success notification
        this.showSuccessNotification = true;
        
        // Auto-hide success notification after 3 seconds
        setTimeout(() => {
          this.showSuccessNotification = false;
        }, 3000);

        // Refresh data
        await Promise.all([
          this.fetchSubscriptions(),
          this.fetchClients()
        ]);
        
        const refreshedClient = this.clients.find(c => c.Id === this.selectedClient?.Id);
        if (refreshedClient) {
          this.selectClient(refreshedClient);
        }
      } else {
        console.error('Failed to create subscription - no ID returned');
        throw new Error('Failed to create subscription');
      }
    } catch (error) {
      console.error('Error applying license:', error);
      // Show error message to user
      alert('Erreur lors de l\'affectation de la licence. Veuillez réessayer.');
    } finally {
      this.isLoading = false; // Hide loading state
    }
  }

  getMonthsForPaymentFrequency(paymentFrequency: string): number {
    const freq = this.paymentFrequencies.find(f => f.value === paymentFrequency);
    if (!freq) return 1;
    return freq.months;
  }

  // Calculate total options price based on PaymentFrequency
  getOptionsTotalForFrequency(licence: Licence, checkedSet: Set<string>, paymentFrequency: string): number {
    const months = this.getMonthsForPaymentFrequency(paymentFrequency);
    return this.getOptionsForLicence(licence)
      .filter(opt => checkedSet.has(opt.Id))
      .reduce((sum, opt) => sum + ((opt.Price || 0) * months), 0);
  }

  // Cancel/close popups and notifications
  cancelLicenseApplication() {
    this.showConfirmationPopup = false;
    this.selectedLicenseForConfirmation = null;
  }

  closeSuccessNotification() {
    this.showSuccessNotification = false;
  }

  getLicenceOptionsTotal(licence: Licence): number {
    if (!licence) return 0;
    
    const licenceId = licence.Id;
    const checkedSet = this.checkedOptions[licenceId] || new Set();
    
    const baseTotal = this.getOptionsForLicence(licence)
      .filter(opt => checkedSet.has(opt.Id))
      .reduce((sum, opt) => sum + (opt.Price || 0), 0);

    if (this.selectedPaymentFrequency === 'Annuel') {
      return baseTotal * 12; // Annual total
    } else {
      return baseTotal; // Monthly total
    }
  }

  // Card pagination
  prevPage() {
    if (this.currentPage > 0) {
      this.currentPage--;
      this.scrollToTop();
    }
  }
  nextPage() {
    if (this.currentPage < this.totalPages - 1) {
      this.currentPage++;
      this.scrollToTop();
    }
  }
  scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  // Card mode helpers for template
  isAffecterCard(): boolean {
    return true; // Since we only have add mode now
  }

  // Checkbox checked state for options
  isOptionChecked(licence: Licence, optionId: string): boolean {
    return this.checkedOptions[licence.Id]?.has(optionId) ?? false;
  }

  // Get options for a licence
  getOptionsForLicence(licence: Licence): Option[] {
    if (!licence) return [];
    const linkedOptionIds = this.licenceOptions
      .filter((lo: LicenceOption) => lo.LicenceId === licence.Id)
      .map((lo: LicenceOption) => lo.OptionId);
    return this.options.filter((opt: Option) => linkedOptionIds.includes(opt.Id));
  }

  // Check if option is linked to licence
  isOptionLinkedToLicence(licenceId: string, optionId: string): boolean {
    return this.licenceOptions.some((lo: LicenceOption) => lo.LicenceId === licenceId && lo.OptionId === optionId);
  }

  // Format date for display
  formatDate(date: Date | string | null): string {
    if (!date) return '';
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    return `${year}/${month}/${day}`;
  }

  // Calculate date fin based on frequency
  calculateDateFin(dateDebut: Date, paymentFrequency: string, customMonths: number): Date {
    let monthsToAdd = 1;
    switch (paymentFrequency) {
      case 'Mensuel':
        monthsToAdd = 1;
        break;
      case 'Annuel':
        monthsToAdd = 12;
        break;
      default:
        monthsToAdd = 1;
    }
    const result = new Date(dateDebut);
    const originalDay = result.getDate();
    let newMonth = result.getMonth() + monthsToAdd;
    let newYear = result.getFullYear();
    newYear += Math.floor(newMonth / 12);
    newMonth = newMonth % 12;
    result.setFullYear(newYear, newMonth, 1);
    const lastDay = new Date(result.getFullYear(), result.getMonth() + 1, 0).getDate();
    result.setDate(Math.min(originalDay, lastDay));
    return result;
  }

  private async saveSubscribedOptionsForLicence(subscriptionId: string, checkedOptionIds: string[]): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      this.subscribedOptionsApiService.getAll().subscribe({
        next: (allSubscribedOptions: SubscribedOptions[]) => {
          const toDelete = allSubscribedOptions.filter(
            (so: SubscribedOptions) => so.SubscriptionId === subscriptionId
          );
          
          // Function to create new subscribed options
          const createNewOptions = () => {
            const createPromises = checkedOptionIds.map(optionId => {
              const subscribedOption: Partial<SubscribedOptions> = {
                SubscriptionId: subscriptionId,
                OptionId: optionId,
                checked: true
              };
              return this.subscribedOptionsApiService.create(subscribedOption).toPromise();
            });
            
            Promise.all(createPromises)
              .then(() => resolve())
              .catch((error) => {
                console.error('Error creating subscribed options:', error);
                reject(error);
              });
          };

          if (toDelete.length === 0) {
            // No existing options to delete, just create new ones
            createNewOptions();
          } else {
            // Delete existing options first
            const deletePromises = toDelete.map(so => 
              this.subscribedOptionsApiService.delete(so.Id).toPromise()
            );
            
            Promise.all(deletePromises)
              .then(() => createNewOptions())
              .catch((error) => {
                console.error('Error deleting existing options:', error);
                // Continue with creating new options even if delete fails
                createNewOptions();
              });
          }
        },
        error: (error) => {
          console.error('Error fetching subscribed options:', error);
          reject(error);
        }
      });
    });
  }

  paymentFrequencies: { label: string, value: string, months: number }[] = [
    { label: 'Mensuel', value: 'Mensuel', months: 1 },
    { label: 'Annuel', value: 'Annuel', months: 12 }
  ];

  // Add missing filterClients for client search
  filterClients() {
    if (!this.searchQuery.trim()) {
      this.filteredClients = [];
      this.showDropdown = false;
      return;
    }
    const query = this.searchQuery.toLowerCase().trim();
    this.filteredClients = this.clients
      .filter(client => client.Name && client.Name.toLowerCase().includes(query))
      .slice(0, 5);
    this.showDropdown = this.filteredClients.length > 0;
  }

  // Add missing onClearSelection for template
  onClearSelection() {
    this.clearSelection();
  }

  // Fixed ngOnInit for OnInit interface
  ngOnInit(): void {
    this.isLoading = true;
    Promise.all([
      this.fetchClients(),
      this.fetchLicences(),
      this.fetchOptions(),
      this.fetchLicenceOptions(),
      this.fetchSubscriptions()
    ]).then(() => {
      // Initialize all options as checked for all licences
      this.licences.forEach(licence => {
        const allOptionIds = this.getOptionsForLicence(licence).map(opt => opt.Id);
        this.checkedOptions[licence.Id] = new Set(allOptionIds);
        this.initialCheckedOptions[licence.Id] = new Set(allOptionIds);
      });
      
      this.isLoading = false;
    }).catch((error) => {
      console.error('Error loading data:', error);
      this.isLoading = false;
    });
  }
}