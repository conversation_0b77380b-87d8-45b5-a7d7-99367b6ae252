<header class="header"> 
  <div class="profile" (click)="toggleProfileMenu()" matTooltip="Profile">
    <mat-icon>account_circle</mat-icon>
    <span class="user-name" *ngIf="!isSidebarCollapsed">{{ UserName }}</span>
  </div>
  <div class="profile-dropdown" *ngIf="profileMenuVisible" [@dropdownAnimation]>
    <div class="profile-item">
      <mat-icon>person</mat-icon>
      <span>{{ userRole }}</span>
    </div>
    <div class="profile-item" (click)="logout()">
      <mat-icon>logout</mat-icon>
      <span>Logout</span>
    </div>
  </div>
</header>
