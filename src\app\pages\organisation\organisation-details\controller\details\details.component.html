<div class="create-form-card">
  <div class="form-container">
    <form>
      <div class="form-grid">
        <div class="form-group">
          <label for="name">Nom du client<span class="required">*</span></label>
          <input
            id="name"
            type="text"
            formControlName="name"
            value="{{ controller?.ClientName }}"
            disabled
          />
        </div>
        <div class="form-group">
          <label for="idlicence">Licence <span class="required">*</span></label>

          <input
            id="name"
            type="text"
            formControlName="name"
            value="{{ controller?.LicenceName }}"
            disabled
          />
        </div>
        <div class="form-group">
          <label for="geographicZone">Nom du Contrôleur :</label>
          <input
            id="geographicZone"
            formControlName="geographicZone"
            value="{{ controller?.ControllerName }}"
            disabled
          />
        </div>

        <div class="form-group">
          <label for="commercialCondition">Base Topic</label>
          <input
            id="commercialCondition"
            formControlName="commercialCondition"
            value="{{ controller?.ControllerBaseTopic }}"
            disabled
          />
        </div>
        <div class="form-group">
          <label for="installationDate">Date d'installation</label>
          <input
            id="installationDate"
            class="form-control"
            [value]="
              controller?.ControllerInstallationDate | date : 'dd-MM-yyyy'
            "
            disabled
          />
        </div>

        <div class="form-group">
          <label for="lastConnection">Dernière connexion</label>
          <input
            id="lastConnection"
            class="form-control"
            [value]="controller?.ControllerLastConnection | date : 'dd-MM-yyyy'"
            disabled
          />
        </div>

        <div class="form-group">
          <label for="status">Statut <span class="required">*</span></label>
          <input
            id="eventType"
            formControlName="eventType"
            disabled
            [value]="controller?.ControllerState ? 'Actif' : 'Inactif'"
          />
        </div>
      </div>
    </form>
  </div>
</div>
