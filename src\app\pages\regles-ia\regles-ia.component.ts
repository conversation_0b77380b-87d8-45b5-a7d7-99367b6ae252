import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';

interface Rule {
  id: number;
  name: string;
  trigger: string;
  actions: string[];
  icons: string[];
  status?: string;
  aiSuggested?: boolean;
  confidence?: number;
}

@Component({
  selector: 'app-regles-ia',
  standalone: true,
  imports: [CommonModule, MatIconModule],
  templateUrl: './regles-ia.component.html',
  styleUrls: ['./regles-ia.component.css']
})
export class ReglesIAComponent {
  activeTab: string = 'active';

  activeRules: Rule[] = [
    {
      id: 1,
      name: "Smart Welcome Home",
      trigger: "Door sensor + Motion detection",
      actions: ["Turn on lights", "Set AC to 21°C", "Play welcome music"],
      icons: ['door_open', 'motion_sensor', 'lightbulb', 'thermostat', 'volume_up'],
      status: "active",
      aiSuggested: true
    },
    // ... Add other active rules similarly
  ];

  aiSuggestions: Rule[] = [
    {
      id: 5,
      name: "Garage Arrival Assistant",
      trigger: "Car in driveway + Garage door opening",
      actions: ["Turn on garage lights", "Pre-heat HVAC", "Unlock front door"],
      icons: ['directions_car', 'door_open', 'lightbulb', 'thermostat', 'lock'],
      confidence: 95
    },
    // ... Add other AI suggestions similarly
  ];

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }
}