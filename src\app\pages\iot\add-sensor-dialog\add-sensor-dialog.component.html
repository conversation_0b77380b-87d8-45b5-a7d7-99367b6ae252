<div class="dialog-container">
  <h2 class="dialog-title">Ajouter un Nouveau Capteur</h2>
  <form [formGroup]="form" (ngSubmit)="save()" class="sensor-form">
    <div class="form-scrollable">
      <!-- First row - Name and Type side by side -->
      <div class="form-row pair">
        <mat-form-field appearance="outline" class="form-field name-field">
          <mat-label>Nom du Capteur</mat-label>
          <input matInput formControlName="name" placeholder="Ex: Capteur de Température">
          <mat-error *ngIf="form.get('name')?.hasError('required')">Le nom est requis</mat-error>
          <mat-error *ngIf="form.get('name')?.hasError('minlength')">Minimum 3 caractères</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="form-field type-field">
          <mat-label>Type de Capteur</mat-label>
          <mat-select formControlName="type">
            <mat-option *ngFor="let type of sensorTypes" [value]="type">{{ type }}</mat-option>
          </mat-select>
          <mat-error *ngIf="form.get('type')?.hasError('required')">Le type est requis</mat-error>
        </mat-form-field>
      </div>

      <!-- Description - Full width -->
      <mat-form-field appearance="outline" class="form-field description-field">
        <mat-label>Description</mat-label>
        <textarea matInput formControlName="description" placeholder="Description du capteur" rows="3"></textarea>
      </mat-form-field>

      <!-- Second row - Manufacturer and Range side by side -->
      <div class="form-row pair">
        <mat-form-field appearance="outline" class="form-field manufacturer-field">
          <mat-label>Fabricant</mat-label>
          <input matInput formControlName="fabricant" placeholder="Ex: Aqara">
        </mat-form-field>

        <mat-form-field appearance="outline" class="form-field range-field">
          <mat-label>Plage de Mesure</mat-label>
          <input matInput formControlName="measurementRange" placeholder="Ex: 0-100°C">
        </mat-form-field>
      </div>

      <!-- Image section -->
      <div class="image-section">
        <h3 class="section-title">Image du Capteur</h3>
        
        <div class="upload-container">
          <button mat-stroked-button type="button" (click)="triggerFileInput()" [disabled]="isUploading" class="upload-button">
            <mat-icon>cloud_upload</mat-icon>
            <span class="span-upload">Ajouter un Capteur</span>
          </button>
          <input #fileInput type="file" accept="image/*" style="display: none;" (change)="onFileSelected($event)">
          <mat-spinner diameter="20" *ngIf="isUploading" class="upload-spinner"></mat-spinner>
        </div>

        <div class="image-preview" *ngIf="uploadedImagePreview">
          <p class="preview-label">Aperçu :</p>
          <img [src]="uploadedImagePreview" alt="Uploaded Image Preview" class="preview-image">
        </div>

        <div class="image-gallery" *ngIf="images.length > 0">
          <h4 class="gallery-title">Images disponibles</h4>
          <div class="gallery-grid">
            <div *ngFor="let image of images" class="gallery-item" 
                [class.selected]="selectedImage === image" 
                (click)="chooseImage(image)">
              <img [src]="image" alt="Sensor Image" class="gallery-image">
            </div>
          </div>
        </div>

        <div class="no-images" *ngIf="images.length === 0 && !uploadedImagePreview">
          <mat-icon class="no-images-icon">image_not_supported</mat-icon>
          <p>Aucune image disponible</p>
        </div>
        
        <mat-error *ngIf="form.get('imageURL')?.hasError('required') && form.get('imageURL')?.touched" class="image-error">
          Veuillez sélectionner ou télécharger une image
        </mat-error>
      </div>
    </div>

    <div class="dialog-actions">
      <button mat-stroked-button type="button" (click)="cancel()" class="action-button cancel">
        Annuler
      </button>
      <button mat-raised-button color="primary" type="submit" [disabled]="form.invalid || isUploading" class="action-button submit">
        <mat-icon>save</mat-icon>
        Enregistrer
      </button>
    </div>
  </form>
</div>