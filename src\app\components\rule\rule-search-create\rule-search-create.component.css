/* === SEARCH AND CREATE SECTION === */

.search-create {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  margin-top: 24px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.create-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.ai-create-button {
  background: linear-gradient(45deg, var(--primary), var(--primary-light)) !important;
  color: white !important;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(46, 125, 50, 0.3);
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 0 20px;
  height: 40px;
}

.ai-create-button:hover:not(:disabled) {
  background: linear-gradient(45deg, var(--primary-dark), var(--primary)) !important;
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.4);
  transform: translateY(-1px);
}

.ai-create-button:disabled {
  background: var(--grey-light) !important;
  color: var(--text-secondary) !important;
  box-shadow: none;
}

.ai-create-button mat-icon {
  margin-right: 8px;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { 
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  50% { 
    transform: scale(1.1) rotate(180deg);
    opacity: 0.8;
  }
}

.create-button {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: white !important;
  font-weight: 500;
  border-radius: 8px;
  padding: 0 20px;
  height: 40px;
  transition: all 0.3s ease;
}

.create-button:hover:not(:disabled) {
  background: linear-gradient(45deg,#81c784, var(--primary)) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(46, 125, 50, 0.3);
}

.create-button mat-icon {
  margin-right: 8px;
}

.search-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.input {
  flex: 1;
  min-width: 200px;
  padding: 12px 16px;
  padding-right: 40px;
  border: 1px solid var(--card-border);
  border-radius: 6px;
  font-size: 14px;
  color: var(--text-primary);
  background: var(--white);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.input:focus {
  border-color: var(--green-main);
  outline: none;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.input:disabled {
  background: var(--background);
  color: var(--text-secondary);
  cursor: not-allowed;
  opacity: 0.7;
}

.search-loading {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.loading-icon {
  font-size: 18px !important;
  width: 18px !important;
  height: 18px !important;
  color: var(--green-main);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design for mobile */
@media (max-width: 768px) {
  .search-create {
    flex-direction: column;
    align-items: stretch;
  }
  
  .create-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .ai-create-button,
  .create-button {
    flex: 1;
    min-width: 160px;
  }

  .input {
    min-width: unset;
  }
}