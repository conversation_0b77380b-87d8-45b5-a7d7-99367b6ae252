// src/app/core/services/custom-mqtt.service.ts
import { Injectable } from '@angular/core';
import { IMqttMessage, MqttService, MqttConnectionState } from 'ngx-mqtt';
import { Observable, Subscription } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CustomMqttService {
  private isConnected: boolean = false; // Track connection state
  readonly stateSubscription: Subscription;

  constructor(readonly mqttService: MqttService) {
    // Subscribe to connection state changes
    this.stateSubscription = this.mqttService.state.subscribe((state: MqttConnectionState) => {
      this.isConnected = state === 1; // 1 = CONNECTED
      console.log(`MQTT Connection State Changed: ${state === 1 ? 'Connected' : 'Disconnected'}`);
    });

    // Log connection events
    this.mqttService.onConnect.subscribe(() => {
      console.log('MQTT Connected to broker at **************:8083');
    });
    this.mqttService.onError.subscribe((error) => {
      console.error('MQTT Connection Error:', error);
    });
    this.mqttService.onClose.subscribe(() => {
      console.log('MQTT Connection Closed');
    });
  }

  public connect(): void {
    if (!this.isConnected) {
      console.log('Attempting to connect to MQTT broker...');
      this.mqttService.connect();
    } else {
      console.log('Already connected to MQTT broker');
    }
  }

  publish(topic: string, message: string): void {
    if (!this.isConnected) {
      console.warn('MQTT not connected, attempting to reconnect before publishing...');
      this.connect();
    }
    try {
      console.log('Publishing to topic:', topic, 'with message:', message);
      this.mqttService.unsafePublish(topic, message, { qos: 1, retain: false });
      console.log('Message published successfully');
    } catch (error) {
      console.error('Failed to publish MQTT message:', error);
    }
  }

  disconnect(): void {
    this.mqttService.disconnect();
  }

  observe(topic: string): Observable<IMqttMessage> {
    return this.mqttService.observe(topic);
  }

  ngOnDestroy(): void {
    if (this.stateSubscription) {
      this.stateSubscription.unsubscribe();
    }
    this.disconnect();
  }
}