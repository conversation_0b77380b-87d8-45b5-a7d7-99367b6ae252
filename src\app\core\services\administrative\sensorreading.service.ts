import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';import { ApiService } from '../api.service';
import { SensorReading } from '@app/core/models/SensorReading';

@Injectable({ providedIn: 'root' })
export class SensorReadingApiService extends ApiService<SensorReading> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("sensor-reading");
  }
}

