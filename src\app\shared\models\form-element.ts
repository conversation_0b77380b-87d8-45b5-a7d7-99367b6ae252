export interface FormElement {
  name: string;                 // The control name in the form
  libelle: string;              // Label to display
  isRequired?: boolean;         // If the field is required
  isNotUnique?: boolean;        // If the value must be unique
  checkUnique?: boolean;        // Trigger to check uniqueness
  type?: 'string' | 'number' | 'select' | 'date' | 'boolean'; // Control type (better typed)
  target?: string;              // Possibly related field or API
  source?: string;              // Source for dynamic data (API endpoint, etc.)
  items?: any[];                // Used for dropdown/select options
  bindLabel?: string;           // Label to bind in a select (e.g. 'name')
  bindValue?: string;           // Value to bind in a select (e.g. 'id')
  max?: number;                 // Max value or length
  min?: number;                 // Min value or length
  defaultValue?: string | number | boolean; // Default value
  pattern?: string | RegExp; // ← ajout ici
}
