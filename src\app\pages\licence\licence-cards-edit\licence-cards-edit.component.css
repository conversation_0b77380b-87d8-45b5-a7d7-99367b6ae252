:host {
  display: block;
  font-family: '<PERSON><PERSON>', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #2d3748;
  background-color: #f9fafb;
  min-height: 100vh;
  height: 100%;
}

.container {
  max-width: none;
  width: 100%;
  margin: 0;
  padding: 0;
  height: 120%;
  display: flex;
  flex-direction: column;
}

/* Header improvements */
.improved-header {
  background: linear-gradient(90deg, #f0fdf4 60%, #e0f2fe 100%);
  border-radius: 0 0 24px 24px;
  padding: 2.5rem 2rem 2rem 2rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 24px rgba(16,185,129,0.07);
  text-align: center;
}

.improved-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 2.2rem;
  font-weight: 800;
  color: #10b981;
  margin-bottom: 0.5rem;
  letter-spacing: 0.01em;
}

.improved-subtitle {
  font-family: '<PERSON><PERSON>', sans-serif;
  font-size: 1.1rem;
  color: #64748b;
  margin-bottom: 0;
  font-weight: 500;
  letter-spacing: 0.01em;
}

.client-billing-section {
  padding: 1.5rem;
  background: white;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.search-container {
  display: flex;
  justify-content: flex-start;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.search-input-container {
  position: relative;
  flex: 0 1 auto;
  min-width: 300px;
  transition: all 0.3s ease;
  margin-right: auto;
}

/* Search input glass effect */
.search-input-glass-wrapper {
  position: relative;
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  z-index: 10;
}

.search-input.glass {
  width: 100%;
  padding: 0.95rem 2.7rem 0.95rem 3.3rem;
  border: none;
  border-radius: 18px;
  font-size: 1.12rem;
  background: rgba(255,255,255,0.25);
  box-shadow: 0 8px 32px 0 rgba(31,38,135,0.10);
  color: black;
  font-weight: 500;
  letter-spacing: 0.01em;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1.5px solid rgba(16,185,129,0.18);
  transition: box-shadow 0.2s, border 0.2s, background 0.2s;
  outline: none;
  font-family: 'Lato', sans-serif;
}

.search-input.glass:focus {
  border: 1.5px solid rgba(16,185,129,0.18);
  background: transparent;
  box-shadow: none;
}

.search-input-glass-icon {
  position: absolute;
  left: 1.1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #10b981;
  font-size: 1.6rem;
  pointer-events: none;
  z-index: 2;
}

.search-input-glass-clear-btn {
  position: absolute;
  right: 1.1rem;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: #a0aec0;
  font-size: 1.35rem;
  cursor: pointer;
  padding: 0;
  z-index: 2;
  transition: color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-input-glass-clear-btn:hover {
  color: #ef4444;
}

.choose-client-error {
  color: #ef4444;
  font-size: 0.97rem;
  margin-top: 0.35rem;
  margin-left: 0.2rem;
  font-weight: 600;
  letter-spacing: 0.01em;
  font-family: 'Lato', sans-serif;
}

.input-error {
  border: 2px solid #ef4444 !important;
  background: #fef2f2 !important;
}

/* Dropdown glass effect */
.dropdown-glass {
  position: absolute;
  top: calc(100% + 1px);
  left: 0;
  width: 120%;
  min-width: 260px;
  background: rgba(255,255,255,0.85);
  border-radius: 18px;
  box-shadow: 0 8px 32px 0 rgba(31,38,135,0.13);
  border: 1.5px solid #e0f2fe;
  padding: 0.5rem 0;
  z-index: 1001;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  animation: dropdownGlassFadeIn 0.25s cubic-bezier(.4,2,.6,1) both;
  max-height: 320px;
  overflow-y: auto;
}

@keyframes dropdownGlassFadeIn {
  from { opacity: 0; transform: translateY(-10px) scale(0.98);}
  to   { opacity: 1; transform: translateY(0) scale(1);}
}

.dropdown-glass-item {
  display: flex;
  align-items: center;
  gap: 1.1rem;
  padding: 0.7rem 1.3rem;
  cursor: pointer;
  border-radius: 12px;
  transition: background 0.18s, box-shadow 0.18s;
  position: relative;
}

.dropdown-glass-item:hover {
  background: transparent;
  box-shadow: none;
}

.dropdown-glass-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.dropdown-glass-name {
  font-weight: 600;
  font-size: 1.04rem;
  color: #065f46;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: 0.01em;
  font-family: 'Lato', sans-serif;
}

/* Selected client card */
.improved-client-card {
  display: flex;
  align-items: center;
  background: #edfff9;
  border-radius: 12px;
  padding: 0.75rem 1rem;
  box-shadow: 0 4px 16px rgba(16,185,129,0.08);
  border: 1.5px solid #10b981;
  min-width: 220px;
  position: relative;
  gap: 0.75rem;
}

.client-details-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
}

.client-name-row {
  display: flex;
  align-items: center;
  gap: 0.7rem;
  margin-bottom: 0.15rem;
}

.client-name-large {
  font-weight: 700;
  font-size: 1.13rem;
  color: #065f46;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: 'Montserrat', sans-serif;
}

.client-subtitle {
  font-size: 0.93rem;
  color: #64748b;
  margin-top: 2px;
  font-weight: 500;
  letter-spacing: 0.01em;
  font-family: 'Lato', sans-serif;
}

/* Payment frequency selection */
.improved-select-type-frequence-payement {
  margin-top: 1.2rem;
}

.improved-frequence-payement {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.improved-frequence-inline {
  flex-direction: row !important;
  align-items: center !important;
  gap: 1.2rem;
}

.improved-select {
  font-family: 'Lato', sans-serif;
  font-size: 1.08rem;
  padding: 0.7rem 1.2rem;
  border-radius: 12px;
  border: 1.5px solid #bbf7d0;
  background: #f0fdf4;
  color: #065f46;
  font-weight: 600;
  outline: none;
  transition: border 0.2s, box-shadow 0.2s;
  min-width: 180px;
  box-shadow: 0 2px 8px rgba(16,185,129,0.07);
}

.improved-select:focus {
  border: 2px solid #10b981;
  background: #edfff9;
}

/* License grid */
.license-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin: 2rem auto;
  max-width: 1200px;
  padding: 0 1.5rem;
  width: 100%;
  overflow: visible !important;
}

.improved-license-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start;
  gap: 2.5rem;
  margin: 2rem auto;
  max-width: 1200px;
  padding: 0 1.5rem;
  width: 100%;
}

.improved-license-grid.single-card {
  justify-content: center;
  align-items: center;
  min-height: 350px;
}

.license-card {
  position: relative;
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-top: 30px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0,0,0,0.04);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), 
              transform 0.4s cubic-bezier(.4,2,.6,1);
  will-change: transform, opacity;
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.improved-license-card {
  width: 350px;
  max-width: 100%;
  min-width: 320px;
  margin: 0 auto;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 20px rgba(0,0,0,0.04);
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), 
              transform 0.4s cubic-bezier(.4,2,.6,1);
  height: 100%;
  min-height: 450px;
  justify-content: flex-start;
}

.improved-license-card.single-card-inner {
  margin: 0 auto;
  min-width: 350px;
  max-width: 400px;
  width: 100%;
}

.license-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 28px rgba(0,0,0,0.1);
}

.license-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%; 
  height: 6px;
  background: #e2e8f0; 
}

.license-card:nth-child(3n+1)::before {
  background: #49b38e;
}

.license-card:nth-child(3n+2)::before {
  background: #3b82f6;
}

.license-card:nth-child(3n+3)::before {
  background: #8b5cf6;
}

/* Card header and description */
.card-header {
  margin-bottom: 1.5rem;
  text-align: center;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f1f5f9;
}

.improved-card-header {
  margin-bottom: 1.2rem;
  text-align: center;
  padding-bottom: 0.7rem;
  border-bottom: 1px solid #f1f5f9;
}

.card-header h3 {
  font-size: 1.5rem;
  margin-bottom: 0.75rem;
  color: #1e293b;
}

.improved-card-header .improved-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.3rem;
  font-weight: 700;
  color: #10b981;
  margin-bottom: 0.3rem;
  letter-spacing: 0.01em;
}

.card-description-wrapper {
  min-height: 80px;
  max-height: 80px;
  display: flex;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.card-description {
  color: #64748b;
  font-size: 0.95rem;
  line-height: 1.5;
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  margin: 0;
  overflow-wrap: break-word;
  word-wrap: break-word;
  hyphens: auto;
  text-align: justify;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.improved-card-description {
  font-family: 'Lato', sans-serif;
  color: #64748b;
  font-size: 0.97rem;
  margin-bottom: 0;
  line-height: 1.4;
  font-weight: 400;
  overflow-wrap: break-word;
  word-wrap: break-word;
  hyphens: auto;
  text-align: justify;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Features list */
.features-list {
  list-style: none;
  padding: 0;
  margin: 0 0 1rem 0;
  flex-grow: 1;
  min-height: 120px;
}

.features-list-wrapper {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-height: 150px;
}

.improved-features-list {
  margin-top: 0.5rem;
  margin-bottom: 1.2rem;
  min-height: 150px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f1f5f9;
  gap: 0.75rem;
}

.custom-checkbox {
  display: block;
  position: relative;
  cursor: pointer;
  user-select: none;
  margin-right: 0.5rem;
}

.custom-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.custom-checkbox input:disabled {
  background: #e5e7eb !important;
  color: #a1a1aa !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
  opacity: 0.7;
}

.checkmark {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20px;
  width: 20px;
  border-radius: 5px;
  background-color: #f1f5f9;
  transition: all 0.2s;
}

.custom-checkbox input:checked ~ .checkmark {
  background-color: #10b981;
  border-color: #10b981;
}

.custom-checkbox input:checked ~ .checkmark .verified-icon {
  opacity: 1;
  transform: scale(1);
}

.custom-checkbox input:not(:checked) ~ .checkmark .verified-icon {
  opacity: 0;
  transform: scale(0.8);
}

.custom-checkbox input:disabled ~ .checkmark {
  background-color: #e2e8f0;
  cursor: not-allowed;
}

.verified-icon, .cancel-icon {
  font-size: 18px;
  color: white;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.2s;
}

.custom-checkbox input:checked ~ .checkmark .verified-icon,
.custom-checkbox input:not(:checked) ~ .checkmark .cancel-icon {
  opacity: 1;
  transform: scale(1);
}

.feature-details {
  flex-grow: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.feature-name {
  font-size: 1.2rem;
  color: #334155;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
}

.checked-green {
  background-color: #d1fae5 !important;
  border: 2px solid #10b981 !important;
}

.unchecked-red {
  background-color: #fee2e2 !important;
  border: 2px solid #ef4444 !important;
}

.checked-icon {
  color: #10b981 !important;
  opacity: 1 !important;
  transform: scale(1) !important;
}

.unchecked-icon {
  color: #ef4444 !important;
  opacity: 1 !important;
  transform: scale(1) !important;
}

/* License actions */
.license-actions {
  margin-top: auto;
  padding-top: 1rem;
}

.select-btn {
  width: 100%;
  padding: 0.75rem;
  border: none;
  border-radius: 10px;
  background: linear-gradient(135deg, #49b38e, #2c7744);
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  margin-top: auto;
  font-size: 0.95rem;
}

.select-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 18px rgba(73, 179, 142, 0.3);
}

.select-btn:disabled {
  background: #e5e7eb !important;
  color: #a1a1aa !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
  opacity: 0.7;
}

/* Overlay and popups */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.confirmation-popup {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  position: relative;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f1f5f9;
}

.popup-header h3 {
  font-size: 1.4rem;
  color: #1e293b;
  margin: 0;
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
}

.close-popup-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background: #f8fafc;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s;
}

.close-popup-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.popup-content {
  margin-bottom: 2rem;
}

.confirmation-message {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.confirmation-icon {
  color: #f59e0b;
  font-size: 2rem;
  margin-top: 0.2rem;
}

.confirmation-message p {
  font-size: 1rem;
  color: #475569;
  line-height: 1.6;
  margin: 0;
  font-family: 'Lato', sans-serif;
}

.client-license-summary {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.25rem;
  border: 1px solid #e2e8f0;
}

.summary-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 600;
  color: #374151;
  font-size: 1rem;
  font-family: 'Lato', sans-serif;
}

.client-info-summary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #065f46;
}

.client-image {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #d1fae5;
}

.license-name {
  font-weight: 600;
  color: #1e293b;
  background: linear-gradient(135deg, #49b38e, #2c7744);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.license-total {
  font-weight: 700;
  color: #10b981;
  font-size: 1.1rem;
}

.popup-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.cancel-btn, .confirm-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  font-family: 'Lato', sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 0.95rem;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cancel-btn {
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.cancel-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.confirm-btn {
  background: linear-gradient(135deg, #49b38e, #2c7744);
  color: white;
}

.confirm-btn:disabled {
  background: #e5e7eb !important;
  color: #a1a1aa !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
  opacity: 0.7;
}

.confirm-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 18px rgba(73, 179, 142, 0.3);
}

/* Success notification */
.success-notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #10b981;
  z-index: 3000;
  max-width: 400px;
  width: 90%;
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.success-icon {
  color: #10b981;
  font-size: 2rem;
  margin-top: 0.2rem;
}

.notification-text {
  flex: 1;
}

.notification-text h4 {
  font-size: 1.1rem;
  color: #065f46;
  margin: 0 0 0.25rem 0;
  font-weight: 700;
  font-family: 'Montserrat', sans-serif;
}

.notification-text p {
  font-size: 0.9rem;
  color: #374151;
  margin: 0;
  line-height: 1.5;
  font-family: 'Lato', sans-serif;
}

.close-notification-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: none;
  background: #f0fdf4;
  color: #10b981;
  cursor: pointer;
  transition: all 0.2s;
  margin-left: auto;
  flex-shrink: 0;
}

.close-notification-btn:hover {
  background: #dcfce7;
  color: #059669;
}

/* Pagination controls */
.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.2rem;
  margin: 6rem 0 2.5rem 0;
  user-select: none;
}

.improved-pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  margin: 2.5rem 0 1.5rem 0;
  user-select: none;
}

.pagination-btn {
  background: #f0fdf4;
  border: none;
  border-radius: 50%;
  width: 38px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  color: #10b981;
  font-size: 1.7rem;
  cursor: pointer;
  transition: background 0.18s, color 0.18s, box-shadow 0.18s;
  box-shadow: 0 2px 8px rgba(16,185,129,0.07);
  visibility: visible !important;
  opacity: 1 !important;
}

.improved-pagination-btn {
  background: #f0fdf4;
  color: #10b981;
  border: none;
  border-radius: 50%;
  width: 38px;
  height: 38px;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.18s, color 0.18s, box-shadow 0.18s;
}

.pagination-btn:disabled,
.improved-pagination-btn:disabled {
  background: #e5e7eb;
  color: #a1a1aa;
  cursor: not-allowed;
  opacity: 0.7;
}

.pagination-btn:hover:not(:disabled),
.improved-pagination-btn:hover:not(:disabled) {
  background: #bbf7d0;
  color: #059669;
}

.pagination-indicator {
  display: flex;
  gap: 0.5rem;
}

.improved-pagination-indicator {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.25rem;
  color: #10b981;
  font-weight: 700;
  letter-spacing: 0.03em;
}

.improved-pagination-page-number {
  padding: 0 0.7rem;
  font-size: 1.25rem;
  font-family: 'Montserrat', sans-serif;
  color: #10b981;
  font-weight: 700;
  border-radius: 8px;
  background: #f0fdf4;
  border: 1.5px solid #bbf7d0;
  min-width: 36px;
  text-align: center;
  display: inline-block;
}

.pagination-dot {
  width: 13px;
  height: 13px;
  border-radius: 50%;
  background: #e0e7ef;
  border: none;
  margin: 0 2px;
  cursor: pointer;
  transition: background 0.2s, transform 0.2s;
  outline: none;
  box-shadow: 0 1px 4px rgba(16,185,129,0.07);
}

/* Add these styles to your subscription-table.component.css */

/* Subscription Selection Popup Styles */
.subscription-selection-popup {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  max-width: 700px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  position: relative;
}

.subscription-search-section {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f1f5f9;
}

.subscription-search-bar {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.subscription-search-input {
  flex: 1;
  min-width: 250px;
  padding: 0.75rem 1rem;
  border: 1.5px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  font-family: 'Lato', sans-serif;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.subscription-search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.subscription-status-filter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.subscription-filter-select {
  padding: 0.75rem 1rem;
  border: 1.5px solid #e2e8f0;
  border-radius: 12px;
  background: white;
  font-size: 1rem;
  font-family: 'Lato', sans-serif;
  color: #374151;
  min-width: 150px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.subscription-filter-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.subscriptions-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #f1f5f9;
  border-radius: 12px;
  background: #fafbfc;
}

.subscription-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  border-bottom: 1px solid #f1f5f9;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
  margin-bottom: 1px;
}

.subscription-item:last-child {
  border-bottom: none;
  border-radius: 0 0 12px 12px;
}

.subscription-item:first-child {
  border-radius: 12px 12px 0 0;
}

.subscription-item:hover {
  background: #f0f7ff;
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.subscription-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.subscription-client {
  font-size: 1.1rem;
  color: #1e293b;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
}

.subscription-details {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.subscription-licence {
  font-size: 0.9rem;
  color: #3b82f6;
  font-weight: 500;
  background: #eff6ff;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: 1px solid #dbeafe;
}

.subscription-period {
  font-size: 0.85rem;
  color: #64748b;
  font-family: 'Lato', sans-serif;
}

.subscription-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  font-family: 'Lato', sans-serif;
}

.status-pending {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

.status-paid {
  background: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.subscription-price {
  font-size: 1rem;
  font-weight: 700;
  color: #10b981;
  font-family: 'Montserrat', sans-serif;
}

.no-subscriptions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  color: #9ca3af;
  text-align: center;
}

.no-subscriptions .material-icons {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-subscriptions p {
  font-size: 1.1rem;
  font-family: 'Lato', sans-serif;
  margin: 0;
}

/* Confirmation popup value styling */
.summary-item .value {
  font-weight: 600;
  color: #1e293b;
}

/* Button styling for new modify button */
.improved-confirm-btn[style*="background: linear-gradient(135deg, #3b82f6, #1d4ed8)"] {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
}

.improved-confirm-btn[style*="background: linear-gradient(135deg, #3b82f6, #1d4ed8)"]:hover {
  background: linear-gradient(135deg, #1d4ed8, #3b82f6) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 18px rgba(59, 130, 246, 0.3);
}

/* Responsive design for popup */
@media (max-width: 768px) {
  .subscription-selection-popup {
    max-width: 95%;
    padding: 1.5rem;
    max-height: 85vh;
  }
  
  .subscription-search-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .subscription-search-input {
    min-width: auto;
  }
  
  .subscription-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .subscription-status {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  
  .subscription-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

.pagination-dot.active,
.pagination-dot:focus {
  background: linear-gradient(90deg, #10b981 60%, #34d399 100%);
  transform: scale(1.18);
  box-shadow: 0 2px 8px rgba(16,185,129,0.13);
}

/* Responsive design */
@media (max-width: 768px) {
  .improved-license-card {
    width: 100%;
    min-width: 280px;
  }
  
  .search-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .search-input-container {
    margin-right: 0;
  }
}