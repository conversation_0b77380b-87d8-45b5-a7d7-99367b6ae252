import { Component, OnInit, ViewChild } from '@angular/core';
import { SensorDataService } from '../../core/services/sensordata.service';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatSelectModule } from '@angular/material/select';
import { CommonModule, DatePipe } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

@Component({
  selector: 'app-sensor-data-table',
  templateUrl: './sensor-data-table.component.html',
  styleUrls: ['./sensor-data-table.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    DatePipe,
  ],
})
export class SensorDataTableComponent implements OnInit {
  tables: string[] = [];
  sensorData: any[] = [];
  selectedTable: string = '';
  dataSource = new MatTableDataSource<any>();
  displayedColumns: string[] = [
    'sensorType',
    'value',
    'unit',
    'battery',
    'timestamp',
  ];

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(private sensorDataService: SensorDataService) {}

  ngOnInit(): void {
    this.loadTables();
  }

  loadTables(): void {
    this.sensorDataService.getAvailableSensorTables().subscribe({
      next: (data) => (this.tables = data),
      error: (err) => console.error(err),
    });
  }

  loadSensorData(): void {
    if (this.selectedTable) {
      this.sensorDataService.getSensorData(this.selectedTable).subscribe({
        next: (data) => {
          this.sensorData = data;
          this.dataSource.data = data;
          this.dataSource.paginator = this.paginator;
          this.dataSource.sort = this.sort;
        },
        error: (err) => console.error(err),
      });
    }
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
}
