<!-- HTML Template -->
<div class="dashboard-container">
  <!-- Header -->
  <div class="statistics-header">
    <h1 class="statistics-title">Statistique:</h1>
  </div>

  <div class="dashboard-grid">
    <!-- Cards Container - Flex with wrapping when more than 4 -->
    <div class="cards-container">
      <div class="card" *ngFor="let sale of salesData">
        <div class="card-header">
          <div class="card-title">{{ sale.title }}</div>
          <div class="card-icon">
            <i [class]="sale.icon" [style.color]="sale.iconColor"></i>
          </div>
        </div>
        <div class="card-value">
          <span class="amount-text">{{ sale.amount }}</span>
          <span class="kwh-unit" *ngIf="sale.unit">{{ sale.unit }}</span>
        </div>
        <div class="percentage positive" *ngIf="sale.percentage && sale.percentage > 0">
          ↑ +{{ sale.percentage }}%
        </div>
        <div class="percentage negative" *ngIf="sale.percentage && sale.percentage < 0">
          ↓ {{ sale.percentage }}%
        </div>
        <div class="na-badge" *ngIf="!sale.percentage">
          N/A
        </div>
        <div class="objective-section" *ngIf="sale.progress !== undefined">
          <div class="objective-label">
            <div class="objective-dot" [ngClass]="sale.color"></div>
            Objective
          </div>
          <div class="progress-container">
            <div class="progress-bar">
              <div
                class="progress-fill"
                [ngClass]="sale.color"
                [style.width.%]="sale.progress"
              ></div>
            </div>
            <div class="progress-text">{{ sale.progress }}%</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
      <!-- Top Chart (Third Chart) - Full width -->
      <div class="chart-card top-chart">
        <div class="chart-header">
          <div class="chart-title">Consommation Énergétique</div>
          <div class="chart-menu">⋯</div>
        </div>
        <div class="chart-container">
          <canvas #chartCanvas></canvas>
        </div>
      </div>

      <!-- Bottom Charts - Two charts stacked horizontally -->
      <div class="bottom-charts">
        <!-- First Chart -->
        <div class="chart-card">
          <div class="chart-header">
            <div class="chart-title">Dépense énergetique</div>
            <div class="chart-menu">⋯</div>
          </div>
          <div class="chart-container">
            <canvas id="energyLineChart"></canvas>
          </div>
        </div>

        <!-- Second Chart -->
        <div class="chart-card">
          <div class="chart-header">
            <div class="chart-title">Répartition des dépenses énergetique</div>
            <div class="chart-menu">⋯</div>
          </div>
          <div class="chart-container">
            <canvas id="expenseChart"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Map Section - Full width -->
    <div class="chart-card map-section">
      <div class="chart-header">
        <div class="chart-title">Map - Sites</div>
        <div class="chart-menu">⋯</div>
      </div>
      <div class="chart-container">
        <div id="map" style="height: 100%; width: 100%;"></div>
      </div>
    </div>
  </div>
</div>
