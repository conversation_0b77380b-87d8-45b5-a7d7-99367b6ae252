// // src/app/core/services/auth.guard.ts
// import { Injectable } from '@angular/core';
// import { CanActivate, Router, ActivatedRouteSnapshot } from '@angular/router';
// import { AuthService } from './auth.service';

// @Injectable({
//   providedIn: 'root'
// })
// export class AuthGuard implements CanActivate {
//   constructor(private authService: AuthService, private router: Router) {}

//   canActivate(route: ActivatedRouteSnapshot): boolean {
//     if (!this.authService.isLoggedIn()) {
//       this.router.navigate(['/login']);
//       return false;
//     }

//     const roles = this.authService.getRoles();
//     const requiredRoles = route.data['roles'] as string[];
//     console.log('AuthGuard - Roles:', roles, 'Required Roles:', requiredRoles);

//     if (requiredRoles && !requiredRoles.some(role => roles.includes(role))) {
//       this.authService.showMessage('Access denied. Insufficient permissions.', 'Close');
//       this.router.navigate(['/dashboard']);
//       return false;
//     }

//     return true;
//   }
// }
// src/app/core/services/auth.guard.ts
import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { AuthService } from './auth.service';
import { MessageService } from 'primeng/api';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  constructor(
    readonly authService: AuthService,
    readonly router: Router,
    readonly messageService: MessageService
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean {
    const isLoginRoute = state.url.includes('/login');

    // If trying to access login page while already logged in
    if (isLoginRoute && this.authService.isLoggedIn()) {
      this.messageService.add({
        severity: 'info',
        summary: 'Already Logged In',
        detail: 'You are already logged in',
        life: 3000
      });
      this.router.navigate(['/accueil']);
      return false;
    }

    // If trying to access protected route while not logged in
    if (!isLoginRoute && !this.authService.isLoggedIn()) {
      localStorage.setItem('redirectUrl', state.url);
      this.messageService.add({
        severity: 'warn',
        summary: 'Authentication Required',
        detail: 'Please log in to access this page',
        life: 3000
      });
      this.router.navigate(['/login']);
      return false;
    }

    // Check roles for protected routes (non-login routes)
    if (!isLoginRoute) {
      const requiredRoles = route.data['roles'] as string[];
      if (requiredRoles && !this.authService.hasAnyRole(requiredRoles)) {
        this.messageService.add({
          severity: 'error',
          summary: 'Access Denied',
          detail: 'You do not have permission to access this page',
          life: 5000
        });
        this.router.navigate(['/accueil']);
        return false;
      }
    }

    return true;
  }
}