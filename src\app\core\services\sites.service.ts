import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { catchError, Observable } from 'rxjs';
import { Site } from '../models/site';

@Injectable({
  providedIn: 'root',
})
export class SiteService {
  readonly apiUrl = 'https://localhost:7199/api/Site';

  constructor(readonly http: HttpClient) {}

  generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  getSites(): Observable<Site[]> {
    return this.http.get<Site[]>(this.apiUrl);
  }

  getSite(id: string): Observable<Site> {
    return this.http.get<Site>(`${this.apiUrl}/${id}`);
  }

  createSite(site: Site): Observable<Site> {
    site.Id = this.generateUUID();
    return this.http.post<Site>(this.apiUrl, site);
  }

  updateSite(id: string, site: Site): Observable<Site> {
    return this.http.put<Site>(`${this.apiUrl}/${id}`, site).pipe(
      catchError((error) => {
        console.error('Error updating site:', error);
        throw error;
      })
    );
  }

  deleteSite(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  getImageUrl(base64Image: string, contentType: string = 'image/jpeg'): string {
    return `data:${contentType};base64,${base64Image}`;
  }

  getIconForCategory(category: string): string {
    const iconMap: Record<string, string> = {
      Bureau: 'business',
      Entrepot: 'warehouse',
      Usine: 'factory',
      Magasin: 'store',
      Residence: 'home',
      CentreCommercial: 'shopping_cart',
      Restaurant: 'restaurant',
      Hotel: 'hotel',
      Ecole: 'school',
      Hopital: 'local_hospital',
      Maison: 'house',
    };
    return iconMap[category] || 'location_on';
  }

  getColorForCategory(category: string): string {
    const colorMap: Record<string, string> = {
      Bureau: 'var(--primary)',
      Entrepot: '#FF9800',
      Usine: '#F44336',
      Magasin: '#2196F3',
      Residence: '#9C27B0',
      CentreCommercial: '#FFEB3B',
      Restaurant: '#E91E63',
      Hotel: '#3F51B5',
      Ecole: '#009688',
      Hopital: '#607D8B',
      Maison: '#795548',
    };
    return colorMap[category] || 'var(--primary)';
  }
}
