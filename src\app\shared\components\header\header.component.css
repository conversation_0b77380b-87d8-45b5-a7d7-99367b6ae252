/* src/app/shared/components/header/header.component.css */
:root {
  --primary: #4a90e2; /* Match sidebar */
  --primary-dark: #357abd;
  --primary-light: #6ab0ff;
  --text-color: #333333;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --container-bg: #ffffff;
  --transition: all 0.3s ease;
}

.header {
  background: linear-gradient(90deg, var(--container-bg), #f9fafc);
  padding: 0 1rem;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  box-shadow: 0 2px 6px var(--shadow-color);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px; /* Adjusted for modern look */
  z-index: 1100; /* Below sidebar */
  transition: var(--transition);
}

.header-left {
  display: flex;
  align-items: center;
}

.sidebar-toggle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: var(--container-bg);
  border: 1px solid var(--shadow-color);
  transition: var(--transition);
  margin-left: 8px;
}

.sidebar-toggle:hover {
  background: rgba(74, 144, 226, 0.1);
  transform: scale(1.1);
  box-shadow: 0 2px 8px var(--shadow-color);
}

.sidebar-toggle mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  color: var(--primary);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.theme-toggle,
.notification,
.profile {
  position: relative;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: var(--transition);
  display: flex;
  align-items: center;
}

.theme-toggle:hover,
.notification:hover,
.profile:hover {
  background: rgba(74, 144, 226, 0.1);
  transform: scale(1.05);
}

.theme-toggle mat-icon,
.notification mat-icon,
.profile mat-icon {
  font-size: 22px;
  width: 22px;
  height: 22px;
  color: var(--text-color);
}

.notification {
  position: relative;
}

.badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #ea4335;
  color: white;
  border-radius: 50%;
  font-size: 10px;
  font-weight: 600;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--container-bg);
}

.notification-dropdown,
.profile-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background: var(--container-bg);
  border-radius: 8px;
  box-shadow: 0 4px 12px var(--shadow-color);
  min-width: 180px;
  overflow: hidden;
  z-index: 1200;
  border: 1px solid var(--shadow-color);
}

.notification-item,
.profile-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  color: var(--text-color);
  cursor: pointer;
  transition: var(--transition);
}

.notification-item:hover,
.profile-item:hover {
  background: rgba(74, 144, 226, 0.1);
  color: var(--primary);
}

.notification-item mat-icon,
.profile-item mat-icon {
  font-size: 18px;
  color: #666;
}

.notification-item:hover mat-icon,
.profile-item:hover mat-icon {
  color: var(--primary);
}

.notification-item span,
.profile-item span {
  font-size: 0.875rem;
  font-weight: 500;
}

.profile {
  padding: 6px 12px;
  border-radius: 20px;
  gap: 6px;
  margin-right: 20px;
  background: rgba(74, 144, 226, 0.05);
  display: flex;
  align-items: center;
}

.user-name {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header {
    height: 50px;
    padding: 0 0.5rem;
  }

  .sidebar-toggle {
    width: 36px;
    height: 36px;
    margin-left: 4px;
  }

  .sidebar-toggle mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }

  .header-right {
    gap: 0.5rem;
  }

  .user-name {
    display: none; /* Hide username on mobile */
  }

  .profile {
    padding: 0.5rem;
    border-radius: 50%;
    margin-right: 10px;
  }

  .notification-dropdown,
  .profile-dropdown {
    min-width: 160px;
    right: 8px;
  }
}