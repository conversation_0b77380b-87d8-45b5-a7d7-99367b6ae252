import { Compo<PERSON>, OnInit, ViewChild, ElementRef, ChangeDetectorRef } from '@angular/core';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { SensorDetails, SensorService } from '../../../core/services/sensordetails.service';
import { MatSelectModule } from '@angular/material/select';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { delay } from 'rxjs/operators';

interface SensorImages {
  images: string[];
}

@Component({
  selector: 'app-add-sensor-dialog',
  templateUrl: './add-sensor-dialog.component.html',
  styleUrls: ['./add-sensor-dialog.component.css'],
  standalone: true,
  imports: [
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    ReactiveFormsModule,
    CommonModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule
  ]
})
export class AddSensorDialogComponent implements OnInit {
  form: FormGroup;
  selectedImage: string = '';
  images: string[] = [];
  sensorTypes = ['Temperature', 'Motion', 'Vibration', 'Flood', 'Button', 'Contact Sensor'];
  isUploading: boolean = false;
  uploadedImagePreview: string | ArrayBuffer | null = null;

  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  constructor(
    private fb: FormBuilder,
    private sensorService: SensorService,
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef,
    public dialogRef: MatDialogRef<AddSensorDialogComponent>
  ) {
    this.form = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: [''],
      type: ['', Validators.required],
      imageURL: ['', Validators.required],
      measurementRange: [''],
      fabricant: ['']
    });
  }

  ngOnInit(): void {
    this.loadImages();
  }

  loadImages(): void {
    this.sensorService.getImages().subscribe({
      next: (data: SensorImages) => {
        console.log('Loaded images:', data);
        this.images = data.images ?? [];
        if (this.images.length > 0 && !this.selectedImage) {
          this.selectedImage = this.images[0];
          this.form.patchValue({ imageURL: this.selectedImage });
        }
        this.cdr.detectChanges(); // Force change detection to update the UI
      },
      error: (err) => {
        console.error('Error loading sensor images:', err);
        this.snackBar.open('Erreur lors du chargement des images.', 'Fermer', { duration: 3000 });
        this.images = [];
        this.cdr.detectChanges();
      }
    });
  }

  chooseImage(image: string) {
    this.selectedImage = image;
    this.form.patchValue({ imageURL: image });
    this.uploadedImagePreview = null; // Clear preview if selecting an existing image
    this.cdr.detectChanges();
  }

  onFileSelected(event: any): void {
    const file: File = event.target.files[0];
    if (file) {
      // Validate file type
      const validTypes = ['image/png', 'image/jpeg', 'image/jpg'];
      if (!validTypes.includes(file.type)) {
        this.snackBar.open('Veuillez sélectionner une image au format PNG, JPG ou JPEG.', 'Fermer', { duration: 3000 });
        this.fileInput.nativeElement.value = '';
        return;
      }

      // Preview the image
      const reader = new FileReader();
      reader.onload = (e) => {
        this.uploadedImagePreview = e.target?.result as string;
        this.cdr.detectChanges();
      };
      reader.readAsDataURL(file);

      // Upload the image
      this.isUploading = true;
      const formData = new FormData();
      formData.append('file', file);
      this.http.post<any>('http://localhost:5256/api/SensorDetails/upload-image', formData)
        .subscribe({
          next: (response) => {
            this.isUploading = false;
            const newImageUrl = response.url;
            console.log('Uploaded image URL:', newImageUrl);

            // Add the new image to the images array immediately
            if (!this.images.includes(newImageUrl)) {
              this.images.push(newImageUrl);
            }
            this.selectedImage = newImageUrl;
            this.form.patchValue({ imageURL: newImageUrl });
            this.cdr.detectChanges(); // Force UI update

            // Reload images from the backend to sync
            this.reloadImages();
            this.snackBar.open('Image téléchargée avec succès !', 'Fermer', { duration: 3000 });
          },
          error: (err) => {
            this.isUploading = false;
            console.error('Error uploading image:', err);
            this.snackBar.open('Erreur lors du téléchargement de l\'image.', 'Fermer', { duration: 3000 });
            this.fileInput.nativeElement.value = '';
            this.uploadedImagePreview = null;
            this.cdr.detectChanges();
            this.reloadImages();
          }
        });
    }
  }

  reloadImages(): void {
    console.log('Reloading images...');
    this.sensorService.getImages().pipe(
      delay(500)
    ).subscribe({
      next: (data: SensorImages) => {
        console.log('Reloaded images:', data);
        // Update the images array, but preserve the currently selected image
        const currentSelectedImage = this.selectedImage;
        this.images = data.images ?? [];
        // Ensure the newly uploaded image is in the list
        if (currentSelectedImage && !this.images.includes(currentSelectedImage)) {
          this.images.push(currentSelectedImage);
        }
        if (this.images.length > 0 && !this.selectedImage) {
          this.selectedImage = this.images[0];
          this.form.patchValue({ imageURL: this.selectedImage });
        }
        this.cdr.detectChanges();
      },
      error: (err) => {
        console.error('Error reloading images:', err);
        this.snackBar.open('Erreur lors de la mise à jour des images.', 'Fermer', { duration: 3000 });
        // Don't clear images array to keep the locally added image
        this.cdr.detectChanges();
      }
    });
  }

  triggerFileInput(): void {
    this.fileInput.nativeElement.click();
  }

  save() {
    if (this.form.valid) {
      const sensor: SensorDetails = this.form.value;
      this.sensorService.createSensor(sensor).subscribe({
        next: () => {
          this.snackBar.open('Capteur ajouté avec succès !', 'Fermer', { duration: 3000 });
          this.dialogRef.close(sensor);
        },
        error: (err) => {
          console.error('Error saving sensor:', err);
          this.snackBar.open('Erreur lors de l\'ajout du capteur.', 'Fermer', { duration: 3000 });
          this.dialogRef.close();
        }
      });
    }
  }

  cancel() {
    this.dialogRef.close();
  }
}