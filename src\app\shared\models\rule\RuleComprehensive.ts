
export interface RuleComprehensive {
  RuleId: string;
  Enabled: boolean;
  Priority: number;
  RuleSummary: string; // This will hold the summary of the rule
  RawData: string; // This will hold the JSON for conditions/actions/name
  RuleCreatedAt: string;
  RuleLastUpdatedAt: string | null;
  TotalApplications: number;
  TotalClients: number;
  TotalSites: number;
  TotalLocals: number;
  LastTriggered: string | null;
  SuccessfulApplications: number;
  FailedApplications: number;
  Status: 'active' | 'inactive';
  TagsString: string | null;
}
