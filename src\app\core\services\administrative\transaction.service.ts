import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiService } from '../api.service';
import { Transaction } from '@app/core/models/transaction';

@Injectable({ providedIn: 'root' })
export class TransactionApiService extends ApiService<Transaction> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("transaction");
  }
  apiUrl = this.getFullUrl();

  /**
   * Gets transactions filtered by local ID
   * @param localId The ID of the local entity
   * @returns Observable of transactions for the specified local
   */
  getByLocalId(localId: string): Observable<Transaction[]> {
    return this.http.get<Transaction[]>(`${this.apiUrl}transaction/local/${localId}`);
  }

  /**
   * Gets transactions filtered by controller ID
   * @param controllerId The ID of the controller entity
   * @returns Observable of transactions for the specified controller
   */
  getByControllerId(controllerId: string): Observable<Transaction[]> {
    return this.http.get<Transaction[]>(`${this.apiUrl}transaction/controller/${controllerId}`);
  }

  /**
   * Gets transactions filtered by capteur (sensor) ID
   * @param capteurId The ID of the capteur entity
   * @returns Observable of transactions for the specified capteur
   */
  getByCapteurId(capteurId: string): Observable<Transaction[]> {
    return this.http.get<Transaction[]>(`${this.apiUrl}transaction/capteur/${capteurId}`);
  }

  /**
   * Gets transactions filtered by local ID and controller ID
   * This method filters locally for now, but could be optimized with a dedicated backend endpoint
   * @param localId The ID of the local entity
   * @param controllerId The ID of the controller entity
   * @returns Observable of transactions for the specified local and controller
   */
  getByLocalAndController(localId: string, controllerId: string): Observable<Transaction[]> {
         return this.http.get<Transaction[]>(`${this.apiUrl}transaction/local/${localId}/controller/${controllerId}`);

  }

  /**
   * Gets transactions filtered by local ID and capteur ID
   * This method filters locally for now, but could be optimized with a dedicated backend endpoint
   * @param localId The ID of the local entity
   * @param capteurId The ID of the capteur entity
   * @returns Observable of transactions for the specified local and capteur
   */
  getByLocalAndCapteur(localId: string, capteurId: string): Observable<Transaction[]> {
           return this.http.get<Transaction[]>(`${this.apiUrl}transaction/local/${localId}/capteur/${capteurId}`);

  }
  /**
   * Gets transactions filtered by controller ID and capteur ID
   * This method filters locally for now, but could be optimized with a dedicated backend endpoint
   * @param controllerId The ID of the controller entity
   * @param capteurId The ID of the capteur entity
   * @returns Observable of transactions for the specified controller and capteur
   */
  getByControllerAndCapteu(controllerId: string, capteurId: string): Observable<Transaction[]> {
           return this.http.get<Transaction[]>(`${this.apiUrl}transaction/controller/${controllerId}/capteur/${capteurId}`);

  }
}