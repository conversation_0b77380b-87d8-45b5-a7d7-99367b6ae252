import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Client } from '@app/core/models/client';
import { Organisation } from '@app/core/models/organisation';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { OrganisationApiService } from '@app/core/services/administrative/organisation.service';

interface StatisticCard {
  title: string;
  value: string | number;
  icon: string;
  iconColor: string;
  unit?: string;
  percentage?: number;
  trend?: 'up' | 'down' | 'neutral';
}

@Component({
  selector: 'app-organisation-statistics',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './organisation-statistics.component.html',
  styleUrls: ['./organisation-statistics.component.css']
})
export class OrganisationStatisticsComponent implements OnInit {
  organisationId!: string;
  organisation: Organisation | null = null;
  clients: Client[] = [];
  statistics: StatisticCard[] = [];
  isLoading = true;

  constructor(
    private clientService: ClientApiService,
    private organisationService: OrganisationApiService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.organisationId = params['id'];
      if (this.organisationId) {
        this.loadOrganisationData();
      }
    });
  }

  private loadOrganisationData(): void {
    this.isLoading = true;
    
    // Load organisation details
    this.organisationService.getById(this.organisationId).subscribe({
      next: (org) => {
        this.organisation = org;
        this.loadClientsAndCalculateStats();
      },
      error: (error) => {
        console.error('Error loading organisation:', error);
        this.isLoading = false;
      }
    });
  }

  private loadClientsAndCalculateStats(): void {
    // Load all clients and filter by organisation
    this.clientService.getAll().subscribe({
      next: (allClients) => {
        this.clients = allClients.filter(client => client.IdOrganisation === this.organisationId);
        this.calculateStatistics();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading clients:', error);
        this.isLoading = false;
      }
    });
  }

  private calculateStatistics(): void {
    const totalClients = this.clients.length;
    const totalInstallations = this.calculateTotalInstallations();
    const totalLicences = this.calculateTotalLicences();
    const activeLicences = this.calculateActiveLicences();
    const consumptionRules = this.generateMockConsumptionRules();
    const consumptionReduction = this.generateMockConsumptionReduction();
    const totalSites = this.calculateTotalSites();
    const activeEquipment = this.calculateActiveEquipment();

    this.statistics = [
      {
        title: 'Nombre total de clients',
        value: totalClients,
        icon: 'people',
        iconColor: 'var(--primary)',
        percentage: totalClients > 0 ? 12 : 0,
        trend: 'up'
      },
      {
        title: 'Nombre d\'installations',
        value: totalInstallations,
        icon: 'build',
        iconColor: '#2196F3',
        percentage: totalInstallations > 0 ? 8 : 0,
        trend: 'up'
      },
      {
        title: 'Licences actives',
        value: `${activeLicences}/${totalLicences}`,
        icon: 'verified',
        iconColor: '#FF9800',
        percentage: totalLicences > 0 ? Math.round((activeLicences / totalLicences) * 100) : 0,
        trend: 'up'
      },
      {
        title: 'Sites déployés',
        value: totalSites,
        icon: 'location_on',
        iconColor: '#9C27B0',
        percentage: totalSites > 0 ? 15 : 0,
        trend: 'up'
      },
      {
        title: 'Équipements actifs',
        value: activeEquipment,
        icon: 'devices',
        iconColor: '#00BCD4',
        percentage: activeEquipment > 0 ? 5 : 0,
        trend: 'up'
      },
      {
        title: 'Règles de diminution',
        value: consumptionRules,
        icon: 'rule',
        iconColor: '#795548',
        percentage: 22,
        trend: 'up'
      },
      {
        title: 'Taux de réduction énergétique',
        value: consumptionReduction,
        icon: 'trending_down',
        iconColor: 'var(--primary)',
        unit: '%',
        percentage: 18,
        trend: 'up'
      },
      {
        title: 'Économies réalisées',
        value: this.generateMockSavings(),
        icon: 'savings',
        iconColor: '#FF5722',
        unit: '€',
        percentage: 25,
        trend: 'up'
      }
    ];
  }

  private calculateTotalInstallations(): number {
    // Sum all sites across all clients
    return this.clients.reduce((total, client) => {
      return total + (client.Sites?.length || 0);
    }, 0);
  }

  private calculateTotalLicences(): number {
    // Sum all licences across all clients
    return this.clients.reduce((total, client) => {
      return total + (client.Licences?.length || 0);
    }, 0);
  }

  private calculateActiveLicences(): number {
    // Count active licences (mock logic - you can implement based on licence status)
    const totalLicences = this.calculateTotalLicences();
    return Math.floor(totalLicences * 0.85); // Assume 85% are active
  }

  private calculateTotalSites(): number {
    return this.clients.reduce((total, client) => {
      return total + (client.Sites?.length || 0);
    }, 0);
  }

  private calculateActiveEquipment(): number {
    return this.clients.reduce((total, client) => {
      return total + (client.ActiveEquipment || 0);
    }, 0);
  }

  private generateMockConsumptionRules(): number {
    // Mock data - in real implementation, this would come from a rules service
    return Math.floor(Math.random() * 50) + 10;
  }

  private generateMockConsumptionReduction(): number {
    // Mock data - percentage reduction in consumption
    return Math.floor(Math.random() * 30) + 15;
  }

  private generateMockSavings(): number {
    // Mock data - monetary savings in euros
    const baseAmount = this.clients.length * 1000;
    return Math.floor(baseAmount + (Math.random() * baseAmount * 0.5));
  }

  getTrendIcon(trend?: 'up' | 'down' | 'neutral'): string {
    switch (trend) {
      case 'up': return 'trending_up';
      case 'down': return 'trending_down';
      default: return 'trending_flat';
    }
  }

  getTrendClass(trend?: 'up' | 'down' | 'neutral'): string {
    switch (trend) {
      case 'up': return 'positive';
      case 'down': return 'negative';
      default: return 'neutral';
    }
  }

  getBusinessSectors(): string {
    const sectors = this.clients
      .map(client => client.BusinessSector)
      .filter(sector => sector && sector.trim())
      .filter((sector, index, array) => array.indexOf(sector) === index); // Remove duplicates
    
    if (sectors.length === 0) {
      return 'Non spécifié';
    }
    
    return sectors.length > 3 
      ? `${sectors.slice(0, 3).join(', ')} et ${sectors.length - 3} autres`
      : sectors.join(', ');
  }

  goBack(): void {
    this.router.navigate(['/accueil']);
  }
}