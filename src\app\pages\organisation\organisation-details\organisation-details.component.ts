import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';

import {
  FormsModule,
  ReactiveFormsModule,
  FormGroup,
  FormControl,
  Validators,
} from '@angular/forms';
import { Client } from '../../../core/models/client';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { SiteApiService } from '../../../core/services/administrative/site.service';
import { Site } from '../../../core/models/site';
import { CardSiteComponent } from '../../../components/card-site/card-site.component';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';
import { PageEvent, MatPaginatorModule } from '@angular/material/paginator';
import { Lister, Pagination, FilterParam } from '@app/core/models/util/page';
import { MatIconModule, MatIcon } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { ControllerServeur } from '@app/core/models/controllerServeur';
import { ControllerServerComponent } from './controller-server/list/controller-server.component';
import { DashboardClientComponent } from '@app/components/dashboards/dashboard-client/dashboard-client.component';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { ListComponent } from './abonnement/list/list.component';
import { ControllerComponent } from './controller/list/controller.component';
import { NgxSpinnerModule, NgxSpinnerService } from 'ngx-spinner';
import { HotToastService } from '@ngxpert/hot-toast';
import { SensorsBackEndDataService } from '@app/core/sensors-back-end-data.service';
import {
  NgToastComponent,
  NgToastService,
  TOAST_POSITIONS,
} from 'ng-angular-popup';
import { NavigationService } from '@app/core/services/Navigation.Service';
import { HierarchyComponent } from '@app/pages/devices/hierarchy/hierarchy.component';
import { ClientWithSiteStatusView } from '@app/shared/models/clientWithSiteStatusView';

@Component({
  selector: 'app-organisation-details',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    CardSiteComponent,
    GenericTableComponent,
    MatPaginatorModule,
    MatIconModule,
    MatButtonModule,
    MatDialogModule,
    MatSnackBarModule,
    ControllerServerComponent,
    MatIconModule,
    MatIcon,
    DashboardClientComponent,
    MatDialogModule,
    ListComponent,
    ControllerComponent,
    NgxSpinnerModule,
    NgToastComponent,
    HierarchyComponent
],
  templateUrl: './organisation-details.component.html',
  styleUrls: ['./organisation-details.component.css'],
})

export class OrganisationDetailsComponent implements OnInit {
  @ViewChild('dashdiv') dashDiv!: ElementRef;
  client: Client = new Client();
  clientSites: Site[] = [];
  isLoadingSites: boolean = false;
  viewMode: 'cards' | 'table' = 'cards';
  LogoCLient: string = 'assets/images/default-organisation.jpg';
  showSites: boolean = true;
  activeTab:
      'hierarchy'
    | 'sites'
    | 'controllerServeurs'
    | 'controllers'
    | 'licences'
    | 'abonnement' = 'hierarchy';
  controllerServeurs: ControllerServeur[] = [];

  siteHeaders: string[] = ['Nom', 'Adresse', 'Téléphone', 'Contact', 'Statut'];
  siteKeys: string[] = ['Name', 'Address', 'PhoneNumber', 'Contact', 'Status'];
  urlclientId: string = '';
  pageSize: number = 5;
  currentPage: number = 0;
  totalSites: number = 0;
  showMoreFields: boolean = false;
  dashButton: boolean = true;
  searchParam: string = '';
  hasSearchFilter: boolean = false;
  imagePreview: string | ArrayBuffer | null = null;
  existingImageUrl: string | null = null;
  imageRemoved: boolean = false;
  // Popup form properties
  showSiteForm: boolean = false;
  isEditMode: boolean = false;
  selectedSite: Site | null = null;
  uploadedImages: File[] = [];
  showValidationErrors = false;
  validationErrorMessage = '';
  activeEquipment: number = 0;
  inactiveEquipment: number = 0;
  TOAST_POSITIONS = TOAST_POSITIONS;
  isTabTransitioning: boolean = false;
  previousTab: string = 'sites';
  newlyCreatedSiteId: string | null = null;
  updatedImage: string | null = null;
  clientWithSiteStatus: ClientWithSiteStatusView = {};
  request: Lister = {};

  siteForm = new FormGroup({
    id: new FormControl(''),
    name: new FormControl('', [Validators.required]),
    adress: new FormControl('', [Validators.required]),
    addressComplement: new FormControl(''),
    phoneNumber: new FormControl('', [Validators.required]),
    description: new FormControl(''),
    image: new FormControl(null),
    contact: new FormControl('', [Validators.required]),
    manager: new FormControl('', [Validators.required]),
    email: new FormControl('', [
      Validators.required,
      Validators.email,
      Validators.pattern('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$'),
    ]),
    status: new FormControl('actif'),
    grade: new FormControl('A'),
    latitude: new FormControl(0),
    longtitude: new FormControl(0),
    surface: new FormControl(0),
    clientId: new FormControl(''),



  });

  constructor(
    readonly route: ActivatedRoute,
    readonly router: Router,
    readonly clientService: ClientApiService,
    readonly siteService: SiteApiService,
    readonly location: Location,
    readonly snackBar: MatSnackBar,
    readonly dialog: MatDialog,
    private readonly spinner: NgxSpinnerService,
    private readonly toast: NgToastService,
    private readonly navigationService: NavigationService,
    private equipmentService: SensorsBackEndDataService
  ) {}

  ngOnInit(): void {
    this.loadClientDetails();
  }
  toggleDetailsDisplay(): void {
    this.showMoreFields = !this.showMoreFields;
  }
  setDashButton(): void {
    this.dashButton = !this.dashButton;
    this.dashDiv.nativeElement.scrollIntoView({ behavior: 'smooth' });
  }
  onSearchKeyup(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      this.searchSites();
    } else if (event.key === 'Backspace' && this.searchParam === '') {
      this.clearSearch();
    }
  }

  toggleSection(): void {
    this.showSites = !this.showSites;
  }

  switchTab(
    newTab: 'sites' | 'controllerServeurs' | 'controllers' | 'abonnement' | 'hierarchy'
  ): void {
    if (this.isTabTransitioning || this.activeTab === newTab) {
      return;
    }

    this.isTabTransitioning = true;
    this.previousTab = this.activeTab;

    // Start fade out
    setTimeout(() => {
      this.activeTab = newTab;

      // Complete transition
      setTimeout(() => {
        this.isTabTransitioning = false;
      }, 300);
    }, 150);
  }

  get imageUrl(): string {
    if (this.client?.ClientLogo) {
      //console.log('Client logo found:', this.client.ClientLogo.toString());
      return `data:image/jpeg;base64,${this.client.ClientLogo}`;
    } else {
      return this.LogoCLient;
    }
  }
  private sanitizeValue(value: any): any {
    return value === null || value === undefined || value === '' ? '—' : value;
  }
  loadClientDetails(): void {
    this.equipmentService
      .getCapteursByClientId(this.route.snapshot.paramMap.get('id') ?? '')
      .subscribe({
        next: (data: any) => {
          console.log(data);
          data.forEach((d: { LastSeen: string | number | Date }) => {
            const apiDate = new Date(d.LastSeen);
            const now = new Date();
            const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000); // 1 hour ago

            if (apiDate >= oneHourAgo && apiDate <= now) {
              this.activeEquipment++;
            } else {
              this.inactiveEquipment++;
            }
          });
        },
        error: (error: any) => {
          console.log(
            'error dans la recuperation des donnees de capteurs lié au client'
          );
        },
      });
    this.spinner.show();
    this.urlclientId = this.route.snapshot.paramMap.get('id') ?? '';
    if (this.urlclientId) {
      this.clientService.getById(this.urlclientId).subscribe({
        next: (data: any) => {
          console.log('client data: ', data);
          this.client = data;
          this.loadClientSitesView(this.client.Id);
          this.clientService.downloadClientImage(this.client.Id).subscribe({
            next: (response: any) => {
              this.client.ClientLogo = response.image;
            },
            error: (error) => {
              console.error('Error loading client image:', error);
            },
          });
          this.spinner.hide();

          if (this.client?.Id) {
            this.loadClientSites(this.urlclientId);
          }

          this.extractControllerServeurs();
        },
        error: (error: any) => {
          console.error('Error loading client details:', error);
          this.spinner.hide();
        },
      });
    }
  }

  formatDate(dateInput: string | Date | undefined): string {
    if (!dateInput) return '—'; // or return empty string

    const date = new Date(dateInput);
    const iso = date.toISOString().split('T')[0];
    const [year, month, day] = iso.split('-');
    return `${year}-${month}-${day}`;
  }

  loadClientSitesView(clientId: string): void {
    var request: Lister = {};
    const pagination: Pagination = {
      CurrentPage: this.currentPage + 1,
      PageSize: this.pageSize,
      totalElement: 0,
    };

    request.Pagination = pagination;

    const FilterParams: FilterParam[] = [];

    FilterParams.push({
      Column: 'ClientId',
      Value: clientId,
      Op: 'eq',
      AndOr: 'AND',
    });

    request.FilterParams = FilterParams;

    this.clientService.getPageSiteStatus(request).subscribe({
      next: (response: any) => {
        this.clientWithSiteStatus = response.Content[0] ?? [];
        console.log(
          'Client with site status response:',
          this.clientWithSiteStatus
        );
      },
    });
  }

  loadClientSites(clientId: string): void {
    this.isLoadingSites = true;

    const pagination: Pagination = {
      CurrentPage: this.currentPage,
      PageSize: this.pageSize,
      totalElement: 0,
    };

    const FilterParams: FilterParam[] = [
      {
        Column: 'ClientId',
        Value: clientId,
        Op: 'eq',
        AndOr: 'AND',
      },
    ];

    // Add search filter if searchParam is not empty
    if (this.searchParam && this.searchParam.trim() !== '') {
      this.hasSearchFilter = true;
      FilterParams.push({
        Column: 'Name',
        Value: this.searchParam.trim(),
        Op: 'contains',
        AndOr: 'AND',
      });
    } else {
      this.hasSearchFilter = false;
    }

    const lister: Lister = {
      Pagination: pagination,
      FilterParams: FilterParams,
    };

    this.siteService.gatePage(lister).subscribe({
      next: (response) => {
        if (response && response.Content) {
          this.clientSites = response.Content;
          this.totalSites =
            response.Lister?.Pagination?.totalElement ??
            this.clientSites.length;
        } else {
          this.fallbackToGetAll(clientId);
        }
        this.isLoadingSites = false;
      },
      error: (error) => {
        console.error('Error loading sites with pagination:', error);
        this.toast.warning(
          'Failed to load sites. Please try again later.',
          'Error',
          3000,
          false
        );

        this.fallbackToGetAll(clientId);
      },
    });
  }

  private extractControllerServeurs(): void {
    if (!this.client?.Licences) {
      this.controllerServeurs = [];
      return;
    }
    console.log(
      'Extracting controllerServeurs from client Licences:',
      this.client.Licences
    );

    this.controllerServeurs = this.client.Licences.flatMap(
      (licence: any) => licence.ControllerServeurs ?? []
    );

    console.log('Extracted controllerServeurs:', this.controllerServeurs);
  }

  private fallbackToGetAll(clientId: string): void {
    this.siteService.getAll().subscribe({
      next: (allSites) => {
        this.clientSites = allSites.filter(
          (site) => site.ClientId === clientId
        );
        this.totalSites = this.clientSites.length;
        this.isLoadingSites = false;
      },
      error: (fallbackError) => {
        console.error('Error loading all sites:', fallbackError);
        this.toast.warning(
          'Failed to load sites. Please try again later.',
          'Error',
          3000,
          false
        );

        this.isLoadingSites = false;
      },
    });
  }
  get totalEquipment(): number {
    return this.activeEquipment + this.inactiveEquipment;
  }
  viewSiteDetails(site: any): void {
    const siteId = site.id ?? site.Id;
    this.router.navigate(['/site-locals/', siteId]);
  }

  editSite(site: any): void {
    this.isEditMode = true;
    this.showMoreFields = false;
    this.selectedSite = site;
    this.imagePreview = null;
    this.existingImageUrl = null;
    this.imageRemoved = false;

    // Set existing image URL if site has valid image data
    if (site.Image && site.Image !== 'null' && site.Image !== '') {
      this.existingImageUrl = `data:image/jpeg;base64,${site.Image}`;
    } else if (site.Id) {
      // If no image data but site has ID, try to load it
      this.loadSiteImageForEdit(site.Id);
    }

    this.siteForm.patchValue({
      id: site.id ?? site.Id ?? '',
      name: site.name ?? site.Name ?? '',
      adress: site.address ?? site.Address ?? '',
      addressComplement: site.addressComplement ?? site.AddressComplement ?? '',
      phoneNumber: site.phoneNumber ?? site.PhoneNumber ?? '',
      description: site.description ?? site.Description ?? '',
      contact: site.contact ?? site.Contact ?? '',
      manager: site.manager ?? site.Manager ?? '',
      email: site.email ?? site.Email ?? '',
      status: site.status ?? site.Status ?? '',
      grade: site.grade ?? site.Grade ?? '',
      latitude: site.latitude ?? site.Latitude ?? 0,
      longtitude: site.longtitude ?? site.Longtitude ?? 0,
      surface: site.surface ?? site.Surface ?? 0,
      clientId: site.clientId ?? site.ClientId ?? this.client?.Id ?? '',
    });
    this.showSiteForm = true;
  }

  // Load site image for editing
  private loadSiteImageForEdit(siteId: string): void {
    this.siteService.downloadImage(siteId).subscribe({
      next: (response) => {
        if (response?.image && this.selectedSite) {
          this.selectedSite.Image = response.image;
          this.existingImageUrl = `data:image/jpeg;base64,${response.image}`;
        }
      },
      error: (error) => {
        console.error('Error loading site image for edit:', error);
      },
    });
  }

  deleteSite(siteId: string): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirmation de suppression',
        message: 'Êtes-vous sûr de vouloir supprimer ce site ?',
        icon: 'warning',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.siteService.delete(siteId).subscribe({
          next: () => {
            this.clientService
              .changeClientStatusInactif(this.urlclientId)
              .subscribe({
                next: (response: any) => {
                  console.log('Ok');
                },
                error: (err: any) => {
                  console.log(
                    'Error in changing client status to inactive',
                    err
                  );
                },
                complete: () => {
                  console.log('Request completed');
                },
              });
            this.toast.success(
              'Site supprimé avec succès',
              'Success',
              3000,
              false
            );
            this.clientSites = this.clientSites.filter(
              (site) => site.Id !== siteId
            );
            this.totalSites--;
          },
          error: (err) => {
            console.error('Error deleting site:', err);
            this.toast.warning(
              'Erreur lors de la suppression du site',
              'Error',
              3000,
              false
            );
          },
        });
      }
    });
  }

  addNewSite(): void {
    this.isEditMode = false;
    this.showMoreFields = false;
    this.selectedSite = null;
    this.siteForm.reset();
    this.siteForm.patchValue({
      clientId: this.client?.Id ?? '',
    });
    this.uploadedImages = [];
    this.showSiteForm = true;
  }

  goBack(): void {
    window.history.back();
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;

    console.log(
      'Updated pagination - Page:',
      this.currentPage,
      'Size:',
      this.pageSize
    );
    // Reload sites with new pagination
    if (this.client?.Id) {
      this.loadClientSites(this.client.Id);
    }
  }

  handleTableAction(event: { action: string; row: any }): void {
    const { action, row } = event;

    switch (action) {
      case 'view':
        this.viewSiteDetails(row);
        break;
      case 'edit':
        this.editSite(row);
        break;
      case 'delete':
        const deleteId = row.Id;
        this.deleteSite(deleteId);
        break;
      default:
        console.warn('Unknown action:', action);
    }
  }

  handleControllerServerDeleted(deletedId: string): void {
    this.loadClientDetails();
  }

  get paginatedSites(): Site[] {
    const startIndex = this.currentPage * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    return this.clientSites.slice(startIndex, endIndex);
  }

  // Popup form methods

  onImagesSelected(event: any): void {
    if (event.target.files && event.target.files.length > 0) {
      const file = event.target.files[0];
      this.uploadedImages = [file];

      // Create preview
      const reader = new FileReader();
      reader.onload = () => {
        this.imagePreview = reader.result;
        if (this.isEditMode) {
          this.existingImageUrl = null; // Clear existing image when new one is selected in edit mode
        }
      };
      reader.readAsDataURL(file);
    } else {
      this.uploadedImages = [];
      this.imagePreview = null;
    }
  }

  generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
      /[xy]/g,
      function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      }
    );
  }

  submitSiteForm(): void {
    if (this.siteForm.valid) {
      this.showValidationErrors = false;
    }
    this.markFormGroupTouched(this.siteForm);

    if (this.siteForm.valid) {
      this.showValidationErrors = false;
      this.validationErrorMessage = '';
      if (this.isEditMode) {
        // Show confirmation dialog only for edit mode
        const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
          width: '400px',
          data: {
            title: 'Confirmation',
            message: 'Êtes-vous sûr de vouloir modifier ce site ?',
            icon: 'edit',
          },
        });

        dialogRef.afterClosed().subscribe((result) => {
          if (result) {
            this.processSiteForm();
          }
        });
      } else {
        // For create mode, directly process the form without confirmation
        this.processSiteForm();
      }
    } else {
      // Show validation errors at the top of the form
      this.showValidationErrors = true;
      this.validationErrorMessage =
        'Veuillez corriger les erreurs dans le formulaire avant de continuer.';

      // Scroll to top of form to show error message
      const formElement = document.querySelector('.popup-form');
      if (formElement) {
        formElement.scrollTop = 0;
      }
    }
  }
  closeSiteForm(): void {
    this.showSiteForm = false;
    this.isEditMode = false;
    this.selectedSite = null;
    this.siteForm.reset();
    this.uploadedImages = [];
    this.imagePreview = null;
    this.existingImageUrl = null;
    this.imageRemoved = false;
    this.showValidationErrors = false; // Reset validation errors
    this.validationErrorMessage = ''; // Reset validation error message
  }
  private processSiteForm(): void {
    this.markFormGroupTouched(this.siteForm);

    if (this.siteForm.valid) {
      const formValues = this.siteForm.value;

      if (this.isEditMode && this.selectedSite) {
        // EDIT MODE - handle everything in one form
        const updatedSite: any = {
          Id: formValues.id ?? this.selectedSite.Id,
          Name: formValues.name ?? '',
          Address: formValues.adress ?? '',
          AddressComplement: formValues.addressComplement ?? '',
          PhoneNumber: formValues.phoneNumber ?? '',
          Description: formValues.description ?? '',
          Contact: formValues.contact ?? '',
          Manager: formValues.manager ?? '',
          Email: formValues.email ?? '',
          Status: formValues.status ?? '',
          Grade: formValues.grade ?? '',
          Latitude: formValues.latitude ?? 0,
          Longtitude: formValues.longtitude ?? 0,
          Surface: formValues.surface ?? 0,
          ClientId: formValues.clientId ?? this.client?.Id ?? '',
          Image: this.imagePreview
            ? (this.imagePreview as string).split(',')[1]
            : this.imageRemoved
            ? null
            : this.selectedSite?.Image && this.selectedSite.Image !== 'null'
            ? this.selectedSite.Image
            : null,
          createdAt: this.selectedSite.CreatedAt,
          CreatedBy: this.selectedSite.CreatedBy,
          LastUpdatedAt: new Date().toISOString(),
          LastUpdatedBy: null,
          DeletedAt: this.selectedSite.DeletedAt,
          DeletedBy: this.selectedSite.DeletedBy,
          EmployeesCount: this.selectedSite.EmployeesCount ?? 0,
          LocalsCount: this.selectedSite.LocalsCount ?? 0,
        };

        if (this.uploadedImages && this.uploadedImages.length > 0) {
          const file = this.uploadedImages[0];
          const reader = new FileReader();
          reader.onload = () => {
            const result = reader.result as string;
            updatedSite.Image = result.includes('base64,')
              ? result.split('base64,')[1]
              : result;
            this.updateSite(updatedSite);
          };
          reader.readAsDataURL(file);
        } else {
          this.updateSite(updatedSite);
        }
      } else {
        // CREATE MODE - exclude image from initial submission
        const newSite: any = {
          Id: this.generateUUID(),
          CreatedAt: new Date().toISOString(),
          CreatedBy: null,
          LastUpdatedAt: null,
          LastUpdatedBy: null,
          DeletedAt: null,
          DeletedBy: null,
          Name: formValues.name ?? '',
          Address: formValues.adress ?? '',
          AddressComplement: formValues.addressComplement ?? '',
          PhoneNumber: formValues.phoneNumber ?? '',
          Image: null, // Image will be added separately
          Description: formValues.description ?? '',
          Contact: formValues.contact ?? '',
          Manager: formValues.manager ?? '',
          Email: formValues.email ?? '',
          Status: formValues.status ?? '',
          Grade: formValues.grade ?? '',
          Latitude: formValues.latitude ?? 0,
          Longtitude: formValues.longtitude ?? 0,
          Surface: formValues.surface ?? 0,
          ClientId: formValues.clientId ?? this.client?.Id ?? '',
          EmployeesCount: 0,
          LocalsCount: 0,
        };

        this.createSite(newSite);
      }
    }
  }

  private createSite(site: Site): void {
    this.spinner.show();
    this.siteService.create(site).subscribe({
      next: (createdSite) => {
        this.toast.success('Site créé avec succès', 'Succès', 3000, false);
        this.loadClientSites(this.client?.Id ?? '');
        // If there's an image to upload, upload it using siteService.uploadImage
        if (this.uploadedImages.length > 0) {
          const file = this.uploadedImages[0];
          const reader = new FileReader();
          reader.onload = () => {
            const imageData = (reader.result as string).split(',')[1];
            console.log('Created site ID:', imageData, createdSite.Id);
            this.siteService.uploadImage(createdSite.Id, imageData).subscribe({
              next: () => {
                this.spinner.hide();
                this.closeSiteForm();
              },
              error: (error) => {
                this.spinner.hide();
                console.error('Error uploading image:', error);
                this.toast.warning(
                  "Site créé mais erreur lors de l'ajout de l'image",
                  'Avertissement',
                  3000,
                  false
                );
                this.router.navigate(['/site-locals/', createdSite.Id]);
              },
            });
            this.clientService
              .changeClientStatus(createdSite.ClientId)
              .subscribe({
                next: (response: any) => {
                  this.router.navigate(['/site-locals/', createdSite.Id]);
                },
              });
          };
          reader.readAsDataURL(file);
        } else {
          this.clientService
            .changeClientStatus(createdSite.ClientId)
            .subscribe({
              next: (response: any) => {
                console.log('Client status updated:', response);
              },
            });
          this.spinner.hide();
          this.router.navigate(['/site-locals/', createdSite.Id]);
        }
      },
      error: (error) => {
        this.spinner.hide();
        this.toast.warning(
          'Erreur lors de la création du site',
          'Erreur',
          3000,
          false
        );
        console.error('Error creating site:', error);
      },
    });
  }

  // New method for image upload
  submitImageForSite(): void {
    if (!this.newlyCreatedSiteId || this.uploadedImages.length === 0) {
      this.router.navigate(['/site-locals/', this.newlyCreatedSiteId]);
      return;
    }

    this.spinner.show();
    const file = this.uploadedImages[0];
    const reader = new FileReader();
    reader.onload = () => {
      const imageData = (reader.result as string).split(',')[1];
      this.siteService
        .uploadImage(this.newlyCreatedSiteId ?? '', imageData)
        .subscribe({
          next: () => {
            this.spinner.hide();
            this.toast.success(
              'Image uploaded successfully',
              'Success',
              3000,
              false
            );
            this.closeImageUploadForm();
            this.router.navigate(['/site-locals/', this.newlyCreatedSiteId]);
          },
          error: (error) => {
            this.spinner.hide();
            this.toast.warning('Error uploading image', 'Error', 3000, false);
            console.error('Error uploading image:', error);
            this.router.navigate(['/site-locals/', this.newlyCreatedSiteId]);
          },
        });
    };
    reader.readAsDataURL(file);
  }
  closeImageUploadForm(): void {
    this.newlyCreatedSiteId = null;
    this.uploadedImages = [];
    this.imagePreview = null;
    if (this.newlyCreatedSiteId) {
      this.router.navigate(['/site-locals/', this.newlyCreatedSiteId]);
    }
  }

  private updateSite(site: Site): void {
    this.updatedImage = site.Image ?? null;
    site.Image = 'null';
    this.siteService.update(site).subscribe({
      next: () => {
        this.siteService
          .uploadImage(site.Id, this.updatedImage ?? 'null')
          .subscribe({
            next: () => {
              this.loadClientSites(this.client?.Id ?? '');
              this.closeSiteForm();
            },
            error: (error) => {
              this.loadClientSites(this.client?.Id ?? '');
              this.closeSiteForm();
              console.error('Error uploading image:', error);
            },
          });
        this.toast.success('Site updated successfully', 'Success', 3000, false);
        this.closeSiteForm();
      },
      error: (error) => {
        this.toast.warning(
          'Erreur lors de la mise à jour du site',
          'Error',
          3000,
          false
        );
        console.error('Error updating site:', error);
      },
    });
  }

  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  toggleShowMore(): void {
    this.showMoreFields = !this.showMoreFields;
  }

  searchSites(): void {
    this.currentPage = 0; // Reset to first page when searching
    if (this.client?.Id) {
      this.loadClientSites(this.client.Id);
    }
  }

  clearSearch(): void {
    this.searchParam = '';
    this.hasSearchFilter = false;
    this.currentPage = 0; // Reset to first page when clearing search
    if (this.client?.Id) {
      this.loadClientSites(this.client.Id);
    }
  }
  removeImage(): void {
    this.imagePreview = null;
    this.existingImageUrl = null;
    this.uploadedImages = [];
    this.imageRemoved = true;

    // If in edit mode, mark that we want to remove the existing image
    if (this.isEditMode && this.selectedSite) {
      this.selectedSite.Image = 'null';
    }

    // Clear the file input
    const fileInput = document.getElementById('image') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }
}
