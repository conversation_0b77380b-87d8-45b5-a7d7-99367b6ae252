import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiService } from '../api.service';
import { Subscription } from '@app/core/models/subscription';
import { Observable } from 'rxjs';
import { environment } from '@app/environments/environment';

@Injectable({ providedIn: 'root' })
export class SubscriptionApiService extends ApiService<Subscription> {
  private apiUrl = `${environment.host}/api/subscription`;
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint('subscription');
  }

  renewSubscription(
    id: string,
    type: 'monthly' | 'yearly'
  ): Observable<{ subscription: Subscription; facture: any }> {
    return this.http.post<{ subscription: Subscription; facture: any }>(
      `${this.apiUrl}/renew/${id}`,
      type
    );
  }
}
