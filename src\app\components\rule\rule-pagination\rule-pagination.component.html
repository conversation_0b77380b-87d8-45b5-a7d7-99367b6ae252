<div class="pagination-controls" *ngIf="pageCount > 0 && !isLoading">
    <button mat-icon-button (click)="previousPage.emit()" [disabled]="currentPage === 1 || hasPendingOperations"
        matTooltip="Page précédente">
        <mat-icon>chevron_left</mat-icon>
    </button>

    <ng-container *ngFor="let page of getPages()">
        <span class="page-number" [class.active]="currentPage === page" [class.disabled]="hasPendingOperations"
            (click)="!hasPendingOperations && goToPage.emit(page)">
            {{ page }}
        </span>
    </ng-container>

    <button mat-icon-button (click)="nextPage.emit()" [disabled]="currentPage === pageCount || hasPendingOperations"
        matTooltip="Page suivante">
        <mat-icon>chevron_right</mat-icon>
    </button>

    <span class="page-info">
        <ng-container *ngIf="pageCount > 1">Page {{ currentPage }} sur {{ pageCount }} - </ng-container>
        Total: {{ totalElements }} règle{{ totalElements !== 1 ? 's' : '' }}
        <ng-container *ngIf="searchTerm">
            (filtré{{ totalElements !== 1 ? 's' : '' }} par "{{ searchTerm }}")
        </ng-container>
        <ng-container *ngIf="appliedFiltersCount > 0 && !searchTerm">
            ({{ appliedFiltersCount }} filtre{{ appliedFiltersCount !== 1 ? 's' : '' }} appliqué{{
            appliedFiltersCount !== 1 ? 's' : '' }})
        </ng-container>
        <ng-container *ngIf="hasPendingOperations">
            <span class="operation-indicator">
                <mat-icon class="loading-icon" style="font-size: 14px; margin-left: 8px;">hourglass_empty</mat-icon>
                Opération en cours...
            </span>
        </ng-container>
    </span>
</div>