<div class="organisation-management-container">
  <div class="header-section">
    <div class="page-title">
      <h1 class="title">
        <i class="material-icons title-icon">people</i> Liste des Contrôleurs
        <!-- ({{
        filteredClients.length
        }}) -->
      </h1>
    </div>
    <div class="actions">
      <button
        class="create-button"
        (click)="addNewController()"
        *ngIf="!showCreateForm && !showDetailsModal"
      >
        <i class="material-icons">add</i> Ajouter un contrôleur
      </button>
    </div>
  </div>

  <div class="modal-overlay" *ngIf="showCreateForm">
    <div class="modal-container">
      <div class="modal-header">
        <h3>
          <mat-icon>{{ isEditMode ? "edit" : "add" }}</mat-icon>
          {{ isEditMode ? "Modifier le Contrôleur" : "Ajouter un Contrôleur" }}
        </h3>
        <button class="close-button" (click)="onFormClosed()">
          <i class="material-icons">close</i>
        </button>
      </div>

      <div class="modal-content">
        <app-c-p-form
          [controller]="selectedControllerEdit"
          [isEditMode]="isEditMode"
          (formClosed)="onFormClosed()"
          (controllerCreated)="onControllerCreated($event)"
          (controllerUpdated)="onControllerUpdated($event)"
        >
        </app-c-p-form>
      </div>
    </div>
  </div>

  <div class="search-bar-container">
    <div class="search-bar">
      <input
        type="text"
        [(ngModel)]="searchParam"
        placeholder="Rechercher"
        class="search-input"
        (keyup.enter)="searchControllers()"
      />
      <button class="clear-btn" (click)="clearSearch()" *ngIf="searchParam">
        <mat-icon style="font-size: 22px;padding-right: 20px;">close</mat-icon>
      </button>
    </div>
    <span class="search-button" (click)="searchControllers()">
      <mat-icon>search</mat-icon>
    </span>
  </div>

  <!-- Details Modal -->
  <div class="modal-overlay" *ngIf="showDetailsModal">
    <div class="modal-container details-modal">
      <div class="modal-header">
        <h3>Détails du Contrôleur</h3>
        <button class="close-button" (click)="onDetailsClosed()">
          <i class="material-icons">close</i>
        </button>
      </div>

      <div class="modal-content">
        <app-c-p-details
          [controller]="selectedControllerDetails"
          (detailsClosed)="onDetailsClosed()"
        >
        </app-c-p-details>
      </div>
    </div>
  </div>

  <!-- Clients table -->
  <div class="table-section" *ngIf="!isLoading" style="width: 100%">
    <!-- <div class="table-header">
    <h3>Liste des Clients ({{ filteredClients.length }})</h3>
  </div> -->

    <app-generic-table
      [data]="controllers"
      [headers]="headers"
      [keys]="keys"
      [actions]="['edit', 'view', 'delete']"
      (actionTriggered)="handleTableAction($event)"
    ></app-generic-table>
    <div class="pagination-container">
      <mat-paginator
        [length]="totalCount"
        [pageSize]="pageSize"
        [pageIndex]="currentPage"
        [pageSizeOptions]="[5, 10, 25, 50]"
        (page)="onPageChange($event)"
        aria-label="Select page"
      ></mat-paginator>
    </div>
  </div>
  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="loading-spinner"></div>
    <p>Chargement des Contrôleurs...</p>
  </div>
  <!-- <ngx-ui-loader></ngx-ui-loader> -->
</div>
