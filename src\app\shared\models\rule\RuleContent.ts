import { Action } from "./Action";
import {  ClientForRule } from "./ClientForRule";
import { Condition } from "./Condition";

// Client-side comprehensive rule content for display

export interface RuleContent {
  id: string;
  name: string;
  priority: number;
  status: 'active' | 'inactive';
  tags: string[];
  conditions: Condition[];
  actions: Action[];
  tagStatus: { [key: string]: string; };
  totalApplications: number;
  lastTriggered: string;
  clients: ClientForRule[]; // Aggregated client hierarchy
}
