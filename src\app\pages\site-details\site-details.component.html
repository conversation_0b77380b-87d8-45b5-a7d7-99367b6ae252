<!-- local-details.component.html -->
<div class="site-container">
  <div class="site-details" *ngIf="selectedLocal">
    <div class="local-details-section">
      <app-local-details [local]="selectedLocal" [site]="site"></app-local-details>
    </div>
  </div>

  <!-- Floor Plan Section -->
  <!-- <div class="architecture-section" *ngIf="selectedLocal">
    <h2 class="section-title">Plan d'architecture</h2> -->


    <!-- <div class="floor-plan-container">
      <div class="architecture-wrapper" #architectureWrapper>
        <<img [src]="selectedLocal.Architecture2DImage || 'assets/plans/salle vide.avif'" alt="Plan d'architecture"
          class="architecture-image" (load)="onImageLoad($event)" />


        <div class="device-icons-overlay">
          <div
            *ngFor="let device of staticDevices"
            class="device-position-marker"
            [style.left.%]="device.position.x"
            [style.top.%]="device.position.y"
            [class.dragging]="isDragging && selectedDeviceId === device.id"
            (mousedown)="startDragging($event, device)"
            (touchstart)="startDragging($event, device)"
          >
            <app-device-icon
              [deviceType]="device.type"
              [status]="device.status"
            >
            </app-device-icon>
            <div class="device-tooltip">
              {{ device.name }}
            </div>
          </div>
        </div>
      </div>
     </div> -->
  </div>


  <!-- Devices Section
  <div class="devices-section">
    <h2 class="section-title">Équipements</h2>
    <div class="status-panels">
      <app-device-status-panel
        title="Éclairages"
        [devices]="getLightDevices()"
        [pairingMode]="pairingMode"
        (deviceStatusChange)="
          updateDeviceStatus($event.deviceId, $event.status)
        "
        (togglePermitJoin)="togglePermitJoin($event)"
      >
      </app-device-status-panel>

      <app-device-status-panel
        title="Climatisation"
        [devices]="getClimateDevices()"
        [pairingMode]="pairingMode"
        (deviceStatusChange)="
          updateDeviceStatus($event.deviceId, $event.status)
        "
        (togglePermitJoin)="togglePermitJoin($event)"
      >
      </app-device-status-panel>

      <app-device-status-panel
        title="Contrôleurs"
        [devices]="getOtherDevices()"
        [pairingMode]="pairingMode"
        (deviceStatusChange)="
          updateDeviceStatus($event.deviceId, $event.status)
        "
        (togglePermitJoin)="togglePermitJoin($event)"
      >
      </app-device-status-panel>
    </div>
  </div> -->

  <app-logs *ngIf="localIdStr" [localId]="localIdStr"></app-logs>
