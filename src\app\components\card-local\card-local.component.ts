import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { Local } from '@app/core/models/local';
import { LocalApiService } from '@app/core/services/administrative/local.service';
import { CapteurApiService } from '@app/core/services/administrative/capteur.service';
import { TransactionApiService } from '@app/core/services/administrative/transaction.service';
import { Transaction } from '@app/core/models/transaction';

@Component({
  selector: 'app-card-local',
  templateUrl: './card-local.component.html',
  styleUrls: ['./card-local.component.css'],
  standalone: true,
  imports: [CommonModule, MatIconModule],
})
export class CardLocalComponent {
  @Input() local!: Local;
  
  @Output() viewDetails = new EventEmitter<string>();
  @Output() editLocal = new EventEmitter<Local>();
  @Output() deleteLocal = new EventEmitter<string>();

  capteurCount = 0;
  imageLocaleUrl: string = '';

  constructor(
    readonly router: Router,
    readonly localService: LocalApiService,
    private transactionService: TransactionApiService,
    private capteurService: CapteurApiService
  ) {}

  ngOnInit() {
    // Get the image URL from the service
    // this.imageLocaleUrl = this.localService.getImageUrl(this.local.ImageLocal);
    
    
    if (this.local.TypeLocal?.Nom) {
      const typeElement = document.querySelector('.local-type');
      if (typeElement) {
        typeElement.setAttribute('data-type', this.local.TypeLocal.Nom);
      }
    }

    if (this.local?.Id) {
      // Téléchargement de l'image associée au local
      this.localService.downloadImage(this.local.Id).subscribe({
        next: (response) => {
          if (response?.image) {
            this.imageLocaleUrl = 'data:image/jpeg;base64,' + response.image;
          }
        },
        error: (err) => {
          console.error('Erreur lors du téléchargement de l’image locale :', err);
        }
      });
  
      // Comptage des capteurs liés au local
      this.transactionService.getAll().subscribe({
        next: (transactions: Transaction[]) => {
          const localTransactions = transactions.filter(t => t.IdLocal === this.local.Id);
          const capteurIds = [...new Set(localTransactions.map(t => t.IdCapteur))];
          this.capteurCount = capteurIds.length;
        },
        error: err => console.error('Erreur chargement transactions:', err)
      });
    }
  }

  onView(): void {
    this.router.navigate(['/local-details', this.local.Id]);
  }

  onEdit(): void {
    this.editLocal.emit(this.local);
  }

  onDelete(): void {
    this.deleteLocal.emit(this.local.Id);
  }
}