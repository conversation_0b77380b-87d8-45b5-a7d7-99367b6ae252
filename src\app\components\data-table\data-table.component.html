<div class="table-container">
  <div class="filters-container">
    <div class="filter" *ngFor="let column of config.columns">
      <ng-container [ngSwitch]="column.filterType">
        <mat-form-field *ngSwitchCase="'text'" appearance="outline">
          <mat-label>{{column.filterPlaceholder || 'Filter ' + column.label}}</mat-label>
          <input matInput
                 #filterInput
                 (keyup)="onFilter(column, filterInput.value)">
          <button mat-icon-button matSuffix *ngIf="filters[column.key]"
                  (click)="clearFilter(column.key); filterInput.value=''">
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>

        <mat-form-field *ngSwitchCase="'select'" appearance="outline">
          <mat-label>{{column.filterPlaceholder || 'Filter ' + column.label}}</mat-label>
          <mat-select (selectionChange)="onFilter(column, $event.value)">
            <mat-option [value]="">Tous</mat-option>
            <mat-option *ngFor="let option of column.filterOptions"
                        [value]="option.value">
              {{option.label}}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field *ngSwitchCase="'number'" appearance="outline">
          <mat-label>{{column.filterPlaceholder || 'Filter ' + column.label}}</mat-label>
          <input matInput type="number"
                 #filterInput
                 (keyup)="onFilter(column, filterInput.value)">
        </mat-form-field>
      </ng-container>
    </div>
  </div>

  <table mat-table [dataSource]="data" class="mat-elevation-z8">
    <ng-container *ngFor="let column of config.columns" [matColumnDef]="column.key">
      <th mat-header-cell *matHeaderCellDef 
          [class.sortable]="column.sortable"
          (click)="onSort(column)">
        {{column.label}}
        <mat-icon *ngIf="column.sortable">
          {{sortedColumn === column.key ? (sortDirection === 'asc' ? 'arrow_upward' : 'arrow_downward') : ''}}
        </mat-icon>
      </th>
      <td mat-cell *matCellDef="let element">{{formatValue(column, element[column.key])}}</td>
    </ng-container>

    <ng-container matColumnDef="actions" *ngIf="config.actions">
      <th mat-header-cell *matHeaderCellDef>Actions</th>
      <td mat-cell *matCellDef="let element" class="actions-cell">
        <button mat-icon-button *ngIf="config.actions.view"
                (click)="onAction('view', element)" class="action-button view">
          <mat-icon style="color:darkslategrey;">visibility</mat-icon>
        </button>
        <button mat-icon-button *ngIf="config.actions.edit"
                (click)="onAction('edit', element)" class="action-button edit">
          <mat-icon style="color: orange;">edit</mat-icon>
        </button>
        <button mat-icon-button *ngIf="config.actions.delete"
                (click)="onAction('delete', element)" class="action-button delete" >
          <mat-icon style="color: red;">delete</mat-icon>
        </button>
        <ng-container *ngFor="let customAction of config.actions?.custom">
          <button mat-icon-button 
                  (click)="onAction(customAction.action, element)"
                  [matTooltip]="customAction.label"
                  class="action-button custom-action"> <mat-icon>{{customAction.icon}}</mat-icon>
          </button>
        </ng-container>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
  </table>

  <div class="loading-overlay" *ngIf="loading">
    <mat-spinner></mat-spinner>
  </div>

  <mat-paginator [length]="totalItems"
                 [pageSize]="pageSize"
                 [pageSizeOptions]="config.pageSizeOptions"
                 (page)="onPageChange($event)">
  </mat-paginator>
</div>