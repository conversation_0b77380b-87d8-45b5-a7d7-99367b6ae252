<ngx-loading 
  [show]="isLoading" 
  [config]="{ 
    animationType: 'threeBounce', 
    backdropBackgroundColour: 'rgba(0,0,0,0.2)', 
    primaryColour: '#10b981',
    secondaryColour: '#10b981',
    tertiaryColour: '#10b981'
  }"></ngx-loading>

<div class="container">

    <div class="header improved-header">
    <div *ngIf="subscriptionIdFromRoute" class="back-button-container" style="margin-bottom: 1rem;">
      <button 
        class="back-btn" 
        (click)="goBackToSubscriptionList()"
        style="
          display: flex;
          align-items: center;
          gap: 0.5rem;
          background: #f3f4f6;
          color: #374151;
          border: 1px solid #d1d5db;
          padding: 0.5rem 1rem;
          border-radius: 0.5rem;
          cursor: pointer;
          font-size: 0.875rem;
          transition: all 0.2s;
        "
        onmouseover="this.style.background='#e5e7eb'"
        onmouseout="this.style.background='#f3f4f6'"
      >
        <span class="material-icons" style="font-size: 1.125rem;">arrow_back</span>
        Retour à la liste des abonnements
      </button>
    </div>
    
    <h1 class="title improved-title">
      {{ subscriptionIdFromRoute ? 'Modifier l\'abonnement' : 'Modifier l\'abonnement pour un client' }}
    </h1>
    <p class="subtitle improved-subtitle">
      {{ subscriptionIdFromRoute ? 'Modifiez les options de cet abonnement.' : 'Choisir des nouveau options ou mettre à niveau l\'abonnement pour le client sélectionné.' }}
    </p>
    </div>
  <div class="licence-cards-container">

    <div class="client-billing-section">
      <div class="search-container">
        <div class="search-input-container" *ngIf="!selectedClient && !subscriptionIdFromRoute">
          <div class="search-input-glass-wrapper" *ngIf="!selectedClient && !subscriptionIdFromRoute">
            <span class="search-input-glass-icon material-icons">search</span>
            <input
              type="text"
              class="search-input glass"
              [ngClass]="{'input-error': showChooseClientError}"
              [(ngModel)]="searchQuery"
              (focus)="showDropdown = true; filterClients()"
              (click)="showDropdown = true; filterClients()"
              (input)="filterClients()"
              placeholder="Rechercher un client..."
              [readonly]="selectedClient !== null"
              autocomplete="off"
            />
            <button
              *ngIf="searchQuery && !selectedClient"
              class="search-input-glass-clear-btn"
              type="button"
              (click)="filterClients()"
              tabindex="-1"
              aria-label="Effacer"
            >
            </button>
            <div class="choose-client-error" *ngIf="showChooseClientError">
              Veuillez choisir un client d'abord.
            </div>
            <div class="dropdown-glass"
                 *ngIf="showDropdown"
                 style="max-height: 320px; overflow-y: auto;"
                 @slideInOut>
              <div 
                *ngFor="let client of filteredClients"
                class="dropdown-glass-item"
                (click)="selectClient(client)"
              >
                <div class="dropdown-glass-info">
                  <div class="dropdown-glass-name">{{ client.Name }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="search-input-container" *ngIf="selectedClient || subscriptionIdFromRoute" style="display: flex; align-items: center; justify-content: center;">
          <div class="selected-client-container improved-client-card" @growIn>
            <div class="client-avatar-section">
            </div>
            <div class="client-details-section">
              <div class="client-name-row">
                <span class="client-name-large">{{ selectedClient?.Name }}</span>
              </div>
              <div class="client-subtitle">
                {{ subscriptionIdFromRoute ? 'Abonnement en cours de modification' : 'Gérez les licences et options pour ce client' }}
              </div>
            </div>
          </div>
        </div>
        <div class="select-type-frequence-payement improved-select-type-frequence-payement">
          <div class="frequence-payement improved-frequence-payement improved-frequence-inline">
            <select class="improved-select" [(ngModel)]="selectedPaymentFrequency" disabled>
              <option *ngFor="let freq of paymentFrequencies" [value]="freq.value">
                {{ freq.label }}
              </option>
            </select>
          </div>
        </div>
      </div>
</div>

<div class="license-grid improved-license-grid"
  [ngClass]="{'single-card': pagedLicences.length === 1}"
  style="overflow-x: hidden; position: relative;"
>
  <ng-container *ngIf="clientSubscription">
    <div class="license-grid improved-license-grid"
      [ngClass]="{'single-card': pagedLicences.length === 1}"
      (mousedown)="onTouchStart($event)" (mouseup)="onTouchEnd($event)"
      (touchstart)="onTouchStart($event)" (touchend)="onTouchEnd($event)"
      style="overflow-x: hidden; position: relative;"
    >
      <ng-container *ngFor="let licence of pagedLicences">
        <div 
          class="license-card improved-license-card"
          [ngClass]="{'single-card-inner': pagedLicences.length === 1}"
        >
          <div class="card-header improved-card-header">
            <h3 class="improved-title">{{ licence.Name }}</h3>
            <div class="card-description-wrapper">
              <p class="card-description improved-card-description" [title]="licence.Description">
                {{ licence.Description || 'Aucune description disponible' }}
              </p>
            </div>
          </div>
          <div class="features-list-wrapper">
            <ul class="features-list improved-features-list">
              <li *ngFor="let option of getOptionsForLicence(licence)">
                <div class="feature-item">
                  <label class="custom-checkbox">
                    <input 
                      type="checkbox"
                      [checked]="isOptionChecked(licence, option.Id)"
                      (change)="toggleOption(licence, option.Id, $event)"
                      [disabled]="!selectedClient"
                    />
                    <span class="checkmark" [ngClass]="{
                      'checked-green': isOptionChecked(licence, option.Id),
                      'unchecked-red': !isOptionChecked(licence, option.Id)
                    }">
                      <span 
                        class="material-icons verified-icon"
                        *ngIf="isOptionChecked(licence, option.Id)"
                        style="color: #10b981; opacity: 1; transform: scale(1);"
                      >check_circle</span>
                      <span 
                        class="material-icons cancel-icon"
                        *ngIf="!isOptionChecked(licence, option.Id)"
                        style="color: #ef4444; opacity: 1; transform: scale(1);"
                      >cancel</span>
                    </span>
                  </label>
                  <div class="feature-details">
                    <span class="feature-name">{{ option.Name }}</span>
                  </div>
                </div>
              </li>
            </ul>
          </div>
          <div class="license-actions">
            <button 
              *ngIf="isLicenceAssignedToClient(licence) && hasOptionChanges(licence) && !isSubscriptionCancelled(licence)"
              class="select-btn"
              style="background: linear-gradient(135deg, #49b38e, #2c7744);"
              (click)="openModifyPopup(licence)">
              Sauvegarder
            </button>

            <button
              *ngIf="!isLicenceAssignedToClient(licence) && !isSubscriptionCancelled(licence)"
              class="select-btn"
              style="background: linear-gradient(135deg, #49b38e, #2c7744);"
              (click)="upgradeLicenceForClient(licence)">
              Mise à niveau
            </button>
          </div>
        </div>
      </ng-container>
    </div>
  </ng-container>
</div>
  </div>

  <div class="overlay" *ngIf="showUpgradePopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Confirmer la mise à niveau de la licence</h3>
        <button class="close-popup-btn" (click)="cancelUpgradePopup()">
          <span class="material-icons">close</span>
        </button>
      </div>
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon">upgrade</span>
          <p>
            Êtes-vous sûr de vouloir mettre à niveaula licence <strong>{{ selectedLicenseForConfirmation?.Name }}</strong> pour le client
            <strong>{{ selectedClient?.Name }}</strong> ?
          </p>
        </div>
        <div class="client-license-summary">
          <div class="summary-item">
            <span class="label">Client :</span>
            <div class="client-info-summary">
              <img *ngIf="selectedClient?.ClientLogo" [src]="'data:image/png;base64,' + selectedClient?.ClientLogo"
                   [alt]="selectedClient?.Name" class="client-image">
              <span>{{ selectedClient?.Name }}</span>
            </div>
          </div>
          <div class="summary-item">
            <span class="label">Ancienne licence :</span>
            <span class="license-name">{{ oldLicence?.Name }}</span>
          </div>
          <div class="summary-item">
            <span class="label">Anciennes options :</span>
            <ul class="chosen-options-list" style="list-style: none; margin: 0; padding: 0; display: flex; flex-wrap: wrap; gap: 8px;">
              <li *ngFor="let option of oldOptions" style="margin: 0; padding: 0;">
                <span style="
                  display: inline-block;
                  background: #e5e7eb;
                  color: #374151;
                  border-radius: 18px;
                  padding: 5px 16px;
                  font-size: 1rem;
                  font-weight: 600;
                  margin-bottom: 2px;
                  letter-spacing: 0.01em;
                  border: none;
                ">
                  {{ option.Name }}
                </span>
              </li>
            </ul>
          </div>
          <div class="summary-item">
            <span class="label">Nouvelle licence :</span>
            <span class="license-name">{{ selectedLicenseForConfirmation?.Name }}</span>
          </div>
          <div class="summary-item">
            <span class="label">Nouvelles options :</span>
            <ul class="chosen-options-list" style="list-style: none; margin: 0; padding: 0; display: flex; flex-wrap: wrap; gap: 8px;">
              <li *ngFor="let option of getNewOptionsForUpgrade()" style="margin: 0; padding: 0;">
                <span style="
                  display: inline-block;
                  background: linear-gradient(90deg, #10b981 60%, #34d399 100%);
                  color: #fff;
                  border-radius: 18px;
                  padding: 5px 16px;
                  font-size: 1rem;
                  font-weight: 600;
                  margin-bottom: 2px;
                  letter-spacing: 0.01em;
                  border: none;
                ">
                  {{ option.Name }}
                </span>
              </li>
            </ul>
          </div>
          <div class="summary-item">
            <span class="label">Ancien total:</span>
            <span class="license-total">{{ proratedOldForUpgrade | number:'1.0-2' }} €</span>
          </div>
          <div class="summary-item" *ngIf="proratedDiferencePay > 0">
            <span class="label">Prix à payer:</span>
            <span class="licence-total">{{ proratedDiferencePay | number:'1.0-2' }} €</span>
          </div>
          <div class="summary-item" *ngIf="proratedDiferenceReturn > 0">
            <span class="label">Prix à rembourser:</span>
            <span class="licence-total">- {{ proratedDiferenceReturn | number:'1.0-2' }} €</span>
          </div>
          <div class="summary-item">
            <span class="label">Nouveau prix :</span>
            <span class="license-total">{{ proratedTotalForUpgrade | number:'1.0-2' }} €</span>
          </div>
        </div>
      </div>
      <div class="popup-actions">
        <button class="cancel-btn" (click)="cancelUpgradePopup()">
          Annuler
        </button>
        <button class="confirm-btn" (click)="confirmUpgradeLicence()">
          Oui, mise a niveau la licence
        </button>
      </div>
    </div>
  </div>

  <div class="overlay" *ngIf="showModifyPopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Confirmer la modification de la licence</h3>
        <button class="close-popup-btn" (click)="cancelModifyLicence()">
          <span class="material-icons">close</span>
        </button>
      </div>
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon">edit</span>
          <p>
            Êtes-vous sûr de vouloir modifier les options de la licence
            <strong>{{ modifyingLicence?.Name }}</strong>
            pour le client
            <strong>{{ selectedClient?.Name }}</strong> ?
          </p>
        </div>
        <div class="client-license-summary">
          <div class="summary-item">
            <span class="label">Client :</span>
            <div class="client-info-summary">
              <img *ngIf="selectedClient?.ClientLogo" [src]="'data:image/png;base64,' + selectedClient?.ClientLogo"
                   [alt]="selectedClient?.Name" class="client-image">
              <span>{{ selectedClient?.Name }}</span>
            </div>
          </div>
          <div class="summary-item">
            <span class="label">Licence :</span>
            <span class="license-name">{{ modifyingLicence?.Name }}</span>
          </div>
          <div class="summary-item">
            <span class="label">Anciennes options :</span>
            <ul class="chosen-options-list" style="list-style: none; margin: 0; padding: 0; display: flex; flex-wrap: wrap; gap: 8px;">
              <li *ngFor="let option of getOldOptionsForModifying()" style="margin: 0; padding: 0;">
                <span style="
                  display: inline-block;
                  background: #e5e7eb;
                  color: #374151;
                  border-radius: 18px;
                  padding: 5px 16px;
                  font-size: 1rem;
                  font-weight: 600;
                  margin-bottom: 2px;
                  letter-spacing: 0.01em;
                  border: none;
                ">
                  {{ option.Name }}
                </span>
              </li>
            </ul>
          </div>
          <div class="summary-item">
            <span class="label">Nouvelles options :</span>
            <ul class="chosen-options-list" style="list-style: none; margin: 0; padding: 0; display: flex; flex-wrap: wrap; gap: 8px;">
              <li *ngFor="let option of getNewOptionsForModifying()" style="margin: 0; padding: 0;">
                <span style="
                  display: inline-block;
                  background: linear-gradient(90deg, #10b981 60%, #34d399 100%);
                  color: #fff;
                  border-radius: 18px;
                  padding: 5px 16px;
                  font-size: 1rem;
                  font-weight: 600;
                  margin-bottom: 2px;
                  letter-spacing: 0.01em;
                  border: none;
                ">
                  {{ option.Name }}
                </span>
              </li>
            </ul>
          </div>
          <div class="summary-item">
            <span class="label">Ancien total :</span>
            <span class="license-total">{{ getOldTotalForModifying() | number:'1.0-2' }} €</span>
          </div>
          <div class="summary-item" *ngIf="getModificationType() === 'add'">
            <span class="label">Prix à payer :</span>
            <span class="license-total">{{ getPriceToPayForModification() | number:'1.0-2' }} €</span>
          </div>
          <div class="summary-item" *ngIf="getModificationType() === 'remove'">
            <span class="label">Prix à rembourser :</span>
            <span class="license-total">- {{ getPriceToRefundForModification() | number:'1.0-2' }} €</span>
          </div>
          <div class="summary-item" *ngIf="getModificationType() === 'both'">
            <span class="label">Prix à payer :</span>
            <span class="license-total">{{ getPriceToPayForModification() | number:'1.0-2' }} €</span>
          </div>
          <div class="summary-item" *ngIf="getModificationType() === 'both'">
            <span class="label">Prix à rembourser :</span>
            <span class="license-total">- {{ getPriceToRefundForModification() | number:'1.0-2' }} €</span>
          </div>
          <div class="summary-item">
            <span class="label">Nouveau total :</span>
            <span class="license-total">{{ getNewTotalWithOldForModifying() | number:'1.0-2' }} €</span>
          </div>
        </div>
      </div>
      <div class="popup-actions">
        <button class="cancel-btn" (click)="cancelModifyLicence()">
          Annuler
        </button>
        <button class="confirm-btn" (click)="confirmSaveModifiedLicenceOptions()">
          Oui, sauvegarder les modifications
        </button>
      </div>
    </div>
  </div>

  <div class="overlay" *ngIf="showSaveOrDiscardPopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Modifications non sauvegardées</h3>
        <button class="close-popup-btn" (click)="showSaveOrDiscardPopup = false">
          <span class="material-icons">close</span>
        </button>
      </div>
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon">warning</span>
          <p>
            Vous avez des modifications non sauvegardées pour la licence
            <strong>{{ licenceWithUnsavedChanges?.Name }}</strong>.
            Voulez-vous sauvegarder les modifications avant de désélectionner le client ?
          </p>
        </div>
      </div>
      <div class="popup-actions">
        <button class="cancel-btn" (click)="discardChangesAndClearSelection()">
          Ne pas sauvegarder
        </button>
        <button class="confirm-btn" (click)="confirmSaveAndClearSelection()">
          Sauvegarder et continuer
        </button>
      </div>
    </div>
  </div>

  <div class="success-notification" *ngIf="showSaveNotification" @slideDown>
    <div class="notification-content">
      <span class="material-icons success-icon">check_circle</span>
      <div class="notification-text">
        <h4>Options sauvegardées avec succès !</h4>
        <p>Les modifications ont été enregistrées.</p>
      </div>
      <button class="close-notification-btn" (click)="showSaveNotification = false">
        <span class="material-icons">close</span>
      </button>
    </div>
  </div>

  <div class="overlay" *ngIf="showNoOptionCheckedPopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Attention</h3>
      </div>
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon" style="color:#ef4444;">error_outline</span>
          <p>Vous devez cocher au moins une option.</p>
        </div>
      </div>
      <div class="popup-actions">
        <button class="confirm-btn" (click)="closeNoOptionCheckedPopup()">OK</button>
      </div>
    </div>
  </div>

 <div class="pagination-controls improved-pagination-controls">
    <button (click)="prevPage()" [disabled]="currentPage === 0" class="pagination-btn prev-btn improved-pagination-btn">
      &lt;
    </button>
    <span class="pagination-indicator improved-pagination-indicator">
      <span class="pagination-page-number improved-pagination-page-number">
        {{ currentPage + 1 }}
      </span>
    </span>
    <button (click)="nextPage()" [disabled]="currentPage === totalPages - 1" class="pagination-btn next-btn improved-pagination-btn">
      &gt;
    </button>
  </div>
  