// client-form.service.ts
import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ClientFormComponent } from '@app/pages/organisation/client-form/client-form.component';
import { Client } from '@app/core/models/client';
import { firstValueFrom } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class ClientFormService {
    constructor(private dialog: MatDialog) { }

    async openCreateClientDialog(organisations: any[]): Promise<Client | undefined> {
        const dialogRef = this.dialog.open(ClientFormComponent, {
            panelClass: "custom-dialog",
            disableClose: true,
            data: {
                client: null,
                organisations: organisations,
                isEdit: false
            }            
        });

        return await firstValueFrom(dialogRef.afterClosed());
    }

    async openEditClientDialog(client: Client, organisations: any[]): Promise<Client | undefined> {
        const dialogRef = this.dialog.open(ClientFormComponent, {
            panelClass: "custom-dialog",
            disableClose: true,
            data: {
                client: { ...client },
                organisations: organisations,
                isEdit: true
            }
        });

        return await firstValue<PERSON>rom(dialogRef.afterClosed());
    }
}