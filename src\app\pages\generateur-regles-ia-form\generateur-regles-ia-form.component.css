/* Enhanced Smart Home Automation Generator Styles */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Modern CSS Variables */
:root {
  --primary: #3b82f6;
  --primary-dark: #2563eb;
  --primary-light: #93c5fd;
  --secondary: #6366f1;
  --accent: #8b5cf6;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #06b6d4;
  
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  --border-radius: 12px;
  --border-radius-lg: 16px;
  --border-radius-xl: 20px;
  
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
  color: var(--gray-700);
  line-height: 1.6;
  min-height: 100vh;
}

/* Main Container */
.automation-generator {
  max-width: 1400px;
  margin: 2rem auto;
  padding: 0 1rem;
}

/* Header */
.header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.header::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  border-radius: 2px;
}

.main-title {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(135deg, var(--primary), var(--secondary), var(--accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  letter-spacing: -0.02em;
}

.subtitle {
  font-size: 1.25rem;
  color: var(--gray-500);
  font-weight: 400;
}

/* Status Bar */
.status-bar {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
  transition: var(--transition);
}

.status-bar:hover {
  box-shadow: var(--shadow-md);
}

.device-stats {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: var(--gray-50);
  border-radius: var(--border-radius);
  border: 1px solid var(--gray-200);
  transition: var(--transition);
}

.stat-item:hover {
  background: white;
  box-shadow: var(--shadow);
  transform: translateY(-2px);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
}

.stat-icon.sensors { 
  background: linear-gradient(135deg, var(--info), var(--primary)); 
}

.stat-icon.actuators { 
  background: linear-gradient(135deg, var(--success), var(--primary)); 
}

.stat-content h4 {
  font-size: 0.875rem;
  color: var(--gray-500);
  font-weight: 500;
  margin: 0;
}

.stat-content span {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--gray-800);
  display: block;
}

/* Loading States */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  flex-direction: column;
  gap: 1rem;
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--gray-200);
  border-top: 4px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 1.125rem;
  color: var(--gray-500);
  font-weight: 500;
}

/* Message Alerts */
.message-alert {
  margin-bottom: 2rem;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  border-left: 4px solid;
  font-weight: 500;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.message-alert::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  opacity: 0.3;
}

.message-alert.success {
  background: linear-gradient(135deg, #f0fdf4, #ecfdf5);
  border-color: var(--success);
  color: #166534;
}

.message-alert.success::before {
  background: var(--success);
}

.message-alert.error {
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
  border-color: var(--error);
  color: #991b1b;
}

.message-alert.error::before {
  background: var(--error);
}

.message-alert.info {
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
  border-color: var(--info);
  color: #1e40af;
}

.message-alert.info::before {
  background: var(--info);
}

/* Cards */
.card {
  background: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  margin-bottom: 2rem;
  overflow: hidden;
  transition: var(--transition);
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  opacity: 0;
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card:hover::before {
  opacity: 1;
}

.card-header {
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid var(--gray-100);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  flex-wrap: wrap;
}

.card-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--gray-800);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
  margin: 0;
}

.card-title mat-icon {
  font-size: 1.75rem;
  color: var(--primary);
  width: 1.75rem;
  height: 1.75rem;
}

.card-body {
  padding: 2rem;
}

/* Form Elements */
.form-grid {
  display: grid;
  gap: 1.5rem;
}

.form-grid.cols-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-weight: 600;
  color: var(--gray-700);
  font-size: 0.875rem;
  letter-spacing: 0.025em;
  margin: 0;
}

.form-control {
  padding: 0.875rem 1rem;
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
  background: white;
  font-family: inherit;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control:disabled {
  background: var(--gray-50);
  color: var(--gray-400);
  cursor: not-allowed;
  border-color: var(--gray-200);
}

.form-help {
  font-size: 0.75rem;
  color: var(--gray-500);
  margin-top: 0.25rem;
}

/* Checkbox */
.checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
}

.checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid var(--gray-300);
  border-radius: 6px;
  position: relative;
  cursor: pointer;
  transition: var(--transition);
  background: white;
  appearance: none;
  -webkit-appearance: none;
}

.checkbox:checked {
  background: var(--primary);
  border-color: var(--primary);
}

.checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.checkbox:hover {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Buttons */
.btn {
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  font-family: inherit;
  white-space: nowrap;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn mat-icon {
  font-size: 1.125rem;
  width: 1.125rem;
  height: 1.125rem;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  box-shadow: var(--shadow);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--secondary), var(--accent));
  color: white;
  box-shadow: var(--shadow);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-success {
  background: linear-gradient(135deg, var(--success), #059669);
  color: white;
  box-shadow: var(--shadow);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-outline {
  background: white;
  color: var(--gray-700);
  border: 2px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
}

.btn-outline:hover {
  background: var(--gray-50);
  border-color: var(--gray-300);
  transform: translateY(-1px);
}

.btn-danger {
  background: linear-gradient(135deg, var(--error), #dc2626);
  color: white;
  box-shadow: var(--shadow);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: var(--shadow-sm) !important;
}

/* Action Buttons Container */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  margin-top: 3rem;
}

/* Condition/Action Items */
.item-container {
  margin-bottom: 1.5rem;
}

.item-card {
  background: var(--gray-50);
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  position: relative;
  transition: var(--transition);
}

.item-card:hover {
  border-color: var(--primary);
  background: white;
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--gray-200);
}

.item-title {
  font-weight: 600;
  color: var(--gray-800);
  font-size: 1.125rem;
  margin: 0;
}

.remove-btn {
  background: linear-gradient(135deg, var(--error), #dc2626);
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--shadow);
}

.remove-btn:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.remove-btn mat-icon {
  font-size: 1rem;
  width: 1rem;
  height: 1rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  background: var(--gray-50);
  border-radius: var(--border-radius);
  border: 2px dashed var(--gray-300);
  margin: 1rem 0;
  transition: var(--transition);
}

.empty-state:hover {
  border-color: var(--primary);
  background: white;
}

.empty-state mat-icon {
  font-size: 3rem;
  color: var(--gray-400);
  margin-bottom: 1rem;
  width: 3rem;
  height: 3rem;
}

.empty-state h3 {
  font-size: 1.25rem;
  color: var(--gray-600);
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.empty-state p {
  color: var(--gray-500);
  margin: 0;
}

/* JSON Output */
.json-container {
  background: var(--gray-900);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  margin-top: 2rem;
  box-shadow: var(--shadow-xl);
}

.json-header {
  background: linear-gradient(135deg, var(--gray-800), var(--gray-700));
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  flex-wrap: wrap;
}

.json-title {
  color: white;
  font-weight: 600;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0;
}

.json-title mat-icon {
  color: var(--primary-light);
}

.json-preview {
  padding: 2rem;
  background: var(--gray-800);
  color: var(--gray-100);
  font-family: 'Fira Code', 'Consolas', 'SF Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.6;
  overflow-x: auto;
  max-height: 400px;
  overflow-y: auto;
}

.json-preview::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.json-preview::-webkit-scrollbar-track {
  background: var(--gray-700);
  border-radius: 4px;
}

.json-preview::-webkit-scrollbar-thumb {
  background: var(--gray-600);
  border-radius: 4px;
}

.json-preview::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}

.json-key { color: #7dd3fc; }
.json-string { color: #bbf7d0; }
.json-number { color: #fde68a; }
.json-boolean { color: #fda4af; }

/* Conditional Sections */
.conditional-section {
  margin: 1.5rem 0;
  padding: 1.5rem;
  border-radius: var(--border-radius);
  position: relative;
  border: 1px solid;
}

.conditional-section.condition {
  background: linear-gradient(135deg, #eff6ff, #f0f9ff);
  border-color: #bfdbfe;
}

.conditional-section.action {
  background: linear-gradient(135deg, #f0fdf4, #ecfdf5);
  border-color: #bbf7d0;
}

.conditional-title {
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
}

.conditional-title mat-icon {
  font-size: 1.25rem;
  width: 1.25rem;
  height: 1.25rem;
}

.condition .conditional-title {
  color: #1e40af;
}

.action .conditional-title {
  color: #166534;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .automation-generator {
    max-width: 100%;
    padding: 0 1rem;
  }
  
  .form-grid.cols-2 {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .automation-generator {
    margin: 1rem auto;
    padding: 0 0.5rem;
  }

  .main-title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .card-body {
    padding: 1.5rem;
  }

  .card-header {
    padding: 1.5rem 1.5rem 1rem;
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .status-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .device-stats {
    justify-content: center;
  }

  .action-buttons {
    flex-direction: column;
  }

  .item-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .json-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .stat-item {
    flex-direction: column;
    text-align: center;
  }

  .btn {
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
  }

  .form-control {
    padding: 0.75rem;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.card {
  animation: fadeInUp 0.6s ease-out;
}

.item-card {
  animation: fadeInUp 0.4s ease-out;
}

.stat-item {
  animation: slideInRight 0.5s ease-out;
}

.loading-container .spinner {
  animation: spin 1s linear infinite;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-2 { margin-top: 0.5rem; }
.mt-4 { margin-top: 1rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }

.flex { display: flex; }
.inline-flex { display: inline-flex; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }

.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }

.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

/* Dark Mode Support (Optional) */
@media (prefers-color-scheme: dark) {
  :root {
    --gray-50: #0f172a;
    --gray-100: #1e293b;
    --gray-200: #334155;
    --gray-300: #475569;
    --gray-400: #64748b;
    --gray-500: #94a3b8;
    --gray-600: #cbd5e1;
    --gray-700: #e2e8f0;
    --gray-800: #f1f5f9;
    --gray-900: #f8fafc;
  }
  
  body {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    color: var(--gray-700);
  }
}

/* Print Styles */
@media print {
  .automation-generator {
    margin: 0;
    padding: 0;
  }
  
  .btn {
    display: none;
  }
  
  .card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid var(--gray-300);
  }
  
  .json-container {
    background: white;
    color: black;
    border: 1px solid var(--gray-300);
  }
  
  .json-preview {
    background: white;
    color: black;
  }
}