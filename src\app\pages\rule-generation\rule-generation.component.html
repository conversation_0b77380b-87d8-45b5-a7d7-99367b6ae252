<div class="card">
    <!-- Header Component -->
    <app-rule-header [isLoading]="getLoadingState()" (refreshData)="refreshData()">
    </app-rule-header>

    <!-- Search and Create Component -->
    <app-rule-search-create [(searchTerm)]="searchTerm" [isSearching]="isSearching"
        [hasPendingOperations]="hasPendingOperations()" (submitSearch)="onSearchSubmit()"
        (clearSearch)="onSearchClear()" (openAiRuleGeneratorDialog)="openAiRuleGeneratorDialog()"
        (openRuleFormDialog)="openRuleFormDialog()">
    </app-rule-search-create>

    <!-- Filter and Sort Component -->
    <app-rule-filter-sort [isLoading]="getLoadingState()" [hasPendingOperations]="hasPendingOperations()"
        [currentSortColumn]="currentSortColumn" [currentSortDirection]="currentSortDirection"
        [sortOptions]="sortOptions" (updateSort)="updateSort($event)">
    </app-rule-filter-sort>

    <!-- Loading and Empty States -->
    <div *ngIf="rules.length === 0 && searchTerm && !isSearching" class="no-data-message">
        <p>Aucune règle trouvée pour "{{ searchTerm }}".
            <button mat-button (click)="onSearchClear()" class="clear-search-button">
                <mat-icon>clear</mat-icon>
                Effacer la recherche
            </button>
        </p>
    </div>

    <div *ngIf="rules.length === 0 && !searchTerm && !isSearching && !getLoadingState()" class="no-data-message">
        <p>Aucune règle trouvée.
            <ng-container *ngIf="appliedFilters.length > 0">
                Les filtres appliqués ne correspondent à aucune règle.
                <button mat-button (click)="clearAllFilters()" class="clear-search-button">
                    <mat-icon>clear_all</mat-icon>
                    Effacer les filtres
                </button>
            </ng-container>
            <ng-container *ngIf="appliedFilters.length === 0">
                Cliquez sur "Créer une nouvelle règle" pour en ajouter une.
            </ng-container>
        </p>
    </div>

    <div *ngIf="isSearching" class="search-loading-message">
        <mat-icon class="loading-icon">search</mat-icon>
        <p>Recherche en cours...</p>
    </div>

    <!-- Rules List Component -->
    <app-rule-list [rules]="isSearchMode ? filteredRules : rules" [expandedRuleId]="expandedRuleId"
        [selectedControllerId]="selectedControllerId" [isLoading]="getLoadingState()" [isDeleting]="isDeleting"
        [activeTab]="activeTab" [getRuleSummary]="getRuleSummary" [hasRuleSummary]="hasRuleSummary"
        [formatRawData]="formatRawData" [formatTimestamp]="formatTimestamp"
        [getControllerStatusIcon]="getControllerStatusIcon" [getControllerStatusText]="getControllerStatusText"
        [getControllerStatusClass]="getControllerStatusClass" (toggleExpandRule)="toggleExpandRule($event)"
        (viewControllerDetails)="viewControllerDetails($event)" (editRule)="openRuleFormDialog($event)"
        (duplicateRule)="duplicateRule($event)" (deleteRule)="deleteRule($event)"
        (toggleRuleStatus)="toggleRuleStatus($event)" (openTransactionHistory)="openTransactionHistory($event)"
        (setActiveTab)="setActiveTab($event)" (selectController)="selectController($event)"
        (copyRawData)="copyRawData($event)" (downloadRawData)="downloadRawData($event)">
    </app-rule-list>

    <!-- Pagination Component -->
    <app-rule-pagination [currentPage]="currentPage" [pageCount]="pageCount" [totalElements]="totalElements"
        [searchTerm]="searchTerm" [appliedFiltersCount]="appliedFilters.length"
        [hasPendingOperations]="hasPendingOperations()" [isLoading]="getLoadingState()" (previousPage)="previousPage()"
        (nextPage)="nextPage()" (goToPage)="goToPage($event)">
    </app-rule-pagination>

    <!-- Global loading overlay for critical operations -->
    <ngx-ui-loader *ngIf="getLoadingState() && !isSearching"></ngx-ui-loader>
    <ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>
</div>