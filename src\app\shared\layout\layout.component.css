/* src/app/shared/components/layout/layout.component.css */
:host {
  display: flex;
  flex-direction: column;
  height: 99vh;
  max-width: 97vw;
}

app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000; /* Lower than sidebar */
  height: 60px;
}

app-sidebar {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1100; /* Higher than header */
}

.main-content {
  margin-left: 250px;
  margin-top: 60px;
  padding: 20px;
  flex: 1;
  overflow: auto;
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: calc(100% - 250px);
}

.main-content.sidebar-collapsed {
  margin-left: 80px; /* Match sidebar collapsed width */
  width: calc(100% - 80px);
}

@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
    margin-top: 60px;
    width: 100%;
  }

  .main-content.sidebar-collapsed {
    margin-left: 0;
    width: 100%;
  }
}