import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';import { ApiService } from '../api.service';
import { TypeLocal } from '@app/core/models/TypeLocal';

@Injectable({ providedIn: 'root' })
export class TypeLocalApiService extends ApiService<TypeLocal> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("type-local");
  }
}

