import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RuleContent } from '@app/shared/models/rule/RuleContent';
import { RuleExecutionChartData, RuleExecutionSimple } from '@app/core/services/administrative/rules.service';
import { NgChartsModule } from 'ng2-charts';
import { ChartConfiguration, ChartType } from 'chart.js';


@Component({
  selector: 'app-rule-list',
  templateUrl: './rule-list.component.html',
  styleUrls: ['./rule-list.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    NgChartsModule // <-- Add this
  ]
})
export class RuleListComponent {
  @Input() rules: RuleContent[] = [];
  @Input() expandedRuleId: string | null = null;
  @Input() selectedControllerId: string | null = null;
  @Input() isLoading: boolean | null = false;
  @Input() isDeleting = new Set<string>();
  @Input() activeTab: 'hierarchy' | 'rawdata' | 'summary' = 'summary';
  @Input() pagedExecutions: { [ruleId: string]: import('@app/shared/models/rule/PagedResponse').PagedResponse<RuleExecutionSimple[]> } = {};
  @Input() pagedExecutionsPageSize: number = 10;
  @Output() executionsPageChange = new EventEmitter<{ ruleId: string, page: number }>();
  @Output() controllerExecutionsPageChange = new EventEmitter<{ ruleId: string, controllerId: string, page: number }>();

  @Output() toggleExpandRule = new EventEmitter<string>();
  @Output() viewControllerDetails = new EventEmitter<string>();
  @Output() editRule = new EventEmitter<string>();
  @Output() duplicateRule = new EventEmitter<string>();
  @Output() deleteRule = new EventEmitter<string>();
  @Output() toggleRuleStatus = new EventEmitter<RuleContent>();
  @Output() openTransactionHistory = new EventEmitter<string>();
  @Output() setActiveTab = new EventEmitter<'hierarchy' | 'rawdata' | 'summary'>();
  @Output() selectController = new EventEmitter<string>();
  @Output() copyRawData = new EventEmitter<string>();
  @Output() downloadRawData = new EventEmitter<RuleContent>();

  // Helper functions that need to be passed as inputs or implemented here
  @Input() getRuleSummary!: (ruleId: string) => string;
  @Input() hasRuleSummary!: (ruleId: string) => boolean;
  @Input() formatRawData!: (ruleId: string) => string;
  @Input() formatTimestamp!: (timestamp: string) => string;
  @Input() getControllerStatusIcon!: (controller: any) => string;
  @Input() getControllerStatusText!: (controller: any) => string;
  @Input() getControllerStatusClass!: (controller: any) => string;

  isRuleActionPending(ruleId: string): boolean {
    return this.isDeleting.has(ruleId);
  }

  isSimpleRule(rule: RuleContent): boolean {
    const hasMinimalData = (
      rule.clients.length === 0 &&
      rule.tags.length === 0 &&
      rule.totalApplications === 0 &&
      (!rule.conditions || rule.conditions.length <= 1) &&
      (!rule.actions || rule.actions.length <= 1)
    );

    const isNewRule = (
      !rule.lastTriggered || rule.lastTriggered === '' || rule.lastTriggered === 'Jamais'
    );

    return hasMinimalData && isNewRule;
  }

  getTotalClientsForRule(rule: RuleContent): number {
    return rule.clients.length;
  }

  getTotalControllersForRule(rule: RuleContent): number {
    return rule.clients.reduce((total, client) => {
      return total + client.sites.reduce((siteAcc, site) => {
        return siteAcc + site.locations.reduce((locAcc, location) => {
          return locAcc + location.controllers.length;
        }, 0);
      }, 0);
    }, 0);
  }

  // NEW: Helper methods for execution data display
  formatDateForChart(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', { 
      day: '2-digit', 
      month: '2-digit' 
    });
  }

  getChartTooltip(chartData: RuleExecutionChartData): string {
    const date = new Date(chartData.ExecutionDate).toLocaleDateString('fr-FR');
    return `${date}: ${chartData.SuccessfulExecutions} succès, ${chartData.FailedExecutions} échecs sur ${chartData.TotalExecutions} total`;
  }

  getSuccessPercentage(chartData: RuleExecutionChartData): number {
    if (chartData.TotalExecutions === 0) return 0;
    return (chartData.SuccessfulExecutions / chartData.TotalExecutions) * 100;
  }

  getFailurePercentage(chartData: RuleExecutionChartData): number {
    if (chartData.TotalExecutions === 0) return 0;
    return (chartData.FailedExecutions / chartData.TotalExecutions) * 100;
  }

  getTotalExecutions(chartData: RuleExecutionChartData[]): number {
    return chartData.reduce((total, day) => total + day.TotalExecutions, 0);
  }

  getSuccessRate(chartData: RuleExecutionChartData[]): number {
    const totalExecutions = this.getTotalExecutions(chartData);
    if (totalExecutions === 0) return 0;
    
    const totalSuccessful = chartData.reduce((total, day) => total + day.SuccessfulExecutions, 0);
    return Math.round((totalSuccessful / totalExecutions) * 100);
  }

  getExecutionsForRule(ruleId: string): RuleExecutionSimple[] {
    const paged = this.pagedExecutions[ruleId];
    return paged ? paged.Content || [] : [];
  }

  getExecutionsPageCount(ruleId: string): number {
    const paged = this.pagedExecutions[ruleId];
    if (!paged) return 1;
    return paged.Lister?.Pagination?.PageCount || 1;
  }

  getExecutionsCurrentPage(ruleId: string): number {
    const paged = this.pagedExecutions[ruleId];
    if (!paged) return 1;
    return paged.Lister?.Pagination?.CurrentPage || 1;
  }

  onExecutionsPageChange(ruleId: string, page: number): void {
    this.executionsPageChange.emit({ ruleId, page });
  }

  getLastExecutionDate(executions: RuleExecutionSimple[]): string {
    if (!executions || executions.length === 0) {
      return 'Aucune';
    }
    
    // Find the most recent execution
    const sortedExecutions = [...executions].sort((a, b) => 
      new Date(b.ExecutionTimestamp).getTime() - new Date(a.ExecutionTimestamp).getTime()
    );
    
    return this.formatTimestamp(sortedExecutions[0].ExecutionTimestamp);
  }

  getControllerExecutionsPageCount(ruleId: string, controllerId: string): number {
    const paged = this.pagedExecutions[`${ruleId}_${controllerId}`];
    if (!paged) return 1;
    return paged.Lister?.Pagination?.PageCount || 1;
  }

  getControllerExecutionsCurrentPage(ruleId: string, controllerId: string): number {
    const paged = this.pagedExecutions[`${ruleId}_${controllerId}`];
    if (!paged) return 1;
    return paged.Lister?.Pagination?.CurrentPage || 1;
  }

  onControllerExecutionsPageChange(ruleId: string, controllerId: string, page: number): void {
    this.controllerExecutionsPageChange.emit({ ruleId, controllerId, page });
  }

  // Event handlers
  handleToggleExpandRule(ruleId: string): void {
    this.toggleExpandRule.emit(ruleId);
  }

  handleEditRule(ruleId: string): void {
    this.editRule.emit(ruleId);
  }

  handleDuplicateRule(ruleId: string): void {
    this.duplicateRule.emit(ruleId);
  }

  handleDeleteRule(ruleId: string): void {
    this.deleteRule.emit(ruleId);
  }

  handleToggleRuleStatus(rule: RuleContent): void {
    this.toggleRuleStatus.emit(rule);
  }

  handleOpenTransactionHistory(ruleId: string): void {
    this.openTransactionHistory.emit(ruleId);
  }

  handleSetActiveTab(tab: 'hierarchy' | 'rawdata' | 'summary'): void {
    this.setActiveTab.emit(tab);
  }

  handleSelectController(controllerId: string): void {
    this.selectController.emit(controllerId);
  }

  handleCopyRawData(ruleId: string): void {
    this.copyRawData.emit(ruleId);
  }

  handleDownloadRawData(rule: RuleContent): void {
    this.downloadRawData.emit(rule);
  }

  handleViewControllerDetails(controllerId: string): void {
    this.viewControllerDetails.emit(controllerId);
  }

  // Returns the max total executions in the data (for scaling)
  getMaxExecutions(chartData: RuleExecutionChartData[]): number {
    if (!chartData || chartData.length === 0) return 100;
    return Math.max(...chartData.map(day => day.TotalExecutions), 100);
  }

  // Returns Y-axis ticks (e.g., [100, 80, 60, 40, 20, 0])
  getYAxisTicks(chartData: RuleExecutionChartData[], steps: number = 5): number[] {
    const max = this.getMaxExecutions(chartData);
    const step = Math.ceil(max / steps);
    const ticks = [];
    for (let i = max; i >= 0; i -= step) {
      ticks.push(i);
    }
    if (ticks[ticks.length - 1] !== 0) ticks.push(0);
    return ticks;
  }

  // Returns the percentage height for a bar, relative to the max
  getBarHeight(value: number, chartData: RuleExecutionChartData[]): number {
    const max = this.getMaxExecutions(chartData);
    return max === 0 ? 0 : (value / max) * 100;
  }

  // Chart.js integration
  public barChartOptions: ChartConfiguration['options'] = {
    responsive: true,
    maintainAspectRatio: false, // Allow custom height
    plugins: {
      legend: {
        display: true,
        position: 'bottom',
        labels: {
          color: '#334155',
          font: { size: 11, weight: 'bold' }
        }
      },
      tooltip: {
        enabled: true,
        backgroundColor: '#fff',
        titleColor: '#10b981',
        bodyColor: '#334155',
        borderColor: '#10b981',
        borderWidth: 1,
        titleFont: { weight: 'bold', size: 12 }
      }
    },
    scales: {
      x: {
        title: { display: true, text: 'Jour', color: '#64748b', font: { size: 12, weight: 'bold' } },
        grid: { color: '#e2e8f0' },
        ticks: { color: '#64748b', font: { size: 11 } }
      },
      y: {
        title: { display: true, text: 'Exécutions', color: '#64748b', font: { size: 12, weight: 'bold' } },
        grid: { color: '#e2e8f0' },
        ticks: { color: '#64748b', font: { size: 11 } },
        beginAtZero: true
      }
    }
  };
  public barChartType: ChartType = 'bar';
  // We'll store chart data per controllerId
  public controllerCharts: { [controllerId: string]: ChartConfiguration['data'] } = {};

  // Call this when you have new data for a controller
  updateControllerChartData(controllerId: string, chartData: RuleExecutionChartData[]) {
    this.controllerCharts[controllerId] = {
      labels: chartData.map(day =>
        new Date(day.ExecutionDate).toLocaleDateString('fr-FR', { weekday: 'short' })
      ),
      datasets: [
        {
          data: chartData.map(day => day.SuccessfulExecutions),
          label: 'Succès',
          backgroundColor: '#10b981',
          borderRadius: 8, // Rounded bars
          barPercentage: 0.6,
          categoryPercentage: 0.7
        },
        {
          data: chartData.map(day => day.FailedExecutions),
          label: 'Échecs',
          backgroundColor: '#dc2626',
          borderRadius: 8,
          barPercentage: 0.6,
          categoryPercentage: 0.7
        }
      ]
    };
  }
}