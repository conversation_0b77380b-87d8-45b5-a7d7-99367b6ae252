import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { Device, DeviceStatus, DeviceType } from '../core/models/device.model';

@Component({
  selector: 'app-device-status-panel',
  standalone: true,
  imports: [CommonModule, MatIconModule],
  template: `
    <div class="device-status-panel">
      <div class="panel-header">
        <h3>{{ title }}</h3>
        <div class="status-summary">
          <div class="status-count" *ngIf="getRunningCount() > 0">
            <mat-icon class="status-icon running">power</mat-icon>
            <span>{{ getRunningCount() }}</span>
          </div>
          <div class="status-count" *ngIf="getPausedCount() > 0">
            <mat-icon class="status-icon paused">pause_circle</mat-icon>
            <span>{{ getPausedCount() }}</span>
          </div>
          <div class="status-count" *ngIf="getStoppedCount() > 0">
            <mat-icon class="status-icon stopped">stop_circle</mat-icon>
            <span>{{ getStoppedCount() }}</span>
          </div>
        </div>
      </div>

      <div class="devices-list">
        <div *ngFor="let device of devices" class="device-item">
          <div class="device-info">
            <div class="device-icon" [ngClass]="getDeviceIconClass(device)">
              <mat-icon>{{ getDeviceIcon(device) }}</mat-icon>
              <span class="status-dot" [ngClass]="getStatusDotClass(device.status)"></span>
            </div>
            <div class="device-details">
              <span class="device-name">{{ device.name }}</span>
              <span class="device-status" [ngClass]="getStatusTextClass(device.status)">
                {{ getStatusText(device.status) }}
              </span>
            </div>
          </div>

     <div class="device-actions">
            <button *ngIf="device.type === DeviceType.CONTROLLER"
                    class="action-btn"
                    [class.active]="pairingMode"
                    (click)="onTogglePermitJoin(device.id)"
                    [title]="getPermitJoinTitle()">
              <mat-icon>{{ pairingMode ? 'link_off' : 'link' }}</mat-icon>
            </button>

            <!-- Only show play/stop for lamps -->
            <ng-container *ngIf="device.type === DeviceType.LAMP">
              <button *ngIf="device.status !== DeviceStatus.RUNNING"
                      class="action-btn start"
                      (click)="onStatusChange(device.id, DeviceStatus.RUNNING)">
                <mat-icon>play_arrow</mat-icon>
              </button>
              <button *ngIf="device.status !== DeviceStatus.STOPPED"
                      class="action-btn stop"
                      (click)="onStatusChange(device.id, DeviceStatus.STOPPED)">
                <mat-icon>stop</mat-icon>
              </button>
            </ng-container>

            <!-- Keep all actions for other device types -->
            <ng-container *ngIf="device.type !== DeviceType.LAMP && device.type !== DeviceType.CONTROLLER">
              <button *ngIf="device.status !== DeviceStatus.RUNNING"
                      class="action-btn start"
                      (click)="onStatusChange(device.id, DeviceStatus.RUNNING)">
                <mat-icon>play_arrow</mat-icon>
              </button>
              <button *ngIf="device.status === DeviceStatus.RUNNING"
                      class="action-btn pause"
                      (click)="onStatusChange(device.id, DeviceStatus.PAUSED)">
                <mat-icon>pause</mat-icon>
              </button>
              <button *ngIf="device.status !== DeviceStatus.STOPPED"
                      class="action-btn stop"
                      (click)="onStatusChange(device.id, DeviceStatus.STOPPED)">
                <mat-icon>stop</mat-icon>
              </button>
            </ng-container>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .device-status-panel {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .panel-header {
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #e2e8f0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .panel-header h3 {
      margin: 0;
      font-size: 1.1rem;
      font-weight: 600;
      color: #2c3e50;
    }

    .status-summary {
      display: flex;
      gap: 1rem;
      align-items: center;
    }

    .status-count {
      display: flex;
      align-items: center;
      gap: 0.25rem;
    }

    .status-icon {
      width: 20px;
      height: 20px;
      font-size: 20px;
    }

    .status-icon.running { color: #2ecc71; }
    .status-icon.paused { color: #f39c12; }
    .status-icon.stopped { color: #e74c3c; }

    .devices-list {
      padding: 1rem;
    }

    .device-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem;
      border-radius: 6px;
      margin-bottom: 0.5rem;
      background: #f8fafc;
      transition: all 0.2s ease;
    }

    .device-item:hover {
      background: #f1f5f9;
      transform: translateY(-1px);
    }

    .device-info {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .device-icon {
      position: relative;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #e2e8f0;
    }

    .device-icon.lamp { background: #fff3e0; }
    .device-icon.climate { background: #e3f2fd; }
    .device-icon.controller { background: #f3e5f5; }

    .device-icon mat-icon {
      font-size: 24px;
    }

    .device-icon.lamp mat-icon { color: #f57c00; }
    .device-icon.climate mat-icon { color: #1976d2; }
    .device-icon.controller mat-icon { color: #7b1fa2; }

    .status-dot {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      border: 2px solid white;
    }

    .status-dot.running { background: #2ecc71; }
    .status-dot.paused { background: #f39c12; }
    .status-dot.stopped { background: #e74c3c; }
    .status-dot.pairing {
      background: #3498db;
      animation: pulse 1.5s infinite;
    }

    @keyframes pulse {
      0% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.2); opacity: 0.5; }
      100% { transform: scale(1); opacity: 1; }
    }

    .device-details {
      display: flex;
      flex-direction: column;
    }

    .device-name {
      font-weight: 500;
      color: #2c3e50;
    }

    .device-status {
      font-size: 0.875rem;
      font-weight: 500;
    }

    .device-status.running { color: #2ecc71; }
    .device-status.paused { color: #f39c12; }
    .device-status.stopped { color: #e74c3c; }
    .device-status.pairing { color: #3498db; }

    .device-actions {
      display: flex;
      gap: 0.5rem;
    }

    .action-btn {
      width: 36px;
      height: 36px;
      border: none;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      background: #e2e8f0;
      color: #64748b;
    }

    .action-btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .action-btn.active,
    .action-btn.start { background: #2ecc71; color: white; }
    .action-btn.pause { background: #f39c12; color: white; }
    .action-btn.stop { background: #e74c3c; color: white; }

    .action-btn mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
      line-height: 20px;
    }
  `]
})
export class DeviceStatusPanelComponent {
  @Input() title: string = '';
  @Input() devices: Device[] = [];
  @Input() pairingMode: boolean = false;
  @Output() deviceStatusChange = new EventEmitter<{ deviceId: string, status: DeviceStatus }>();
  @Output() togglePermitJoin = new EventEmitter<string>();

  readonly DeviceType = DeviceType;
  readonly DeviceStatus = DeviceStatus;

  getDeviceIcon(device: Device): string {
    switch (device.type) {
      case DeviceType.LAMP:
        return 'lightbulb';
      case DeviceType.CLIMATE:
        return 'ac_unit';
      case DeviceType.CONTROLLER:
        return 'router';
      default:
        return 'devices';
    }
  }

  getDeviceIconClass(device: Device): string {
    switch (device.type) {
      case DeviceType.LAMP:
        return 'lamp';
      case DeviceType.CLIMATE:
        return 'climate';
      case DeviceType.CONTROLLER:
        return 'controller';
      default:
        return '';
    }
  }

  getStatusDotClass(status: DeviceStatus): string {
    return status.toLowerCase();
  }

  getStatusTextClass(status: DeviceStatus): string {
    return status.toLowerCase();
  }

  getStatusText(status: DeviceStatus): string {
    switch (status) {
      case DeviceStatus.RUNNING:
        return 'En marche';
      case DeviceStatus.PAUSED:
        return 'En pause';
      case DeviceStatus.STOPPED:
        return 'Arrêté';
      case DeviceStatus.PAIRING:
        return 'En appairage';
      default:
        return 'Inconnu';
    }
  }

  getPermitJoinTitle(): string {
    return this.pairingMode ? "Désactiver l'appairage" : "Activer l'appairage";
  }

  getRunningCount(): number {
    return this.devices.filter(d => d.status === DeviceStatus.RUNNING).length;
  }

  getPausedCount(): number {
    return this.devices.filter(d => d.status === DeviceStatus.PAUSED).length;
  }

  getStoppedCount(): number {
    return this.devices.filter(d => d.status === DeviceStatus.STOPPED).length;
  }

  onStatusChange(deviceId: string, status: DeviceStatus): void {
    this.deviceStatusChange.emit({ deviceId, status });
  }

  onTogglePermitJoin(deviceId: string): void {
    this.togglePermitJoin.emit(deviceId);
  }
}