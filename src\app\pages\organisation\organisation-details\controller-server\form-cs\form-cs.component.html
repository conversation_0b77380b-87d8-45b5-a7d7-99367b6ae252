<div class="create-form-card">
  <div class="form-container">
    <form [formGroup]="createControllerServerForm" (ngSubmit)="onSubmit()">
      <div class="form-grid">
        <div class="form-group">
          <label for="name">Nom <span class="required">*</span></label>
          <input
            id="name"
            type="text"
            formControlName="name"
            [class.error]="hasError('name')"
          />
          <div class="error-message" *ngIf="hasError('name')">
            {{ getErrorMessage("name") }}
          </div>
        </div>
        <div class="form-group">
          <label for="subscriptionId"
            >Abonnement <span class="required">*</span></label
          >

          <select
            id="subscriptionId"
            formControlName="subscriptionId"
            class="form-select"
            [class.error]="hasError('subscriptionId')"
          >
            <option value="">Sélectionnez une abbonement</option>
            <option *ngFor="let sub of subscriptions" [value]="sub.Id">
              {{ sub.Licence.Name }} ({{
                sub.DateDebut | date : "yyyy-MM-dd"
              }}
              ->{{ sub.DateFin | date : "yyyy-MM-dd" }})
            </option>
          </select>

          <div class="error-message" *ngIf="hasError('subscriptionId')">
            {{ getErrorMessage("subscriptionId") }}
          </div>
        </div>
        <div class="form-group">
          <label for="geographicZone">Zone Géographique</label>
          <input
            id="geographicZone"
            formControlName="geographicZone"
            [class.error]="hasError('geographicZone')"
          />
          <div class="error-message" *ngIf="hasError('geographicZone')">
            {{ getErrorMessage("geographicZone") }}
          </div>
        </div>

        <div class="form-group">
          <label for="commercialCondition">Condition Commerciale</label>
          <input
            id="commercialCondition"
            formControlName="commercialCondition"
            [class.error]="hasError('commercialCondition')"
          />
          <div class="error-message" *ngIf="hasError('commercialCondition')">
            {{ getErrorMessage("commercialCondition") }}
          </div>
        </div>
        <div class="form-group">
          <label for="actionType">Type d'action</label>
          <input
            id="actionType"
            formControlName="actionType"
            [class.error]="hasError('actionType')"
          />
          <div class="error-message" *ngIf="hasError('actionType')">
            {{ getErrorMessage("actionType") }}
          </div>
        </div>
        <div class="form-group">
          <label for="eventType">Type d'événement</label>
          <input
            id="eventType"
            formControlName="eventType"
            [class.error]="hasError('eventType')"
          />
          <div class="error-message" *ngIf="hasError('eventType')">
            {{ getErrorMessage("eventType") }}
          </div>
        </div>
        <div class="form-group">
          <label for="triggerType">Type de déclencheur</label>
          <input
            id="triggerType"
            formControlName="triggerType"
            [class.error]="hasError('triggerType')"
          />
          <div class="error-message" *ngIf="hasError('triggerType')">
            {{ getErrorMessage("triggerType") }}
          </div>
        </div>

        <div class="form-group">
          <label for="status">Statut <span class="required">*</span></label>
          <select
            id="status"
            formControlName="status"
            [class.error]="hasError('status')"
            [disabled]="true"
          >
            <option></option>
            <option value="Actif">Actif</option>
            <option value="Inactif">Inactif</option>
            <option value="Maintenance">Maintenance</option>
            <option value="Retiré">Retiré</option>
          </select>
          <div class="error-message" *ngIf="hasError('status')">
            {{ getErrorMessage("status") }}
          </div>
        </div>
      </div>

      <!-- NEW: Controller Servers Multi-Select -->
      <div class="form-group">
        <label for="ControllerServers">Contrôleur</label>
        <div class="multi-select-container">
          <div
            class="selected-items"
            *ngIf="getSelectedControllers().length > 0"
          >
            <div
              class="selected-item"
              *ngFor="let server of getSelectedControllers()"
            >
              <span>{{ server.HostName }}</span>
              <button
                type="button"
                class="remove-btn"
                (click)="removeControllerServer(server.Id)"
              >
                ×
              </button>
            </div>
          </div>
          <select
            id="ControllerServers"
            class="multi-select"
            (change)="onControllerSelect($event)"
            [disabled]="isLoadingControllers"
          >
            <option value="">
              -- Sélectionnez un ou plusieurs Contrôleurs --
            </option>
            <option
              *ngFor="let server of availableControllers"
              [value]="server.Id"
              [disabled]="isControllerServerSelected(server.Id)"
            >
              {{ server.HostName }}
            </option>
          </select>
        </div>
        <div class="info-message" *ngIf="isLoadingControllers">
          Chargement des serveurs...
        </div>
        <div class="error-message" *ngIf="hasError('ControllerServers')">
          {{ getErrorMessage("ControllerServers") }}
        </div>
      </div>

      <div class="form-actions">
        <button
          type="button"
          class="cancel-button"
          (click)="onCancel()"
          [disabled]="isSubmitting"
        >
          Annuler
        </button>
        <button
          type="submit"
          class="submit-button"
          [disabled]="!createControllerServerForm.valid || isSubmitting"
        >
          Enregistrer
        </button>
      </div>
    </form>
  </div>
</div>
