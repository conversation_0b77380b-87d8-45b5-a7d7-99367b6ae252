/* Dialog Styles */
::ng-deep .custom-dialog {
    min-width: 600px !important;
    min-height: max-content !important;
    border-radius: 5px !important;
}

::ng-deep .mdc-dialog__surface {
    border-radius: 15px !important;
}

::ng-deep .cdk-global-overlay-wrapper {
    align-items: center;
    justify-content: center;
}

/* Popup Container */
.pop-up-container {
    padding: 25px;
    max-width: 490px;
    background-color: white;
}

/* Form Header */
.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e2e8f0;
}

.form-title {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 20px;
    color: #2d3748;
}

.title-icon {
    font-size: 26px;
    color: var(--primary);
}

.close-button {
    background: none;
    border-radius: 50% !important;
    border: none !important;
    border-color: transparent !important;
    color: #6b7280;
    cursor: pointer;
    padding: 5px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
}

.close-button:hover {
    background-color: #f3f4f6;
    color: #ef4444;
}

.close-button mat-icon {
    font-size: 20px;
}

/* Tag Assignment Container */
.tag-assignment-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* Header Card Container */
.header-card-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
  animation: slideIn 0.5s ease-out;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: #2E7D32;
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 0 5px 0;
}

.title-icon {
  font-size: 28px !important;
  width: 28px !important;
  height: 28px !important;
  color: #2E7D32;
}

.subtitle {
  color: #666;
  margin: 0;
  font-size: 14px;
}

/* Content Container */
.content-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Form Card Container */
.form-card-container {
  padding: 25px;
}

/* Form Cards */
.form-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e2e8f0;
}

.form-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2E7D32;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Validation Errors */
.validation-errors {
  margin-bottom: 20px;
}

.alert {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 8px;
}

.alert-danger {
  color: #b91c1c;
  background-color: #fef2f2;
  border-color: #fecaca;
}

.animated.fadeIn {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.validation-errors-title {
  font-weight: 600;
  color: #b91c1c;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
}

.validation-errors-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.validation-errors-list li {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 14px;
  color: #b91c1c;
}

/* Form Styles */
.assignment-form {
  margin-bottom: 20px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.required {
  color: #e53e3e;
}

.form-group select {
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.form-group select:focus {
  border-color: #2E7D32;
  box-shadow: 0 0 0 2px rgba(46, 125, 50, 0.2);
  outline: none;
}

.form-group select:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.error-message {
  font-size: 12px;
  color: #e53e3e;
  margin-top: 4px;
}

/* Form Actions */
.form-actions {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.form-actions-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.cancel-button {
  padding: 12px 24px;
  background: transparent;
  border: 1px solid #d1d5db;
  color: #6b7280;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.cancel-button:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
  color: #374151;
}

.submit-button {
  padding: 12px 24px;
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.submit-button:hover {
  background: linear-gradient(45deg, #81c784, var(--primary)) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);
}

.submit-button:disabled {
  background: #d1d5db !important;
  transform: none;
  box-shadow: none;
  cursor: not-allowed;
  opacity: 0.6;
}



/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  background: white;
  padding: 30px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.spinning {
  animation: spin 1s linear infinite;
  font-size: 32px !important;
  color: #2E7D32;
  margin-bottom: 10px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .tag-assignment-container {
    padding: 15px;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  

  
  .form-actions {
    flex-direction: column;
  }
}

/* Selected value display styling */
.tag-assignment-container ::ng-deep .mat-mdc-form-field .mat-mdc-select-value-text {
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.tag-assignment-container ::ng-deep .mat-mdc-form-field .mat-mdc-select-value {
  color: #333;
  font-size: 14px;
  display: block;
  width: 100%;
}

/* Ensure autocomplete selected value shows */
.tag-assignment-container ::ng-deep .mat-mdc-form-field .mat-mdc-input-element {
  color: #333 !important;
  font-size: 14px;
}

/* Selected chip/tag styling for multi-select */
.tag-assignment-container ::ng-deep .mat-mdc-chip {
  background-color: #e8f5e8;
  color: #2E7D32;
  border: 1px solid #81C784;
  font-size: 12px;
  margin: 2px;
}

.tag-assignment-container ::ng-deep .mat-mdc-chip .mat-mdc-chip-remove {
  color: #2E7D32;
  opacity: 0.7;
}

.tag-assignment-container ::ng-deep .mat-mdc-chip .mat-mdc-chip-remove:hover {
  opacity: 1;
}

/* Ensure selected value is visible in the input */
.tag-assignment-container ::ng-deep .mat-mdc-form-field .mat-mdc-input-element {
  color: #333 !important;
  font-size: 14px;
  opacity: 1 !important;
}

/* Style for the autocomplete panel */
.tag-assignment-container ::ng-deep .mat-mdc-autocomplete-panel {
  border-radius: 8px !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
  margin-top: 5px !important;
}

/* Style for the options */
.tag-assignment-container ::ng-deep .mat-mdc-option {
  min-height: 48px !important;
  padding: 0 16px !important;
}

.tag-assignment-container ::ng-deep .mat-mdc-option .option-icon {
  margin-right: 8px;
  color: #2E7D32;
}


/* Enhanced Form Group Styles */
.form-group {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin-left: 4px;
}

.required {
  color: #e53e3e;
  font-weight: 700;
}

/* Enhanced Mat-Form-Field Styles */
.tag-assignment-container ::ng-deep .mat-mdc-form-field {
  width: 100%;
}

.tag-assignment-container ::ng-deep .mat-mdc-text-field-wrapper {
  background-color: #ffffff;
  border-radius: 10px !important;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.tag-assignment-container ::ng-deep .mat-mdc-text-field-wrapper:hover {
  border-color: #a0aec0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.tag-assignment-container ::ng-deep .mat-mdc-text-field-wrapper.mdc-text-field--focused {
  border-color: #2E7D32 !important;
  box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.2);
}

.tag-assignment-container ::ng-deep .mat-mdc-form-field-focus-overlay {
  background-color: transparent !important;
}

.tag-assignment-container ::ng-deep .mat-mdc-form-field-infix {
  min-height: 48px;
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}

.tag-assignment-container ::ng-deep .mat-mdc-input-element {
  color: #2d3748 !important;
  font-size: 14px !important;
  font-weight: 500;
  padding-left: 8px !important;
}

.tag-assignment-container ::ng-deep .mat-mdc-floating-label {
  color: #718096 !important;
  font-size: 14px !important;
  padding-left: 8px !important;
}

.tag-assignment-container ::ng-deep .mat-mdc-form-field-subscript-wrapper {
  display: none;
}

/* Enhanced Select Dropdown Arrow */
.tag-assignment-container ::ng-deep .mat-mdc-select-arrow {
  color: #718096 !important;
}

.tag-assignment-container ::ng-deep .mat-mdc-select-value-text {
  color: #2d3748 !important;
  font-weight: 500 !important;
  padding-left: 8px;
}

/* Enhanced Autocomplete Panel */
.tag-assignment-container ::ng-deep .mat-mdc-autocomplete-panel {
  border-radius: 10px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  margin-top: 8px !important;
  border: 1px solid #e2e8f0;
  max-height: 300px !important;
}

.tag-assignment-container ::ng-deep .mat-mdc-option {
  min-height: 48px !important;
  padding: 0 16px !important;
  transition: all 0.2s ease;
}

.tag-assignment-container ::ng-deep .mat-mdc-option:hover {
  background-color: #f0fff4 !important;
}

.tag-assignment-container ::ng-deep .mat-mdc-option.mdc-list-item--selected {
  background-color: #e8f5e8 !important;
}

.tag-assignment-container ::ng-deep .mat-mdc-option .option-icon {
  margin-right: 12px;
  color: #2E7D32;
  font-size: 20px !important;
  width: 20px !important;
  height: 20px !important;
}

/* Enhanced Placeholder Text */
.tag-assignment-container ::ng-deep .mat-mdc-form-field .mdc-text-field--no-label:not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input, 
.tag-assignment-container ::ng-deep .mat-mdc-form-field .mdc-text-field--no-label:not(.mdc-text-field--outlined):not(.mdc-text-field--textarea) .mat-mdc-select-value {
  color: #718096 !important;
  font-weight: 400;
}

/* Enhanced Error Messages */
.error-message {
  font-size: 12px;
  color: #e53e3e;
  margin-top: 4px;
  padding-left: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Enhanced Select Options */
.tag-assignment-container ::ng-deep .mat-mdc-select-panel {
  border-radius: 10px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid #e2e8f0;
}

.tag-assignment-container ::ng-deep .mat-mdc-option.mat-mdc-option-active {
  background-color: #f0fff4 !important;
}

/* Focus State Enhancements */
.tag-assignment-container ::ng-deep .mdc-text-field--focused .mdc-floating-label {
  color: #2E7D32 !important;
}

/* Disabled State */
.tag-assignment-container ::ng-deep .mat-mdc-form-field-disabled .mat-mdc-text-field-wrapper {
  background-color: #f8fafc !important;
  border-color: #e2e8f0 !important;
}

/* Chip Styles for Multi-select (if needed) */
.tag-assignment-container ::ng-deep .mat-mdc-chip {
  background-color: #e8f5e8 !important;
  color: #2E7D32 !important;
  border-radius: 8px !important;
  padding: 4px 12px !important;
  font-weight: 500 !important;
  border: 1px solid #81C784 !important;
}

.tag-assignment-container ::ng-deep .mat-mdc-chip-remove {
  color: #2E7D32 !important;
  opacity: 0.7;
}

.tag-assignment-container ::ng-deep .mat-mdc-chip-remove:hover {
  opacity: 1 !important;
}

/* Animation for form elements */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

.form-group {
  animation: fadeIn 0.3s ease-out;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .tag-assignment-container ::ng-deep .mat-mdc-form-field-infix {
    min-height: 44px;
  }
  
  .tag-assignment-container ::ng-deep .mat-mdc-input-element {
    font-size: 13px !important;
  }
  
  .form-group label {
    font-size: 13px;
  }
}
