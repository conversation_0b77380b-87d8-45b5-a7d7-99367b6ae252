.search-section {
  margin: 20px 0;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-card-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
  animation: slideIn 0.5s ease-out;
}

/* Keep your existing header-section styles, but remove any conflicting styles */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* Remove any margin/padding/background that might conflict with the new container */
}

.search-container {
  display: flex;
  gap: 10px;
  align-items: center;
  max-width: 500px;
}

.search-input {
  flex: 1;
  max-width: 450px;
  padding: 12px 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #f8fafc;
  color: #4a5568;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.search-input::placeholder {
  color: #a0aec0;
}

.search-button {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.search-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
  background: linear-gradient(45deg, #81C784, var(--primary));
}

.search-button mat-icon {
  font-size: 18px;
  height: 18px;
  width: 18px;
}

.loading-text { font-size: 20px; font-weight: 500; letter-spacing: 0.5px; }

.dots { animation: dotAnimation 1.4s infinite; display: inline-block; }

@keyframes dotAnimation { 0% { opacity: 0; } 50% { opacity: 1; } 100% { opacity: 0; } }

/* Keep all existing styles and add these new ones at the end */

/* Popup Form Styles */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.popup-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideIn 0.3s ease-out;
  margin-top: 60px; /* Add space from the top/header */

}


.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.popup-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #2E7D32;
  display: flex;
  align-items: center;
  gap: 10px;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}

.close-btn:hover {
  background: #f0f0f0;
}

.close-btn mat-icon {
  color: #666;
  font-size: 24px;
}

.site-form {
  padding: 25px;
}

.site-form .form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.site-form .form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.site-form .form-group.full-width {
  grid-column: 1 / -1;
}

.site-form label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.form-group input[readonly] {
  cursor: not-allowed;
  background-color: #f5f5f5;
}

.required {
  color: #e53e3e;
}

.site-form input,
.site-form select,
.site-form textarea {
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.site-form input:focus,
.site-form select:focus,
.site-form textarea:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  outline: none;
}

.error-message {
  font-size: 12px;
  color: #e53e3e;
  margin-top: 4px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.btn-cancel {
  padding: 10px 20px;
  background: transparent;
  border: 1px solid #cbd5e0;
  color: #718096;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.btn-cancel:hover {
  background-color: #f7fafc;
}

.btn-submit {
  padding: 10px 20px;
  background: linear-gradient(45deg, var(--primary), #81C784);
  color: white;
  border: none;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.btn-submit:hover {
  background: linear-gradient(45deg, #81C784, var(--primary));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.btn-submit:disabled {
  background: #cbd5e0;
  transform: none;
  box-shadow: none;
  cursor: not-allowed;
}

/* Validation Errors */
.validation-errors {
  background-color: #fee2e2;
  border-left: 4px solid #ef4444;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 4px;
}

.validation-errors-title {
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.validation-errors-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.validation-errors-list li {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 14px;
  color: #b91c1c;
}

.validation-errors-list li:last-child {
  margin-bottom: 0;
}

/* Current Images Display */
.current-images-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
  margin-top: 8px;
}

.current-image-item {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.current-image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-image-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.remove-image-btn:hover {
  background: white;
  transform: scale(1.1);
}

.remove-image-btn i {
  font-size: 18px;
  color: #ef5350;
}

/* No Items Message */
.no-sites-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  text-align: center;
  color: #718096;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.no-sites-message .material-icons {
  font-size: 48px;
  color: #cbd5e0;
  margin-bottom: 15px;
}

.no-sites-message p {
  margin: 0 0 20px;
  font-size: 16px;
}

.btn-primary {
  padding: 10px 20px;
  background: linear-gradient(45deg, var(--primary), #81C784);
  color: white;
  border: none;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary:hover {
  background: linear-gradient(45deg, #81C784, var(--primary));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}


.clear-button {
  background: #f44336;
  color: white;
  box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
}

.clear-button:hover {
  background: #d32f2f;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
}

/* Responsive popup */
@media (max-width: 768px) {
  .popup-form {
    width: 95%;
    max-height: 95vh;
  }
  
  .popup-header {
    padding: 15px 20px;
  }
  
  .site-form {
    padding: 20px;
  }
  
  .site-form .form-grid {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn-cancel,
  .btn-submit {
    width: 100%;
    justify-content: center;
  }
}

/* Header Actions */
.actions {
  display: flex;
  gap: 15px;
}

.create-button,
.view-toggle {
  background: linear-gradient(45deg, var(--primary), #81C784);
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.create-button:hover,
.view-toggle:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(76, 175, 80, 0.5);
  background: linear-gradient(45deg, #81C784, var(--primary));
}

.view-toggle {
  background: white;
  color: #4a5568;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.view-toggle:hover {
  background: #f8fafc;
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

/* Responsive design for buttons */
@media (max-width: 768px) {
  .actions {
    width: 100%;
    flex-direction: column;
    gap: 10px;
  }

  .create-button,
  .view-toggle {
    width: 100%;
    justify-content: center;
    padding: 12px;
  }
}


.local-management-container {
  max-width: 98%;
  margin-left: 50px; /* Add this line to push content right of the sidebar */
  padding: 24px 24px 24px 0; /* Optional: add some padding for top/right/bottom */
  box-sizing: border-box;
}

.page-title {
  margin: 0;
}

.title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.title-icon {
  font-size: 30px;
  color: var(--primary);
  background: linear-gradient(45deg, var(--primary), #81C784);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Horizontal card layout for local cards */
.cards-container {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  justify-content: flex-start;
  align-items: stretch;
  margin-bottom: 32px;
}
/* Site Info Section Styles */
.site-info-section {
  margin-bottom: 30px;
}

.breadcrumb-nav {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
}

.back-button {
  background: #f5f5f5;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: #e0e0e0;
  transform: translateX(-3px);
}

.back-button .material-icons {
  color: #555;
  font-size: 24px;
}

.breadcrumb-text {
  font-size: 16px;
  color: #666;
}

.info-section {
  display: flex;
  margin-bottom: 30px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 25px;
  animation: fadeIn 0.5s ease-out;
}

.site-images-container {
  flex: 0 0 300px;
  margin-right: 30px;
}

.logo-container {
  width: 300px;
  height: 280px;
  border-radius: 8px;
  overflow: hidden;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.site-logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #f0f8f0;
}

.no-logo .material-icons {
  font-size: 80px;
  color: #c5c5c5;
}

.site-info-container {
  flex: 1;
}

.site-name {
  margin: 0 0 10px;
  font-size: 20px;
  font-weight: 700;
  color: #2E7D32;
  line-height: 1.2;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.info-label .material-icons {
  font-size: 20px;
  color: var(--primary);
}

.info-value {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}
.info-value div{
  margin-bottom: 5px;
}
/* Responsive adjustments */
@media (max-width: 768px) {
  .info-section {
    flex-direction: column;
  }
  
  .site-images-container {
    margin-right: 0;
    margin-bottom: 20px;
    align-self: center;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}

/* Enhanced Site Info Styles */
.site-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.site-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.site-status.active {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.site-status.inactive {
  background-color: #ffebee;
  color: #c62828;
}

.site-status.maintenance {
  background-color: #fff8e1;
  color: #f57f17;
}

.site-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 15px;
  padding: 15px 15px 15px 0px;
}

.stat-item {
  text-align: center;
  padding: 10px;
  margin-right: 25px;
  border-radius: 8px;

}

.stat-value {
  font-size: 16px;
  font-weight: 700;
  color: #2E7D32;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 4px;
}

.info-column {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.description-text {
  line-height: 1.5;
  white-space: pre-line;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .info-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .site-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .site-stats {
    flex-direction: column;
    gap: 10px;
  }
  
  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e0e0e0;
  }
  
  .stat-item:last-child {
    border-bottom: none;
  }
  
  .stat-value, .stat-label {
    font-size: 16px;
  }
}

.file-input-container {
  margin-top: 8px;
}

.file-input-label {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  font-size: 14px;
  border: 1px dashed #ccc;
  border-radius: 6px;
  color: #333;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.file-input-label mat-icon {
  margin-right: 6px;
  font-size: 18px;
}

.file-input-hidden {
  display: none;
}

.file-info {
  margin-top: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-style: italic;
}

.image-thumbnail {
  width: 150px;
  height: 150px;
  object-fit: cover;
}

.no-file {
  font-size: 13px;
  color: #999;
}