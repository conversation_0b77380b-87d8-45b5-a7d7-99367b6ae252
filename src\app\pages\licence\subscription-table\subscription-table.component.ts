import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { Client } from '@app/core/models/client';
import { LicenceApiService } from '@app/core/services/administrative/licence.service';
import { Licence } from '@app/core/models/licence';
import { Subscription } from '@app/core/models/subscription';
import { SubscriptionApiService } from '@app/core/services/administrative/subscription.service';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';
import { NgxLoadingModule } from 'ngx-loading';
import { OptionApiService } from '@app/core/services/administrative/option.service';
import { Option } from '@app/core/models/option';
import { Router } from '@angular/router';
import { animate, state, style, transition, trigger } from '@angular/animations';

@Component({
  selector: 'app-subscription-table',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    GenericTableComponent,
    NgxLoadingModule
  ],
  templateUrl: './subscription-table.component.html',
  styleUrls: ['./subscription-table.component.css'],
  animations: [
    trigger('fadeInOut', [
      state('void', style({
        opacity: 0,
        transform: 'scale(0.9)'
      })),
      state('*', style({
        opacity: 1,
        transform: 'scale(1)'
      })),
      transition('void <=> *', animate('300ms ease-in-out'))
    ]),
    trigger('slideDown', [
      state('void', style({
        transform: 'translateY(-20px)',
        opacity: 0
      })),
      state('*', style({
        transform: 'translateY(0)',
        opacity: 1
      })),
      transition('void <=> *', animate('400ms ease-out'))
    ])
  ]
})
export class SubscriptionTableComponent implements OnInit {
  constructor(
    private clientApiService: ClientApiService,
    private licenceApiService: LicenceApiService,
    private subscriptionApiService: SubscriptionApiService,
    private optionApiService: OptionApiService,
    private router: Router
  ) {}

  searchQuery = '';
  tablePageSize = 5;
  tableCurrentPage = 0;
  showCannotModifyResiliePopup = false;
  isLoading = false;
  showCannotCancelPendingPopup = false;
  filteredTableRows: any[] = [];
  subscriptionTableRows: any[] = [];
  selectedStatusFilter: string = '';
  selectedLicenceFilter: string = '';
  selectedFrequencyFilter: string = '';
  showCancelPopup = false;
  licenceToCancel: Licence | null = null;
  showCancelNotification = false;
  clients: Client[] = [];
  filteredClients: Client[] = [];
  licences: Licence[] = [];
  subscriptions: Subscription[] = [];
  selectedClient: Client | null = null;
  // Popup data
  modifyingLicence: Licence | null = null;
  oldLicence: Licence | null = null;
  selectedLicenseForConfirmation: Licence | null = null;
  licenceWithUnsavedChanges: Licence | null = null;
  oldOptions: Option[] = [];
  
  // New popup for subscription selection
  showSubscriptionSelectionPopup = false;
  showConfirmSubscriptionPopup = false;
  subscriptionSearchQuery = '';
  selectedStatusFilterPopup = '';
  filteredSubscriptionsForPopup: any[] = [];
  selectedSubscriptionForModification: any = null;

  // Pagination state
  pageSize = 3;
  currentPage = 0;
  get totalPages() {
    return Math.ceil(this.licences.length / this.pageSize);
  }
  get pagedLicences() {
    const start = this.currentPage * this.pageSize;
    return this.licences.slice(start, start + this.pageSize);
  }
  cardAnimDirection: 'next' | 'prev' = 'next';
  viewMode: 'add' | 'modify' = 'add';
  private swipeStartX: number | null = null;
  dropdownOpenRow: any = null;
  cardMode: 'add' | 'modify' = 'add';
  
  // Only show rows with Status 'Payé' or 'En attente' for pagination and display
  getActiveTableRows(): any[] {
    // Return all rows, including "Resilié"
    return this.filteredTableRows;
  }

  get tableTotalPages(): number {
    return Math.ceil(this.getActiveTableRows().length / this.tablePageSize);
  }

  get pagedTableRows(): any[] {
    const activeRows = this.getActiveTableRows();
    const start = this.tableCurrentPage * this.tablePageSize;
    return activeRows.slice(start, start + this.tablePageSize);
  }

  get tableFirstItem(): number {
    const activeRows = this.getActiveTableRows();
    if (activeRows.length === 0) return 0;
    return this.tableCurrentPage * this.tablePageSize + 1;
  }

  get tableLastItem(): number {
    const activeRows = this.getActiveTableRows();
    return Math.min((this.tableCurrentPage + 1) * this.tablePageSize, activeRows.length);
  }

  onTablePageSizeChange(): void {
    this.tableCurrentPage = 0;
    // No assignment needed; pagedTableRows is a getter and updates automatically
  }

  prevTablePage(): void {
    if (this.tableCurrentPage > 0) {
      this.tableCurrentPage--;
    }
  }

  nextTablePage(): void {
    if (this.tableCurrentPage < this.tableTotalPages - 1) {
      this.tableCurrentPage++;
    }
  }

  goToTablePage(page: number): void {
    this.tableCurrentPage = page;
  }

  updateSubscriptionTableRows(): void {
    this.subscriptionTableRows = this.subscriptions.map(sub => {
      const client = this.clients.find(c => c.Id === sub.ClientId);
      const licence = this.licences.find(l => l.Id === sub.LicenceId);
      return {
        ClientName: client ? client.Name : '',
        LicenceName: licence ? licence.Name : '',
        DateDebut: this.formatDate(sub.DateDebut),
        DateFin: this.formatDate(sub.DateFin),
        Status: sub.Status,
        price: sub.Price,
        PaymentFrequency: sub.PaymentFrequency,
        ClientId: sub.ClientId,
        LicenceId: sub.LicenceId,
        SubscriptionId: sub.Id,
        // Add the full subscription object for easier access
        subscription: sub,
        showActions: !(sub.Status && sub.Status.toLowerCase() === 'resilié')
      };
    });
    this.filteredTableRows = [...this.subscriptionTableRows];
    // Update active rows after filtering
    this.getActiveTableRows();
  }

  getActionsForStatus(status: string): string[] {
    if (status && status.toLowerCase() === 'resilié') {
      return [];
    }
    return ['Modifier', 'Annuler'];
  }
  // Add statistics methods for template
  getTotalSubscriptions(): number {
    return this.subscriptions.length;
  }
  getPendingSubscriptions(): number {
    return this.subscriptions.filter(sub => sub.Status && sub.Status.toLowerCase() === 'en attente').length;
  }
  getPaidSubscriptions(): number {
    return this.subscriptions.filter(sub => sub.Status && sub.Status.toLowerCase() === 'payé').length;
  }
  getCancelledSubscriptions(): number {
    return this.subscriptions.filter(sub => sub.Status && sub.Status.toLowerCase() === 'resilié').length;
  }

  // Add clearFilters method for template
  clearFilters(): void {
    this.selectedStatusFilter = '';
    this.selectedLicenceFilter = '';
    this.selectedFrequencyFilter = '';
    this.filterClientsTable();
  }

  // Update filterClientsTable to apply filters
  filterClientsTable(): void {
    let rows = [...this.subscriptionTableRows];

    // Text search
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase().trim();
      rows = rows.filter(row =>
        (row.ClientName || '').toLowerCase().includes(query) ||
        (row.LicenceName || '').toLowerCase().includes(query) ||
        (row.Status || '').toLowerCase().includes(query)
      );
    }

    // Status filter
    if (this.selectedStatusFilter) {
      rows = rows.filter(row => row.Status && row.Status.toLowerCase() === this.selectedStatusFilter.toLowerCase());
    }

    // Licence filter
    if (this.selectedLicenceFilter) {
      rows = rows.filter(row => row.LicenceName === this.selectedLicenceFilter);
    }

    // Frequency filter
    if (this.selectedFrequencyFilter) {
      rows = rows.filter(row => row.PaymentFrequency === this.selectedFrequencyFilter);
    }

    this.filteredTableRows = rows;
    this.tableCurrentPage = 0;
  }

  fetchSubscriptions(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.subscriptionApiService.getAll().subscribe({
        next: async (data: Subscription[]) => {
          this.subscriptions = data;
          // Check for subscriptions where DateFin is finished and status is "Payé"
          const today = new Date();
          for (const sub of this.subscriptions) {
            if (
              sub.Status &&
              sub.Status.toLowerCase() === 'payé' &&
              sub.DateFin &&
              new Date(sub.DateFin) < today
            ) {
              // Update status to "En attente"
              const updatedSub = { ...sub, Status: 'En attente' };
              await this.subscriptionApiService.update(updatedSub).toPromise();
              sub.Status = 'En attente'; // Update local state immediately
            }
          }
          this.updateSubscriptionTableRows();
          resolve();
        },
        error: (error) => {
          console.error('Error fetching subscriptions:', error);
          reject(error);
        }
      });
    });
  }

  fetchClients(): Promise<void> {
    return new Promise((resolve) => {
      this.clientApiService.getAll().subscribe({
        next: (data: Client[]) => {
          this.clients = data.map(clients => ({
            ...clients,
            Name: clients.Name,
            ClientLogo: clients.ClientLogo
          }));
          this.filteredClients = [...this.clients];
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  fetchLicences(): Promise<void> {
    return new Promise((resolve) => {
      this.licenceApiService.getAll().subscribe({
        next: (data: Licence[]) => {
          this.licences = data.map(licence => ({
            ...licence,
            name: licence.Name,
            description: licence.Description
          }));
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  // Table action handler - FIXED VERSION
  onTableAction(event: { action: string; row: any }) {
    console.log('Table action triggered:', event);

    if (event.row && event.row.Status && event.row.Status.toLowerCase() === 'resilié') {
      console.log('Action blocked: Subscription is cancelled (Resilié)');
      return; // Don't process any actions for cancelled subscriptions
    }

    if (event.action === 'Modifier') {
      if (event.row && event.row.SubscriptionId) {
        console.log('Navigating to modify subscription:', event.row.SubscriptionId);
        // Navigate to the edit page with the subscription ID
        this.router.navigate(['/abonnement-modifie', event.row.SubscriptionId]);
      } else {
        console.error('No SubscriptionId found in row:', event.row);
      }
      return;
    }

    if (event.action === 'Annuler') {
      const subscription = this.subscriptions.find(sub => sub.Id === event.row.SubscriptionId);

      if (!subscription) {
        console.error('No subscription found for ID:', event.row.SubscriptionId);
        return;
      }

      if (typeof subscription.Status === 'string' && subscription.Status.trim().toLowerCase() === 'en attente') {
        this.showCannotCancelPendingPopup = true;
        return;
      }

      if (typeof subscription.Status === 'string' && subscription.Status.trim().toLowerCase() === 'resilié') {
        return;
      }

      const updatedSubscription = {
        Id: subscription.Id,
        ClientId: subscription.ClientId,
        LicenceId: subscription.LicenceId,
        DateDebut: subscription.DateDebut,
        DateFin: subscription.DateFin,
        Price: subscription.Price,
        PaymentFrequency: subscription.PaymentFrequency,
        Status: 'Resilié'
      };

      this.subscriptionApiService.update(updatedSubscription).subscribe({
        next: () => {
          this.showCancelNotification = true;
          setTimeout(() => this.showCancelNotification = false, 3000);
          Promise.all([
            this.fetchSubscriptions(),
            this.fetchClients()
          ]).then(() => {
            const localSubIndex = this.subscriptions.findIndex(s => s.Id === subscription.Id);
            if (localSubIndex !== -1) {
              this.subscriptions[localSubIndex].Status = 'Resilié';
            }
            this.updateSubscriptionTableRows();
          }).catch(error => {
            console.error('Error refreshing data after cancellation:', error);
          });
        },
        error: (error) => {
          console.error('Error cancelling subscription:', error);
        }
      });
      return;
    }
  }

  // Helper for formatting date
  formatDate(date: Date | string | null): string {
    if (!date) return '';
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    return `${day}/${month}/${year}`;
  }

  // Popup close handlers
  closeCannotCancelPendingPopup() {
    this.showCannotCancelPendingPopup = false;
  }

  goToAbonementNew() {
    this.router.navigate(['/abonement-new']);
  }

  // New methods for subscription selection popup
  openSubscriptionSelectionPopup() {
    this.showSubscriptionSelectionPopup = true;
    this.subscriptionSearchQuery = '';
    this.selectedStatusFilterPopup = '';
    this.filterSubscriptionsForPopup();
  }

  closeSubscriptionSelectionPopup() {
    this.showSubscriptionSelectionPopup = false;
    this.subscriptionSearchQuery = '';
    this.selectedStatusFilterPopup = '';
    this.filteredSubscriptionsForPopup = [];
  }

  filterSubscriptionsForPopup() {
    // Start with all subscriptions except "Resilié"
    let subscriptions = this.subscriptionTableRows.filter(row => 
      row.Status && row.Status.toLowerCase() !== 'resilié'
    );

    // Apply search filter
    if (this.subscriptionSearchQuery.trim()) {
      const query = this.subscriptionSearchQuery.toLowerCase().trim();
      subscriptions = subscriptions.filter(row =>
        (row.ClientName || '').toLowerCase().includes(query) ||
        (row.LicenceName || '').toLowerCase().includes(query)
      );
    }

    // Apply status filter
    if (this.selectedStatusFilterPopup) {
      subscriptions = subscriptions.filter(row => 
        row.Status && row.Status.toLowerCase() === this.selectedStatusFilterPopup.toLowerCase()
      );
    }

    this.filteredSubscriptionsForPopup = subscriptions;
  }

  selectSubscriptionForModification(subscription: any) {
    this.selectedSubscriptionForModification = subscription;
    this.showSubscriptionSelectionPopup = false;
    this.showConfirmSubscriptionPopup = true;
  }

  closeConfirmSubscriptionPopup() {
    this.showConfirmSubscriptionPopup = false;
    this.selectedSubscriptionForModification = null;
  }

  confirmModifySelectedSubscription() {
    if (this.selectedSubscriptionForModification && this.selectedSubscriptionForModification.SubscriptionId) {
      console.log('Navigating to modify subscription:', this.selectedSubscriptionForModification.SubscriptionId);
      this.router.navigate(['/abonnement-modifie', this.selectedSubscriptionForModification.SubscriptionId]);
      this.closeConfirmSubscriptionPopup();
    }
  }

  closeCannotModifyResiliePopup() {
  this.showCannotModifyResiliePopup = false;
}

  ngOnInit(): void {
    this.isLoading = true;
    Promise.all([
      this.fetchClients(),
      this.fetchLicences(),
      this.fetchSubscriptions()
    ]).then(() => {
      this.updateSubscriptionTableRows();
      this.isLoading = false;
    }).catch(() => {
      this.isLoading = false;
    });
  }
}