import { Component, Inject, OnInit } from '@angular/core';
import { <PERSON><PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';

interface SensorImages {
  images: string[];
}

@Component({
  selector: 'app-edit-sensor-dialog',
  templateUrl: './edit-sensor-dialog.component.html',
  styleUrls: ['./edit-sensor-dialog.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule
  ]
})
export class EditSensorDialogComponent implements OnInit {
  form: FormGroup;
  selectedImage: string = '';
  images: string[] = [];
  sensorTypes = ['Temperature', 'Motion', 'Vibration', 'Flood', 'Button'];

  constructor(
    private fb: FormBuilder,
    private http: HttpClient,
    public dialogRef: MatDialogRef<EditSensorDialogComponent>,
    private dialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.selectedImage = data.imageURL;
    this.form = this.fb.group({
      id: [data.id],
      name: [data.name, [Validators.required, Validators.minLength(3)]],
      description: [data.description],
      type: [data.type, Validators.required],
      imageURL: [data.imageURL, Validators.required],
      measurementRange: [data.measurementRange],
      fabricant: [data.fabricant]
    });
  }

  ngOnInit(): void {
    this.loadImages();
  }

  loadImages(): void {
    this.getImages().subscribe({
      next: (data: SensorImages) => {
        this.images = data.images;
        if (!this.images.includes(this.selectedImage) && this.images.length > 0) {
          this.selectedImage = this.images[0];
          this.form.patchValue({ imageURL: this.selectedImage });
        }
      },
      error: (err) => {
        console.error('Error loading sensor images', err);
      }
    });
  }

  getImages(): Observable<SensorImages> {
    return this.http.get<SensorImages>('assets/sensor-images.json');
  }

  chooseImage(image: string) {
    this.selectedImage = image;
    this.form.patchValue({ imageURL: image });
  }

  save() {
    if (this.form.valid) {
      this.dialogRef.close(this.form.value);
    }
  }

  cancel() {
    this.dialogRef.close();
  }
}