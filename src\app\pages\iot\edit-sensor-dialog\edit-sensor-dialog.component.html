<div class="dialog-container">
  <h2 class="dialog-title">Modifier le Capteur</h2>
  <form [formGroup]="form" (ngSubmit)="save()" class="sensor-form">
    <div class="form-scrollable">
      <!-- First row - Name and Type side by side -->
      <div class="form-row pair">
        <mat-form-field appearance="outline" class="form-field name-field">
          <mat-label>Nom du Capteur</mat-label>
          <input matInput formControlName="name" placeholder="Ex: Capteur de Température">
          <mat-error *ngIf="form.get('name')?.hasError('required')">Le nom est requis</mat-error>
          <mat-error *ngIf="form.get('name')?.hasError('minlength')">Minimum 3 caractères</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="form-field type-field">
          <mat-label>Type de Capteur</mat-label>
          <mat-select formControlName="type">
            <mat-option *ngFor="let type of sensorTypes" [value]="type">{{ type }}</mat-option>
          </mat-select>
          <mat-error *ngIf="form.get('type')?.hasError('required')">Le type est requis</mat-error>
        </mat-form-field>
      </div>

      <!-- Description - Full width -->
      <mat-form-field appearance="outline" class="form-field description-field">
        <mat-label>Description</mat-label>
        <textarea matInput formControlName="description" placeholder="Description du capteur" rows="3"></textarea>
      </mat-form-field>

      <!-- Second row - Manufacturer and Range side by side -->
      <div class="form-row pair">
        <mat-form-field appearance="outline" class="form-field manufacturer-field">
          <mat-label>Fabricant</mat-label>
          <input matInput formControlName="fabricant" placeholder="Ex: Aqara">
        </mat-form-field>

        <mat-form-field appearance="outline" class="form-field range-field">
          <mat-label>Plage de Mesure</mat-label>
          <input matInput formControlName="measurementRange" placeholder="Ex: 0-100°C">
        </mat-form-field>
      </div>

      <!-- Image section -->
      <div class="image-section">
        <h3 class="section-title">Image du Capteur</h3>
        
        <div class="upload-container" *ngIf="false"> <!-- Hidden upload in edit mode -->
          <button mat-stroked-button type="button" class="upload-button" disabled>
            <mat-icon>cloud_upload</mat-icon>
            <span>Télécharger une image</span>
          </button>
        </div>

        <div class="current-image" *ngIf="selectedImage">
          <p class="preview-label">Image actuelle :</p>
          <img [src]="selectedImage" alt="Current Sensor Image" class="preview-image">
        </div>

        <div class="image-gallery" *ngIf="images.length > 0">
          <h4 class="gallery-title">Changer l'image :</h4>
          <div class="gallery-grid">
            <div *ngFor="let image of images" class="gallery-item" 
                [class.selected]="selectedImage === image" 
                (click)="chooseImage(image)">
              <img [src]="image" alt="Sensor Image" class="gallery-image">
            </div>
          </div>
        </div>

        <div class="no-images" *ngIf="images.length === 0">
          <mat-icon class="no-images-icon">image_not_supported</mat-icon>
          <p>Aucune image disponible</p>
        </div>
        
        <mat-error *ngIf="form.get('imageURL')?.hasError('required') && form.get('imageURL')?.touched" class="image-error">
          Veuillez sélectionner une image
        </mat-error>
      </div>
    </div>

    <div class="dialog-actions">
      <button mat-stroked-button type="button" (click)="cancel()" class="action-button cancel">
        Annuler
      </button>
      <button mat-raised-button type="submit" [disabled]="form.invalid" class="action-button submit">
        <mat-icon>save</mat-icon>
        Enregistrer
      </button>
    </div>
  </form>
</div>