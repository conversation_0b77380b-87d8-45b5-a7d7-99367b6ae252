.stats-container {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
  width: 100%;
  box-sizing: border-box;
}

.stats-header {
  margin-bottom: 20px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 20px;
  letter-spacing: 0.5px;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
  width: 100%;
}

/* Update media queries for better responsiveness */
@media screen and (min-width: 1800px) {
  .category-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

@media screen and (min-width: 2100px) {
  .category-grid {
    grid-template-columns: repeat(7, 1fr);
  }
}

@media screen and (max-width: 1600px) {
  .category-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

@media screen and (max-width: 1400px) {
  .category-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media screen and (max-width: 1100px) {
  .category-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media screen and (max-width: 768px) {
  .category-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (max-width: 480px) {
  .category-grid {
    grid-template-columns: repeat(1, 1fr);
  }
}