// navigation.service.ts
import { Injectable } from '@angular/core';
import { Router, NavigationEnd, NavigationStart } from '@angular/router';
import { filter } from 'rxjs/operators';

@Injectable({ providedIn: 'root' })
export class NavigationService {
  private history: { url: string, position: number }[] = [];
  private currentUrl: string = '';
  private currentScrollPosition: number = 0;

  constructor(private readonly router: Router) {
    // Track navigation start to capture scroll position before leaving
    this.router.events
      .pipe(filter((event) => event instanceof NavigationStart))
      .subscribe(() => {
        if (this.currentUrl) {
          // Update or add the current page's scroll position
          const existingIndex = this.history.findIndex(h => h.url === this.currentUrl);
          const scrollPosition = window.scrollY;

          if (existingIndex >= 0) {
            this.history[existingIndex].position = scrollPosition;
          } else {
            this.history.push({
              url: this.currentUrl,
              position: scrollPosition
            });
          }
        }
      });

    // Track navigation end to update current URL
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        this.currentUrl = event.urlAfterRedirects;
      });

    // Listen for scroll events to continuously update current position
    window.addEventListener('scroll', () => {
      this.currentScrollPosition = window.scrollY;
    });
  }

  public getPrevious(): { url: string, position: number } | null {
    if (this.history.length === 0) return null;

    // Find the most recent entry that's not the current URL
    for (let i = this.history.length - 1; i >= 0; i--) {
      if (this.history[i].url !== this.currentUrl) {
        return this.history[i];
      }
    }

    return null;
  }

  public getCurrentScrollPosition(): number {
    return this.currentScrollPosition;
  }

  public clearHistory(): void {
    this.history = [];
  }
}
