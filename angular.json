{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"energy-monitoring-app": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/energy-monitoring-app", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "public"}], "styles": ["@ngxpert/hot-toast/styles.css", "@angular/material/prebuilt-themes/azure-blue.css", "src/styles.css", "node_modules/@fortawesome/fontawesome-free/css/all.min.css", "node_modules/leaflet/dist/leaflet.css"], "stylePreprocessorOptions": {"includePaths": ["src/app/styles"]}, "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "10MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "80kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"buildTarget": "energy-monitoring-app:build", "proxyConfig": "proxy.conf.json"}, "configurations": {"production": {"buildTarget": "energy-monitoring-app:build:production"}, "development": {"buildTarget": "energy-monitoring-app:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["@ngxpert/hot-toast/styles.css", "@angular/material/prebuilt-themes/azure-blue.css", "src/styles.css"], "scripts": []}}}}}, "cli": {"analytics": false}}