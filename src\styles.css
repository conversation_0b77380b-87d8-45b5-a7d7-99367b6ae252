/* Global styles and variables */
:root {
  --black: #000000;
  --white: #ffffff;
  --grey-dark: #383838;
  --grey-light: #C2C2C2;
  --beige-darl: #E9E9E9;
  --beige-light: #F6F6F6;
  --green-light: #D5E4CF;
  --green-dark: #3F6433;
  --maintenance: #f59e0b;
  --planification: #65ad68a4;
  --audit: #0277bd;
  --installation: #2F7D33;
  --box-shadow: rgba(12, 40, 21, 0.1);
  --green-main: #2F7D33;
  --primary: var(--green-main);
  --primary-light: #67c987;
  --primary-lighter: rgb(133, 211, 159);
  --primary-dark: rgb(72, 140, 94);
  --secondary: #64748b;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --background: #f8fafc;
  --surface: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --sidebar-background: #ffffff;
  --header-background: #ffffff;
  --text-color: #333333;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --highlight-color: rgba(46, 125, 50, 0.1);
  --active-item-bg: rgba(46, 125, 50, 0.15);
  --hover-bg-color: rgba(103, 201, 135, 0.1);
  
  /* Project cards specific variables */
  --container-bg: #ffffff;
  --sidebar-title: #262626;
  --card-bg: #ffffff;
  --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  --card-title: black;
  --card-description: #424242;
  --card-detail-text: #616161;
  --card-detail-icon: #757575;
  --card-detail-label: #424242;
  --card-actions-bg: #f5f7fa;
  --card-border: #eeeeee;
  --progress-bg: #f5f7fa;
}

.notification-leave {
  animation: fadeOut 0.3s ease-in forwards;
}


@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

/* Base styles */
html, body {
  height: 100%;
  margin: 0;
  font-family: 'Montserrat', 'Lato', sans-serif;
  color: var(--text-primary);
  line-height: 1.5;
}

/* Card styles */
.mat-card {
  background: var(--surface);
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.mat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Button styles */
.mat-raised-button {
  border-radius: 8px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.2s ease;
}

.mat-raised-button.mat-primary {
  background-color: var(--primary);
}

.mat-raised-button.mat-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Form field styles */
.mat-form-field {
  width: 100%;
}

.mat-form-field-appearance-outline .mat-form-field-outline {
  border-radius: 8px;
}


.success-snackbar {
  background-color: var(--primary) !important; 
  color: white !important;
}

.error-snackbar {
  background-color: #f44336 !important; 
  color: white !important;
}

.notification-above-header {
  z-index: 9999 !important;
}

.cdk-overlay-container {
  z-index: 9999 !important;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes cardEntrance {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Standard card container */
.card-container {
  max-width: 1200px;
  margin: 20px auto;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  animation: cardEntrance 0.5s ease-out;
}

/* Utility classes */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.full-width {
  width: 100%;
}

.text-center {
  text-align: center;
}

.mt-4 {
  margin-top: 1rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.p-4 {
  padding: 1rem;
}

/* Layout classes */
.app-layout {
  display: flex;
  width: 100%;
  height: 100%;
}

/* Main content styles are now handled by layout component */

/* Container styles */
.container {
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 20px;
  box-sizing: border-box;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-content, 
  .main-content.sidebar-collapsed {
    margin-left: 0;
    width: 100%;
  }
}