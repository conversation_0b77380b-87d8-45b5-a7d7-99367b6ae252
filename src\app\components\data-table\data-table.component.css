/* Main container styles */
.container {
  width: 100%;
  padding: 20px;
}

/* Table container styles - consolidated duplicates */
.table-container {
  width: 100%;
  background: white;
  border-radius: 10px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  margin: 25px;
  overflow: hidden;
}

/* Header styles */
.header {
  width: 100%;
  padding: 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
}

/* Filters section - consolidated duplicates */
.filters {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: #f5f5f5;
}

/* Table styles */
table {
  width: 100%;
  border-collapse: collapse;
}

th {
  background-color: #f9fafb;
  color: #6b7280;
  font-weight: 600;
  text-align: left;
  padding: 12px;
  border-bottom: 1px solid #e5e7eb;
}

td {
  padding: 12px;
  border-bottom: 1px solid #e5e7eb;
  color: #374151;
}

/* Actions cell */
.actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* Button styles */
.btn {
  padding: 6px 12px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-weight: 500;
}

.btn-edit {
  background-color: #3b82f6;
  color: white;
}

.btn-delete {
  background-color: #ef4444;
  color: white;
}

/* Status indicators using data attributes instead of :contains */
.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
  display: inline-block;
}

.status[data-status="Active"] {
  background-color: #dcfce7;
  color: #166534;
}

.status[data-status="Inactive"] {
  background-color: #fee2e2;
  color: #991b1b;
}

.status[data-status="Pending"] {
  background-color: #fef3c7;
  color: #92400e;
}

/* Pagination styles */
.pagination {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 1rem;
  gap: 1rem;
}

.page-info {
  color: #6b7280;
}

/* Material form field styles - consolidated ::ng-deep selectors */
::ng-deep .mat-mdc-form-field {
  .mat-elevation-z8 {
    box-shadow: none !important;
  }
  
  .mat-mdc-text-field-wrapper {
    background-color: white;
  }
  
  .mat-mdc-form-field-flex {
    padding: 0 12px !important;
  }
  
  .mat-mdc-form-field-infix {
    padding: 8px 0 !important;
    min-height: unset;
  }
}

/* Search input styles */
.search-input {
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 8px 12px;
  width: 100%;
  max-width: 300px;
}

/* Loading state */
.loading {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
}

/* Error state */
.error-state {
  text-align: center;
  padding: 2rem;
  color: #dc2626;
}