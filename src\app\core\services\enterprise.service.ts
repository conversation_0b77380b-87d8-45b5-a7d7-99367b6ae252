// Frontend: src/app/core/services/enterprise.service.ts
import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { MatSnackBar } from '@angular/material/snack-bar';

export interface EnterpriseUser {
  id: string;
  userName: string;
  email: string;
  fullName: string;
  enterpriseName: string;
  numberOfEmployees: number;
  contractDate: string;
  category: string;
  createdByAdminId?: string;
}

@Injectable({
  providedIn: 'root'
})
export class EnterpriseService {
  readonly apiUrl = 'http://************:5544/api/Account';

  constructor(
    readonly http: HttpClient,
    readonly snackBar: MatSnackBar
  ) {}

  getEnterpriseUsers(): Observable<EnterpriseUser[]> {
    return this.http.get<EnterpriseUser[]>(`${this.apiUrl}/enterprises`).pipe(
      tap(data => console.log('Enterprise users fetched:', data)),
      catchError(this.handleError)
    );
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An error occurred while fetching enterprise users. Please try again later.';
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = error.error?.message ?? error.error?.title ?? `Error Code: ${error.status}\nMessage: ${error.message}`;
    }
    this.showMessage(errorMessage);
    return throwError(() => new Error(errorMessage));
  }

  private showMessage(message: string, action: string = 'Close'): void {
    this.snackBar.open(message, action, {
      duration: 3000,
      verticalPosition: 'top'
    });
  }
}