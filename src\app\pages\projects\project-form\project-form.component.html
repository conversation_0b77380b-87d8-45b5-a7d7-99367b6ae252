<!-- Frontend: src/app/pages/projects/project-form/project-form.component.html -->
<div class="form-container mat-elevation-z4">
    <h2>
      <mat-icon>{{ isUpdateMode ? 'edit' : 'add' }}</mat-icon>
      {{ isUpdateMode ? 'Update Project' : 'Create Project' }}
    </h2>
  
    <form [formGroup]="projectForm" (ngSubmit)="onSubmit()" class="project-form">
      <!-- Name -->
      <mat-form-field appearance="outline">
        <mat-label>Project Name</mat-label>
        <input matInput formControlName="name" required placeholder="Ex: Smart Factory Energy">
        <mat-icon matSuffix>assignment</mat-icon>
        <mat-error *ngIf="projectForm.get('name')?.hasError('required')">
          Project name is required
        </mat-error>
      </mat-form-field>
  
      <!-- Description -->
      <mat-form-field appearance="outline">
        <mat-label>Description</mat-label>
        <textarea matInput formControlName="description" required placeholder="Energy optimization..."></textarea>
        <mat-icon matSuffix>description</mat-icon>
        <mat-error *ngIf="projectForm.get('description')?.hasError('required')">
          Description is required
        </mat-error>
      </mat-form-field>
  
      <!-- Label -->
      <mat-form-field appearance="outline">
        <mat-label>Label</mat-label>
        <mat-select formControlName="label" required>
          <mat-option value="Audit">Audit</mat-option>
          <mat-option value="Travaux">Travaux</mat-option>
          <mat-option value="Installation">Installation</mat-option>
          <mat-option value="Maintenance">Maintenance</mat-option>
          <mat-option value="Planification">Planification</mat-option>
          <mat-option value="Autre">Autre</mat-option>
        </mat-select>
        <mat-icon matSuffix>label</mat-icon>
        <mat-error *ngIf="projectForm.get('label')?.hasError('required')">
          Label is required
        </mat-error>
      </mat-form-field>
  
      <!-- Members -->
      <mat-form-field appearance="outline">
        <mat-label>Project Members</mat-label>
        <mat-select formControlName="members" multiple>
          <mat-option *ngFor="let user of enterpriseUsers" [value]="user.id">
            {{ user.userName }} ({{ user.fullName }})
          </mat-option>
        </mat-select>
        <mat-icon matSuffix>group</mat-icon>
      </mat-form-field>
  
      <!-- Due Date -->
      <mat-form-field appearance="outline">
        <mat-label>Due Date</mat-label>
        <input matInput [matDatepicker]="picker" formControlName="dueDate" required>
        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
        <mat-icon matSuffix>event</mat-icon>
        <mat-error *ngIf="projectForm.get('dueDate')?.hasError('required')">
          Due date is required
        </mat-error>
      </mat-form-field>
  
      <!-- Sensor Count -->
      <mat-form-field appearance="outline">
        <mat-label>Number of Sensors</mat-label>
        <input matInput type="number" min="0" formControlName="sensorCount" required>
        <mat-icon matSuffix>sensors</mat-icon>
        <mat-error *ngIf="projectForm.get('sensorCount')?.hasError('required')">
          Sensor count is required
        </mat-error>
        <mat-error *ngIf="projectForm.get('sensorCount')?.hasError('min')">
          Sensor count must be at least 0
        </mat-error>
      </mat-form-field>
  
      <!-- Progress -->
      <mat-form-field appearance="outline">
        <mat-label>Progress (%)</mat-label>
        <input matInput type="number" min="0" max="100" formControlName="progress" required>
        <mat-icon matSuffix>trending_up</mat-icon>
        <mat-error *ngIf="projectForm.get('progress')?.hasError('required')">
          Progress is required
        </mat-error>
        <mat-error *ngIf="projectForm.get('progress')?.hasError('min')">
          Progress must be at least 0
        </mat-error>
        <mat-error *ngIf="projectForm.get('progress')?.hasError('max')">
          Progress cannot exceed 100
        </mat-error>
      </mat-form-field>
  
      <!-- Buttons -->
      <div class="form-actions">
        <button mat-raised-button color="primary" type="submit" [disabled]="projectForm.invalid || loading">
          <mat-icon>{{ isUpdateMode ? 'save' : 'check_circle' }}</mat-icon>
          {{ isUpdateMode ? 'Update' : 'Create' }} Project
        </button>
        <button mat-raised-button color="warn" type="button" (click)="cancel()" [disabled]="loading">
          <mat-icon>cancel</mat-icon>
          Cancel
        </button>
      </div>
    </form>
  </div>