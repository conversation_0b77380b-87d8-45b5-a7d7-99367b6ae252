<div class="popup-overlay" *ngIf="subscription">
  <div class="site-management-container">
    <div class="popup-header">
      <h2 class="popup-title">
        <span class="title-icon">📋</span>
        Détails de l'Abonnement
      </h2>
      <button class="close-button" (click)="closeDetails()" title="Fermer">
        ×
      </button>
    </div>
    <div class="form-container">
      <form>
        <!--* Section Abonnement *-->
        <h3 class="section-title">
          <span class="section-icon">📄</span>
          Informations de l'Abonnement
        </h3>
        <div class="form-grid">
          <div class="form-group">
            <label for="idlicence"
              >Licence <span class="required">*</span></label
            >
            <input
              id="idlicence"
              type="text"
              [value]="selectedLicenceName"
              disabled
            />
          </div>
          <div class="form-group">
            <label for="dateDebut"
              >Date de Début <span class="required">*</span></label
            >
            <input
              id="dateDebut"
              type="date"
              [value]="subscription.DateDebut | date : 'yyyy-MM-dd'"
              disabled
            />
          </div>
          <div class="form-group">
            <label for="dateFin"
              >Date de Fin <span class="required">*</span></label
            >
            <input
              id="dateFin"
              type="date"
              [value]="subscription.DateFin | date : 'yyyy-MM-dd'"
              disabled
            />
          </div>
          <div class="form-group">
            <label for="paymentFrequency">Fréquence de Paiement</label>
            <input
              id="paymentFrequency"
              type="text"
              [value]="subscription.PaymentFrequency"
              disabled
            />
          </div>
          <div class="form-group">
            <label for="price">Prix de l'Abonnement</label>
            <input
              id="price"
              type="text"
              [value]="subscription.Price"
              disabled
            />
          </div>
        </div>
        <div class="form-group">
          <label for="status">Statut <span class="required">*</span></label>
          <input
            id="status"
            type="text"
            [value]="subscription.Status"
            disabled
          />
        </div>
        <div class="form-actions">
          <button type="button" class="btn-secondary" (click)="closeDetails()">
            Fermer
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
