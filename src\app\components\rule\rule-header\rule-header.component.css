.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px;
    border-bottom: 1px solid var(--card-border);
    background: var(--white);
  }

  .title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    flex: 1;
  }

  /* === LOADING STATES === */
  .loading-icon {
    font-size: 18px !important;
    width: 18px !important;
    height: 18px !important;
    color: var(--green-main);
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }