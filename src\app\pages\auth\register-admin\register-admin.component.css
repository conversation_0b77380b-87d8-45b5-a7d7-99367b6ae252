/* src/app/pages/auth/register-admin/register-admin.component.css
.register-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: #f5f5f5;
  }
  
  .register-card {
    width: 100%;
    max-width: 400px;
    padding: 20px;
  }
  
  .full-width {
    width: 100%;
    margin-bottom: 10px;
  } */
   /* src/app/pages/auth/register-enterprise/register-enterprise.component.css */
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: #ffffff;
  padding: 20px;
}

.register-card {
  width: 100%;
  max-width: 480px;
  background: #ffffff;
  border-radius: 15px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.register-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.18);
}

.header {
  background: var(--green-main);
  padding: 20px;
  text-align: center;
}

.title {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 24px;
  font-weight: 600;
}

.title-icon {
  margin-right: 10px;
  font-size: 28px;
  color: whitesmoke;
}

.subtitle {
  color: whitesmoke;
  font-size: 14px;
  margin-top: 5px;
}

.content {
  padding: 20px;
}

.full-width {
  width: 100%;
  margin-bottom: 20px;
}

mat-form-field {
  transition: all 0.3s ease;
}

mat-form-field:hover .input-icon {
  color: #49b38e;
  transform: scale(1.1);
}

.input-icon {
  color: #4A5568;
  transition: color 0.3s ease, transform 0.3s ease;
}

mat-label {
  color: #4A5568;
}


.button-container {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.submit-button, .cancel-button {
    flex: 1;
    padding: 10px;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
  }
.submit-button {
    background: #2fa939;
    color: #ffffff;
  }
  
  .submit-button:hover:not([disabled]) {
    background: #2b765b;
    transform: scale(1.05);
  }
  
  .submit-button[disabled] {
    background: #cccccc;
    color: #666666;
  }
  
  .cancel-button {
    border-color: #e57373;
    color: #e57373;
  }
  
  .cancel-button:hover {
    background: #e57373;
    color: #ffffff;
    transform: scale(1.05);
  }

mat-icon {
    margin-right: 8px;
  }
