import { ControllerServerController } from "./controllerServerController";
import { AuditModel } from "./models-audit/audit-model";
import { Transaction } from "./transaction";

export class Controller extends AuditModel {
  HostName!: string;
  Model!: string; 
  SerialNumber!: string; 
  MacAddress!: string; 
  IpAddress!: string;
  LastConnection?: Date; 
  State!: boolean; 
  InstallationDate!: Date;
  BaseTopic!: string;
  Transactions?: Transaction[];
  ControllerServerControllers?: ControllerServerController[];
}
