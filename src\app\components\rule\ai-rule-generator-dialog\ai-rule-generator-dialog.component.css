.ai-rule-generator-dialog {
  min-width: 500px;
  max-width: 600px;
}

.dialog-header {
  display: flex;
  align-items: center; /* Ensures vertical alignment */
  gap: 12px;
  margin-bottom: 16px;
  padding: 24px 24px 0 24px; /* Added padding to align with content and dialog title */
}

.ai-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
  color: var(--primary);
  /* Ensure proper vertical alignment for the icon */
  display: inline-flex; /* Use flex for fine-grained alignment if needed */
  align-items: center; /* Center content vertically within the icon container */
  justify-content: center; /* Center content horizontally within the icon container */
}

.dialog-header h2 {
  margin: 0; /* Remove default margin from h2 to prevent misalignment */
  line-height: 1.2; /* Adjust line-height if text seems off */
}

.dialog-content {
  padding: 0 24px;
}

.input-card {
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.description {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.5;
}

.full-width {
  width: 100%;
}

.ai-tips {
  background: #f5f5f5;
  border-radius: 8px;
  padding: 16px;
  margin: 20px 0;
}

.ai-tips h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 12px 0;
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.ai-tips h4 mat-icon {
  color: var(--warning);
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.ai-tips ul {
  margin: 0;
  padding-left: 20px;
}

.ai-tips li {
  margin-bottom: 6px;
  font-size: 13px;
  color: #555;
}

.generating-status {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--green-light);
  border-radius: 8px;
  margin: 16px 0;
  color: var(--green-dark);
  font-weight: 500;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #ffebee;
  border-radius: 8px;
  margin: 16px 0;
  color: var(--danger);
}

.error-message mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.dialog-actions {
  padding: 16px 24px;
  gap: 12px;
  display: flex;
  justify-content: flex-end;
}

.cancel-button {
  color: var(--text-secondary);
}

.generate-button {
  background: linear-gradient(45deg, var(--primary), var(--primary-light)) !important;
  color: white !important;
}

.generate-button:disabled {
  background: var(--grey-light) !important;
  color: var(--text-secondary) !important;
}

.generate-button mat-spinner {
  margin-right: 8px;
}

.preview-section {
  margin-top: 16px;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.preview-section h3 {
  margin-bottom: 12px;
  color: var(--primary);
  font-size: 18px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.preview-section p {
  margin-bottom: 16px;
  line-height: 1.5;
  color: #333;
}

.preview-section p strong {
  color: var(--primary-dark);
}

pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 13px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
}

mat-expansion-panel {
  box-shadow: none !important;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 16px;
}

mat-expansion-panel-header {
  background-color: #f9f9f9;
  border-bottom: 1px solid #e0e0e0;
}

.edit-button {
  color: var(--text-secondary);
  margin-right: 8px;
}

.confirm-button {
  background: var(--success) !important;
  color: white !important;
}