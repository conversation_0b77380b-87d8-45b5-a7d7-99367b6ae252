// import { Device } from './device.model';
// import { FloorPlan } from './floor-plan.model';

// export interface Sites {
//   id: number; // Make optional if not always present in API
//   description?: string;
//   imagePaths?: string[];
//   name: string;
//   organisation?: Organisation; // From organisation.service.ts
//   floorPlan?: FloorPlan; // Optional if not always provided by API
//   devices?: Device[]; // Optional if not part of the API response
// }

// export interface Organisation {
//   id: number;
//   name?: string; // Adjust based on your organisation.service.ts
// }
