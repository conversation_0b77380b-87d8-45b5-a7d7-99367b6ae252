<ngx-loading 
  [show]="isLoading" 
  [config]="{ 
    animationType: 'threeBounce', 
    backdropBackgroundColour: 'rgba(0,0,0,0.2)', 
    primaryColour: '#10b981',
    secondaryColour: '#10b981',
    tertiaryColour: '#10b981'
  }"></ngx-loading>

  <div class="header improved-header">
    <h1 class="title improved-title">Gestion des abonnements</h1>
    <p class="subtitle improved-subtitle">
      Gérez les abonnements pour vos clients. Sélectionnez un client pour voir les licences disponibles.
    </p>
  </div>

  <div class="statistics-container">
    <div class="stats-grid">
      <div class="stat-card total-card">
        <div class="stat-icon">
          <span class="material-icons">assessment</span>
        </div>
        <div class="stat-content">
          <h3 class="stat-number">{{ getTotalSubscriptions() }}</h3>
          <p class="stat-label">Total Abonnements</p>
        </div>
      </div>
      
      <div class="stat-card pending-card">
        <div class="stat-icon">
          <span class="material-icons">schedule</span>
        </div>
        <div class="stat-content">
          <h3 class="stat-number">{{ getPendingSubscriptions() }}</h3>
          <p class="stat-label">En Attente</p>
        </div>
      </div>
      
      <div class="stat-card paid-card">
        <div class="stat-icon">
          <span class="material-icons">check_circle</span>
        </div>
        <div class="stat-content">
          <h3 class="stat-number">{{ getPaidSubscriptions() }}</h3>
          <p class="stat-label">Payé</p>
        </div>
      </div>
      
      <div class="stat-card cancelled-card">
        <div class="stat-icon">
          <span class="material-icons">cancel</span>
        </div>
        <div class="stat-content">
          <h3 class="stat-number">{{ getCancelledSubscriptions() }}</h3>
          <p class="stat-label">Resilié</p>
        </div>
      </div>
    </div>
  </div>

  <div class="table-container">
    <div class="class-search header">
      <div class="header-container improved-header-container">
        <div class="container-position">
          <div class="filters-section">
            <div class="search-bar-table">
              <div class="search-bar-inner improved-search-bar-inner">
                <input
                  type="text"
                  class="search-input glass improved-search-input"
                  [(ngModel)]="searchQuery"
                  (keyup.enter)="filterClientsTable()"
                  (input)="filterClientsTable()"
                  placeholder="Rechercher un client..."
                  autocomplete="off"
                />
                <button class="search-btn improved-search-btn" (click)="filterClientsTable()">
                  <span class="material-icons">search</span>
                </button>
                <button class="confirm-btn improved-confirm-btn" (click)="goToAbonementNew()">
                  Ajouter une abonnement
                </button>
              </div>
            </div>
            
            <div class="additional-filters">
              <div class="filter-group">
                <label class="filter-label">Statut:</label>
                <select [(ngModel)]="selectedStatusFilter" (change)="filterClientsTable()" class="filter-select">
                  <option value="">Tous les statuts</option>
                  <option value="En attente">En attente</option>
                  <option value="Payé">Payé</option>
                  <option value="Resilié">Resilié</option>
                </select>
              </div>
              
              <div class="filter-group">
                <label class="filter-label">Licence:</label>
                <select [(ngModel)]="selectedLicenceFilter" (change)="filterClientsTable()" class="filter-select">
                  <option value="">Toutes les licences</option>
                  <option *ngFor="let licence of licences" [value]="licence.Name">{{ licence.Name }}</option>
                </select>
              </div>
              
              <div class="filter-group">
                <label class="filter-label">Fréquence:</label>
                <select [(ngModel)]="selectedFrequencyFilter" (change)="filterClientsTable()" class="filter-select">
                  <option value="">Toutes les fréquences</option>
                  <option value="Mensuel">Mensuel</option>
                  <option value="Annuel">Annuel</option>
                </select>
              </div>
              
              <button class="clear-filters-btn" (click)="clearFilters()">
                <span class="material-icons">clear</span>
                Effacer filtres
              </button>
            </div>
          </div>
        </div>
        <app-generic-table
          [data]="pagedTableRows"  
          [headers]="['Client', 'Licence', 'Date debut', 'Date Fin', 'Statut', 'Prix', 'Frequence de paiement']"
          [keys]="['ClientName', 'LicenceName', 'DateDebut', 'DateFin', 'Status', 'price', 'PaymentFrequency']"
          [actions]="['Modifier', 'Annuler']"
          (actionTriggered)="onTableAction($event)">
        </app-generic-table>
        <div class="table-pagination-controls improved-table-pagination-controls">
          <div class="table-pagination-size">
            <span>Items par page:</span>
            <select [(ngModel)]="tablePageSize" (change)="onTablePageSizeChange()">
              <option value="5">5</option>
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="50">50</option>
            </select>
          </div>

          
          
          <div class="table-pagination-info">
            Affichage de {{tableFirstItem}} à {{tableLastItem}} sur {{filteredTableRows.length}} éléments
          </div>
          
          <div class="table-pagination-buttons improved-pagination-buttons">
            <button (click)="prevTablePage()" [disabled]="tableCurrentPage === 0">
              &lt;
            </button>
            <span class="table-page-numbers improved-table-page-numbers">
              <span class="pagination-page-number">
                {{ tableCurrentPage + 1 }}
              </span>
            </span>
            <button (click)="nextTablePage()" [disabled]="tableCurrentPage === tableTotalPages - 1">
              &gt;
            </button>
          </div>
        </div>
          <div class="overlay" *ngIf="showCannotCancelPendingPopup" @fadeInOut>
  <div class="confirmation-popup" @slideDown>
    <div class="popup-header">
      <h3>Annulation impossible</h3>
      <button class="close-popup-btn" (click)="closeCannotCancelPendingPopup()">
        <span class="material-icons">close</span>
      </button>
    </div>
    <div class="popup-content">
      <div class="confirmation-message">
        <span class="material-icons confirmation-icon" style="color:#ef4444;">block</span>
        <p>Vous ne pouvez pas annuler une licence dont le statut est <strong>En attente</strong>.</p>
      </div>
    </div>
    <div class="popup-actions">
      <button class="confirm-btn" (click)="closeCannotCancelPendingPopup()">OK</button>
    </div>
  </div>
      </div>
    </div>
  </div>

  <div class="overlay" *ngIf="showCannotModifyResiliePopup" @fadeInOut>
  <div class="confirmation-popup" @slideDown>
    <div class="popup-header">
      <h3>Modification impossible</h3>
      <button class="close-popup-btn" (click)="closeCannotModifyResiliePopup()">
        <span class="material-icons">close</span>
      </button>
    </div>
    <div class="popup-content">
      <div class="confirmation-message">
        <span class="material-icons confirmation-icon" style="color:#ef4444;">block</span>
        <p>Vous ne pouvez pas modifier un abonnement dont le statut est <strong>Resilié</strong>.</p>
      </div>
    </div>
    <div class="popup-actions">
      <button class="confirm-btn" (click)="closeCannotModifyResiliePopup()">OK</button>
    </div>
  </div>
</div>

<div class="overlay" *ngIf="showCannotCancelPendingPopup" @fadeInOut>
  <div class="confirmation-popup" @slideDown>
    <div class="popup-header">
      <h3>Annulation impossible</h3>
      <button class="close-popup-btn" (click)="closeCannotCancelPendingPopup()">
        <span class="material-icons">close</span>
      </button>
    </div>
    <div class="popup-content">
      <div class="confirmation-message">
        <span class="material-icons confirmation-icon" style="color:#ef4444;">block</span>
        <p>Vous ne pouvez pas annuler une licence dont le statut est <strong>En attente</strong>.</p>
      </div>
    </div>
    <div class="popup-actions">
      <button class="confirm-btn" (click)="closeCannotCancelPendingPopup()">OK</button>
    </div>
  </div>
</div>

  <div class="success-notification" *ngIf="showCancelNotification" @slideDown>
    <div class="notification-content">
      <span class="material-icons success-icon">check_circle</span>
      <div class="notification-text">
        <h4>Licence annulée avec succès !</h4>
        <p>La licence a été annulée pour le client.</p>
      </div>
      <button class="close-notification-btn" (click)="showCancelNotification = false">
        <span class="material-icons">close</span>
      </button>
    </div>
</div>