<div class="category-card" [attr.data-type]="organisationType" (click)="onCardClick()">
  <button
    class="details-button"
    (click)="onDetailsClick($event)"
    title="Voir tous les détails"
  >
    <i class="material-icons details-icon">bar_chart</i>
  </button>
  <div
    class="icon-container"
    [style.background]="
      'linear-gradient(135deg, ' + cardColor + '20, ' + cardColor + '40)'
    "
  >
    <i class="material-icons category-icon" [style.color]="cardColor">{{
      icon
    }}</i>
  </div>
  <div class="category-info">
    <h3 class="category-title">{{ displayLabel }}</h3>
    <p class="category-count">
      {{ clientCount }} Organisation<span *ngIf="clientCount > 1">s</span>
    </p>
  </div>
</div>