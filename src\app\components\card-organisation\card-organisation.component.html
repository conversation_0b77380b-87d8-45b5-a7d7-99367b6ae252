<div
  onkeypress=""
  class="org-card"
  [@cardHover]="cardState"
  (mouseenter)="onMouseEnter()"
  (mouseleave)="onMouseLeave()"
  (click)="onDetailsClick()"
>
  <div class="card-image-container">
    <img
      [src]="imageUrl || 'assets/images/default-organisation.jpg'"
      [alt]="organisation.nom"
      class="card-image"
    />
    <div class="card-overlay"></div>
  </div>
  <div class="card-header">
    <h3 class="org-title">{{ organisation.nom }}</h3>
    <p class="org-type">Type: {{ organisation.type }}</p>
    <p class="org-email" *ngIf="organisation.emailAddress">
      <i class="material-icons icon-small">email</i>
      {{ organisation.emailAddress }}
    </p>
  </div>
  <div class="card-content">
    <div class="info-row">
      <div class="info-item">
        <i class="material-icons icon-small">people</i>
        {{ organisation.nombreEmployees }} employés
      </div>
      <div class="info-item">
        <i class="material-icons icon-small">devices</i>
        {{ organisation.nombreEquipement }} équipements
      </div>
    </div>
    <div class="info-row" *ngIf="organisation.consommation">
      <div class="info-item full-width">
        <i class="material-icons icon-small">bolt</i>
        {{ organisation.consommation }} kWh
      </div>
    </div>
    <div class="info-row" *ngIf="!organisation.consommation">
      <div class="info-item full-width">
        <i class="material-icons icon-small">bolt</i>
        0 kWh
      </div>
    </div>
  </div>

  <div class="energy-indicators">
    <div class="indicator positive">
      <i class="material-icons">trending_up</i>
      <span class="value">-15%</span>
      <span class="label">Économies</span>
    </div>
    <div class="indicator negative">
      <i class="material-icons">trending_down</i>
      <span class="value">2500 kWh</span>
      <span class="label">Mois dernier</span>
    </div>
  </div>

  <div class="card-actions">
    <button class="btn btn-primary" (click)="onDetailsClick()">
      <i class="material-icons">visibility</i> 
    </button>
    <button class="btn btn-accent" (click)="onEditClick($event)">
      <i class="material-icons">edit</i> 
    </button>
    <button class="btn btn-danger" (click)="onDeleteClick($event)">
      <i class="material-icons">delete</i> 
    </button>
  </div>
</div>
