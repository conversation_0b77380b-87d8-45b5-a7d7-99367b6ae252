import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-success',
  imports: [CommonModule],
  templateUrl: './success.component.html',
  styleUrl: './success.component.css'
})
export class SuccessComponent {
  showSuccessNotification = false;
  showConfirmationPopup = false;

  public confirmPopUp() {
  	this.showSuccessNotification = true;
	  setTimeout(() => { this.showSuccessNotification = false }, 5000);
  }
  public closeSuccessNotification() {
    this.showSuccessNotification = false;
  }
}
