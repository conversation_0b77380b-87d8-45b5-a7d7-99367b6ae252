import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';import { ApiService } from '../api.service';
import { TypeCapteur } from '@app/shared/models/typeCapteur';

@Injectable({ providedIn: 'root' })
export class TypeCapteurApiService extends ApiService<TypeCapteur> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("type-capteur");
  }
}
