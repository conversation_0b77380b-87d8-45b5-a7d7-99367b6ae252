import { Capteur } from "./capteur";
import { AuditModel } from "./models-audit/audit-model";
export class SensorReading extends AuditModel {
  IdCapteur!: string;
  Capteur!: Capteur | null;
  Timestamp!: string;
  Battery!: number | null;
  LinkQuality!: number | null;
  Temperature!: number | null;
  Humidity!: number | null;
  Illuminance!: number | null;
  Power!: number | null;
  Energy!: number | null;
  Voltage!: number | null;
  Current!: number | null;
  Occupancy!: boolean | null;
  Tamper!: boolean | null;
  Contact!: boolean | null;
  BatteryLevel!: number | null;
  Value!: number | null;
}


