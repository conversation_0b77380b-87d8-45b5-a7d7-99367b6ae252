import { Component, HostListener, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Subscription } from '@app/core/models/subscription';
import { SubscriptionApiService } from '@app/core/services/administrative/subscription.service';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { LicenceApiService } from '@app/core/services/administrative/licence.service';
import { Client } from '@app/core/models/client';
import { Licence } from '@app/core/models/licence';
import { trigger, transition, style, animate } from '@angular/animations';
import {environment} from '@app/environments/environment';
import {NgToastComponent, NgToastService, TOAST_POSITIONS} from 'ng-angular-popup';
import JSZip from 'jszip';
import { saveAs } from 'file-saver';
import { PageEvent, MatPaginatorModule } from '@angular/material/paginator';
import { Lister, Pagination, FilterParam } from '@app/core/models/util/page';
import { ngxLoadingAnimationTypes } from 'ngx-loading';
import { NgxLoadingComponent } from 'ngx-loading';
import { NgxLoadingModule } from 'ngx-loading';



interface InvoiceData {
  date: string;
  dueDate: string;
  invoiceNumber: string;
  recipient: {
    name: string;
    email: string;
    address: string;
    cityStateZip: string;
  };
  items: {
    description: string;
    unitPrice: number;
    total: number;
  }[];
}

@Component({
  selector: 'app-facture',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NgToastComponent,
    MatPaginatorModule,
    NgxLoadingModule
  ],
  templateUrl: './facture.component.html',
  styleUrls: ['./facture.component.css'],
  animations: [
    trigger('slideInRight', [
      transition(':enter', [
        style({ transform: 'translateX(100%)', opacity: 0 }),
        animate('400ms cubic-bezier(0.23, 1, 0.32, 1)', style({ transform: 'translateX(0)', opacity: 1 }))
      ]),
      transition(':leave', [
        animate('300ms cubic-bezier(0.23, 1, 0.32, 1)', style({ transform: 'translateX(100%)', opacity: 0 }))
      ])
    ])
  ]
})
export class FactureComponent implements OnInit {
  private apiUrl = environment.host + '/api/Pdf';

  clients: Client[] = [];
  licences: Licence[] = [];
  subscriptions: Subscription[] = [];
  filteredInvoices: any[] = [];
  searchQuery: string = '';
  statusFilter: string = 'All';
  selectedInvoice: any = null;
  invoiceDetail: any = null;
  dropdownOpen: string | null = null;
  selectedInvoices: Set<string> = new Set();
  allSelected: boolean = false;
  isLoading: boolean = true;
  isDownloading: boolean = false;

  pageSize: number = 10;
  currentPage: number = 0;
  totalInvoices: number = 0;
  searchParam: string = '';
  hasSearchFilter: boolean = false;

  constructor(
    private clientApiService: ClientApiService,
    private licenceApiService: LicenceApiService,
    private subscriptionApiService: SubscriptionApiService,
    private http: HttpClient,
    private toast: NgToastService,
  ) {}

  ngOnInit() {
    this.loadData();
  }

  async loadData() {
    try {
      this.isLoading = true;
      await Promise.all([
        this.fetchClients(),
        this.fetchLicences(),
        this.fetchSubscriptions()
      ]);
      this.updateFilteredInvoices();
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      this.isLoading = false;
    }
  }

  fetchClients(): Promise<void> {
    return new Promise((resolve) => {
      this.clientApiService.getAll().subscribe({
        next: (data: Client[]) => {
          this.clients = data;
          resolve();
        },
        error: (error) => {
          console.error('Error fetching clients:', error);
          resolve();
        }
      });
    });
  }

  fetchLicences(): Promise<void> {
    return new Promise((resolve) => {
      this.licenceApiService.getAll().subscribe({
        next: (data: Licence[]) => {
          this.licences = data;
          resolve();
        },
        error: (error) => {
          console.error('Error fetching licences:', error);
          resolve();
        }
      });
    });
  }

  fetchSubscriptions(): Promise<void> {
    return new Promise((resolve) => {
      this.subscriptionApiService.getAll().subscribe({
        next: (data: Subscription[]) => {
          this.subscriptions = data;
          resolve();
        },
        error: (error) => {
          console.error('Error fetching subscriptions:', error);
          resolve();
        }
      });
    });
  }

  updateFilteredInvoices() {
    const allInvoices = this.subscriptions.map((sub, idx) => {
      const client = this.clients.find(c => c.Id === sub.ClientId) || null;
      const licence = this.licences.find(l => l.Id === sub.LicenceId) || null;

      const invoiceNumber = `FA${(idx + 1).toString().padStart(6, '0')}`;

      return {
        id: invoiceNumber,
        clients: client,
        licences: licence,
        dateDebut: this.formatDate(sub.DateDebut),
        dateFin: this.formatDate(sub.DateFin),
        price: sub.Price,
        status: sub.Status,
        paymentFrequency: sub.PaymentFrequency
      };
    }).filter(invoice => {
      const matchesSearch = !this.searchQuery ||
        (invoice.clients?.Name?.toLowerCase().includes(this.searchQuery.toLowerCase()));
      const matchesStatus = this.statusFilter === 'All' ||
        (this.statusFilter === 'Paid' ? invoice.status === 'Actif' : invoice.status === 'En attente');
      return matchesSearch && matchesStatus;
    });

    this.totalInvoices = allInvoices.length;

    // Apply pagination
    const startIndex = this.currentPage * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.filteredInvoices = allInvoices.slice(startIndex, endIndex);
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.updateFilteredInvoices();
  }

  onSearchKeyup(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      this.searchInvoices();
    } else if (event.key === 'Backspace' && this.searchQuery === '') {
      this.clearSearch();
    }
  }

  searchInvoices(): void {
    this.currentPage = 0; // Reset to first page when searching
    this.updateFilteredInvoices();
  }

  clearSearch(): void {
    this.searchQuery = '';
    this.hasSearchFilter = false;
    this.currentPage = 0; // Reset to first page when clearing search
    this.updateFilteredInvoices();
  }

  formatDate(date: Date | string | null): string {
    if (!date) return '';
    const d = new Date(date);
    return d.toLocaleDateString('fr-FR');
  }

  private convertToInvoiceData(invoice: any): InvoiceData {
    const dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + 30);

    return {
      date: invoice.dateDebut || new Date().toLocaleDateString('fr-FR'),
      dueDate: dueDate.toLocaleDateString('fr-FR'),
      invoiceNumber: invoice.id,
      recipient: {
        name: invoice.clients?.Name || 'Client inconnu',
        email: invoice.clients?.Email || '<EMAIL>',
        address: invoice.clients?.Address || 'Adresse non disponible',
        cityStateZip: invoice.clients?.City ? `${invoice.clients.City}, ${invoice.clients.PostalCode || ''}` : 'Ville non disponible'
      },
      items: [{
        description: invoice.licences?.Name || 'Licence',
        unitPrice: invoice.price || 0,
        total: invoice.price || 0
      }]
    };
  }

  //1 PDF DOWNLOAD
  async downloadInvoice(invoice: any) {
    try {
      this.isDownloading = true;
      const invoiceData = this.convertToInvoiceData(invoice);

      const response = await this.http.post(`${this.apiUrl}/generate-custom`, invoiceData, {
        responseType: 'blob',
        headers: new HttpHeaders({
          'Content-Type': 'application/json'
        })
      }).toPromise();

      if (response) {
        this.downloadBlob(response, `facture-${invoice.id}.pdf`);
      }
    } catch (error) {
      console.error('Error downloading invoice:', error);
      alert('Erreur lors du téléchargement de la facture');
    } finally {
      this.isDownloading = false;
      this.dropdownOpen = null;
    }
  }

  //MULTIPLE PDFS DOWNLOAD
  async downloadSelected() {
    if (this.selectedInvoices.size === 0) return;

    try {
      this.isDownloading = true;
      const selectedInvoicesList = this.filteredInvoices.filter(invoice =>
        this.selectedInvoices.has(invoice.id)
      );

      if (selectedInvoicesList.length === 1) {
        const invoice = selectedInvoicesList[0];
        const invoiceData = this.convertToInvoiceData(invoice);

        const response = await this.http.post(`${this.apiUrl}/generate-custom`, invoiceData, {
          responseType: 'blob',
          headers: new HttpHeaders({
            'Content-Type': 'application/json'
          })
        }).toPromise();

        if (response) {
          saveAs(response, `facture-${invoice.id}.pdf`);
        }
        this.showSuccess("Téléchargement réussi", "Succés");
      } else {
        const zip = new JSZip();

        for (const invoice of selectedInvoicesList) {
          const invoiceData = this.convertToInvoiceData(invoice);

          const response = await this.http.post(`${this.apiUrl}/generate-custom`, invoiceData, {
            responseType: 'blob',
            headers: new HttpHeaders({
              'Content-Type': 'application/json'
            })
          }).toPromise();

          if (response) {
            zip.file(`facture-${invoice.id}.pdf`, response);
          }

          await new Promise(resolve => setTimeout(resolve, 500));
        }

        this.showSuccess("Téléchargement réussi", "Succés");
        const zipBlob = await zip.generateAsync({ type: 'blob' });
        saveAs(zipBlob, 'factures.zip');
      }
    } catch (error) {
      console.error('Error downloading selected invoices:', error);
      this.showError('Erreur lors du téléchargement des factures sélectionnées', "Erreur");
    } finally {
      this.isDownloading = false;
    }
  }

  private downloadBlob(blob: Blob, filename: string) {
    saveAs(blob, filename);
  }

  get summary() {
    const allFilteredInvoices = this.subscriptions.map((sub, idx) => {
      const client = this.clients.find(c => c.Id === sub.ClientId) || null;
      const licence = this.licences.find(l => l.Id === sub.LicenceId) || null;

      const invoiceNumber = `FA${(idx + 1).toString().padStart(6, '0')}`;

      return {
        id: invoiceNumber,
        clients: client,
        licences: licence,
        dateDebut: this.formatDate(sub.DateDebut),
        dateFin: this.formatDate(sub.DateFin),
        price: sub.Price,
        status: sub.Status,
        paymentFrequency: sub.PaymentFrequency
      };
    }).filter(invoice => {
      const matchesSearch = !this.searchQuery ||
        (invoice.clients?.Name?.toLowerCase().includes(this.searchQuery.toLowerCase()));
      const matchesStatus = this.statusFilter === 'All' ||
        (this.statusFilter === 'Résilié' ? invoice.status === 'Payé' : invoice.status === 'En attente');
      return matchesSearch && matchesStatus;
    });

    const total = allFilteredInvoices.reduce((sum, invoice) => sum + (invoice.price || 0), 0);
    const paid = allFilteredInvoices
      .filter(invoice => invoice.status === 'Payé')
      .reduce((sum, invoice) => sum + (invoice.price || 0), 0);
    const enattente = allFilteredInvoices
      .filter(invoice => invoice.status === 'En attente')
      .reduce((sum, invoice) => sum + (invoice.price || 0), 0);
    const résilié = allFilteredInvoices
      .filter(invoice => invoice.status === 'Résilié')
      .reduce((sum, invoice) => sum + (invoice.price || 0), 0);

    return {
      total: total,
      paid: paid,
      unpaid: enattente
    };
  }

  get selectedTotal(): number {
    return this.filteredInvoices
      .filter(invoice => this.selectedInvoices.has(invoice.id))
      .reduce((sum, invoice) => sum + (invoice.price || 0), 0);
  }

  closeDetail() {
    this.selectedInvoice = null;
    this.invoiceDetail = null;
  }

  payNow() {
    if (this.selectedInvoice && this.invoiceDetail) {
      this.selectedInvoice.status = 'Payé';
      this.invoiceDetail.amountDue = 0;
      this.closeDetail();
    }
  }

  toggleDropdown(event: Event, invoiceId: string) {
    event.stopPropagation();
    this.dropdownOpen = this.dropdownOpen === invoiceId ? null : invoiceId;
  }

  @HostListener('document:click', ['$event'])
  onClick(event: MouseEvent) {
    if (!(event.target as HTMLElement).closest('.action-dropdown')) {
      this.dropdownOpen = null;
    }
  }

  previewInvoice(invoice: any) {
    console.log('Preview invoice:', invoice.id);
    this.selectedInvoice = invoice;
    this.invoiceDetail = this.getInvoiceDetail(invoice);
    this.dropdownOpen = null;
  }

  activateInvoice(invoice: any) {
    console.log('Activate invoice:', invoice.id);
    invoice.status = 'Payé';
    this.dropdownOpen = null;

    // Find the corresponding subscription by invoice id
    const subscription = this.subscriptions.find((sub, idx) => {
      const invoiceNumber = `FA${(idx + 1).toString().padStart(6, '0')}`;
      return invoiceNumber === invoice.id;
    });

    if (subscription) {
      // Update the subscription status in the backend
      const updatedSubscription = {
        ...subscription,
        Status: 'Payé'
      };
      this.subscriptionApiService.update(updatedSubscription).subscribe({
        next: () => {
          // Optionally refresh data or update UI
          this.loadData();
        },
        error: (error) => {
          console.error('Error updating subscription status:', error);
        }
      });
    }
  }


  toggleSelectInvoice(invoiceId: string, event: Event) {
    event.stopPropagation();

    if (this.selectedInvoices.has(invoiceId)) {
      this.selectedInvoices.delete(invoiceId);
    } else {
      this.selectedInvoices.add(invoiceId);
    }

    this.updateSelectAllState();
  }

  toggleSelectAll(event: Event) {
    const checkbox = event.target as HTMLInputElement;
    this.allSelected = checkbox.checked;

    if (this.allSelected) {
      this.selectedInvoices.clear();
      this.filteredInvoices.forEach(invoice => {
        this.selectedInvoices.add(invoice.id);
      });

    } else {
      // Deselect all invoices
      this.selectedInvoices.clear();
    }
  }

  updateSelectAllState() {
    if (this.filteredInvoices.length === 0) {
      this.allSelected = false;
      return;
    }

    this.allSelected = this.filteredInvoices.every(invoice =>
      this.selectedInvoices.has(invoice.id)
    );

  }

  getInvoiceDetail(invoice: any): any {

    const items = [{
      description: invoice.clients?.Name || "Clients",
      price: invoice.price,
      amount: invoice.price
    }];
    const subtotal = items.reduce((sum, item) => sum + (item.amount || 0), 0);
    const discount = 0;
    const total = subtotal - discount;
    return {
      from: {
        name: "Your Company",
        email: "<EMAIL>"
      },
      to: {
        name: invoice.clients?.Name || "Client",
        email: invoice.clients?.Email || "<EMAIL>"
      },
      date: invoice.dateDebut,
      subject: `Facture pour ${invoice.licences?.Name || 'Licence'}`,
      billTo: invoice.clients?.Name || "Client",
      currency: "EUR",
      items: items,
      subtotal: subtotal,
      discount: discount,
      total: total,
      amountDue: invoice.status === 'En attente' ? total : 0
    };
  }

  previewSelected() {
    if (this.selectedInvoices.size === 0) return;
    const selected = this.filteredInvoices.filter(invoice =>
      this.selectedInvoices.has(invoice.id)
    );
    if (selected.length === 1) {
      this.previewInvoice(selected[0]);
    } else if (selected.length > 1) {
      this.selectedInvoice = { multi: true };
      this.invoiceDetail = this.getMultiInvoiceDetail(selected);
    }
  }

  getMultiInvoiceDetail(invoices: any[]): any {
    const items = invoices.map(inv => ({
      description: `${inv.clients?.Name || "Client"}`,
      price: inv.price,
      amount: inv.price
    }));
    const subtotal = items.reduce((sum, item) => sum + (item.amount || 0), 0);
    const discount = 0;
    const total = subtotal - discount;
    return {
      from: {
        name: "Your Company",
        email: "<EMAIL>"
      },
      to: {
        name: "Clients multiples",
        email: "-"
      },
      date: "-",
      subject: "Factures groupées",
      billTo: "Clients multiples",
      currency: "EUR",
      items: items,
      subtotal: subtotal,
      discount: discount,
      total: total,
      amountDue: total
    };
  }

  activateSelected() {
    if (this.selectedInvoices.size === 0) return;
    console.log('Activating selected invoices:', Array.from(this.selectedInvoices));
  }

  public showSuccess(message: string, title: string) {
    this.toast.info(message, title, 3000, false);
  }

  public showError(message: string, title: string) {
    this.toast.warning(message, title, 3000, false);
  }

  protected readonly TOAST_POSITIONS = TOAST_POSITIONS;
}