<div class="report-generator-container">
  <div class="header-section">
    <h1 class="page-title">
      <mat-icon>assessment</mat-icon>
      Générateur de Rapport Énergétique
    </h1>
    <p class="page-subtitle">Générez des rapports détaillés sur la consommation énergétique de vos clients</p>
  </div>

  <!-- Main Form -->
  <div class="form-container" *ngIf="!showPreview">
    <div class="form-card">
      <h2 class="section-title">
        <mat-icon>business</mat-icon>
        Configuration du Rapport
      </h2>

      <!-- Client Selection -->
      <div class="form-section">
        <label for="" class="form-label">Sélectionner le Client *</label>
        <div class="search-container">
          <div class="search-input-wrapper">
            <mat-icon class="search-icon">search</mat-icon>
            <input
              type="text"
              class="search-input"
              [(ngModel)]="searchTerm"
              placeholder="Rechercher un client..."
              (focus)="searchTerm = ''"
            >
          </div>

          <div class="dropdown-container" *ngIf="searchTerm && !selectedClient">
                <button class="dropdown-item"
                    *ngFor="let client of filteredClients"
                    (click)="onClientSelect(client)"
                    [attr.aria-label]="'Sélectionner ' + client.Name">
                <div class="org-info">
                    <div class="org-name">{{client.Name}}</div>
                    <div class="org-details">{{client.Organisation?.Nom || 'Aucune organisation'}} • {{client.CompanySize || 0}} employés</div>
                </div>
                <mat-icon class="select-icon">business</mat-icon>
                </button>
            <div class="no-results" *ngIf="filteredClients.length === 0">
              Aucun client trouvé
            </div>
          </div>
        </div>

        <!-- Selected Client Display -->
        <div class="selected-org" *ngIf="selectedClient">
  <div class="org-card-mini">
    <div class="org-header">
      <mat-icon class="org-icon">business</mat-icon>
      <div class="org-info">
        <h3>{{selectedClient.Name}}</h3>
        <p>{{selectedClient.Organisation?.Nom || 'Aucune organisation'}}</p>
      </div>
      <button class="clear-btn" (click)="selectedClient = null; searchTerm = ''">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <div class="org-stats">
      <div class="stat-item">
        <mat-icon class="stat-icon">people</mat-icon>
        <span>{{selectedClient.CompanySize || 0}} employés</span>
      </div>
      <div class="stat-item">
        <mat-icon class="stat-icon">devices</mat-icon>
        <span>{{selectedClient.ActiveEquipment || 0}} équipements actifs</span>
      </div>
      <div class="stat-item">
        <mat-icon class="stat-icon">location_city</mat-icon>
        <span>{{selectedClient.Sites?.length || 0}} sites</span>
      </div>
    </div>
  </div>
</div>
      </div>

      <!-- Date Range Selection -->
      <div class="form-section">
        <label for="" class="form-label">Période du Rapport *</label>
        <div class="date-range-container">
                <mat-form-field class="date-field">
                <mat-label>Date de début</mat-label>
                <input matInput [matDatepicker]="startPicker" [(ngModel)]="startDate" readonly>
                <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
                <mat-datepicker #startPicker></mat-datepicker>
                </mat-form-field>

                <div class="date-separator">
                <mat-icon>arrow_forward</mat-icon>
                </div>

                <mat-form-field class="date-field">
                <mat-label>Date de fin</mat-label>
                <input matInput [matDatepicker]="endPicker" [(ngModel)]="endDate" readonly>
                <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
                <mat-datepicker #endPicker></mat-datepicker>
                </mat-form-field>
        </div>
      </div>

      <!-- Export Format Selection -->
      <div class="form-section">
        <label for="" class="form-label">Format d'Export *</label>
        <div class="format-selection">
          <div onkeypress="" class="format-option" 
               [class.selected]="exportFormat === 'PDF'"
               (click)="exportFormat = 'PDF'">
            <mat-icon class="format-icon">picture_as_pdf</mat-icon>
            <div class="format-info">
              <h4>PDF</h4>
              <p>Rapport formaté et imprimable</p>
            </div>
            <mat-icon class="check-icon" *ngIf="exportFormat === 'PDF'">check_circle</mat-icon>
          </div>
          
          <div onkeypress="" class="format-option" 
               [class.selected]="exportFormat === 'CSV'"
               (click)="exportFormat = 'CSV'">
            <mat-icon class="format-icon">table_chart</mat-icon>
            <div class="format-info">
              <h4>CSV</h4>
              <p>Données pour analyse Excel</p>
            </div>
            <mat-icon class="check-icon" *ngIf="exportFormat === 'CSV'">check_circle</mat-icon>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="form-actions">
        <button class="btn btn-secondary" (click)="resetForm()">
          <mat-icon>refresh</mat-icon>
          Réinitialiser
        </button>
        
        <button class="btn btn-primary" 
                [disabled]="!isFormValid || isLoading"
                (click)="generateReport()">
          <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
          <mat-icon *ngIf="!isLoading">assessment</mat-icon>
          {{isLoading ? 'Génération...' : 'Générer le Rapport'}}
        </button>
      </div>
    </div>
  </div>

  <!-- Report Preview -->
  <div class="preview-container" *ngIf="showPreview && generatedReport">
<div class="preview-header">
  <button class="btn btn-secondary" (click)="showPreview = false">
    <mat-icon>arrow_back</mat-icon>
    Retour au formulaire
  </button>
  
   <h2 class="preview-title">
    <mat-icon>visibility</mat-icon>
    Aperçu du Rapport
  </h2> 
  
  <div class="action-buttons">
    <!-- <button class="btn btn-secondary" (click)="previewPDF()">
      <mat-icon>pageview</mat-icon>
      Aperçu PDF
    </button> -->
    <button class="btn btn-primary" (click)="downloadReport()">
      <mat-icon>download</mat-icon>
      Télécharger {{exportFormat}}
    </button>
  </div>
</div>

    <div class="report-preview">
      <!-- Report Header -->
      <div class="report-header">
        <div class="report-title">
          <h1>RAPPORT ÉNERGÉTIQUE</h1>
          <div class="report-period">
            {{startDate | date:'dd/MM/yyyy'}} - {{endDate | date:'dd/MM/yyyy'}}
          </div>
        </div>
        <div class="report-logo">
          <mat-icon class="logo-icon">eco</mat-icon>
        </div>
      </div>

      <!-- Client Info -->
      <div class="report-section">
        <h2 class="section-header">
          <mat-icon>business</mat-icon>
          Informations du Client
        </h2>
        <div class="info-grid">
          <div class="info-item">
            <label for="">Nom:</label>
            <span>{{generatedReport.client.Name}}</span>
          </div>
          <div class="info-item">
            <label for="">Organisation:</label>
            <span>{{generatedReport.client.Organisation?.Nom || 'N/A'}}</span>
          </div>
          <div class="info-item">
            <label for="">Email:</label>
            <span>{{generatedReport.client.ContactEmail || 'N/A'}}</span>
          </div>
          <div class="info-item">
            <label for="">Téléphone:</label>
            <span>{{generatedReport.client.PhoneNumber || 'N/A'}}</span>
          </div>
          <div class="info-item">
            <label for="">Adresse:</label>
            <span>{{generatedReport.client.Address || 'N/A'}}</span>
          </div>
          <div class="info-item">
            <label for="">Taille de l'entreprise:</label>
            <span>{{generatedReport.client.CompanySize || 0}} employés</span>
          </div>
          <div class="info-item">
            <label for="">Équipements actifs:</label>
            <span>{{generatedReport.client.ActiveEquipment || 0}}</span>
          </div>
          <div class="info-item" *ngIf="generatedReport.client.Sites && generatedReport.client.Sites.length > 0">
            <label for="">Sites:</label>
            <span>{{generatedReport.client.Sites.length}} site(s)</span>
          </div>
        </div>
      </div>

      <!-- Summary Statistics -->
      <div class="report-section">
        <h2 class="section-header">
          <mat-icon>analytics</mat-icon>
          Résumé Énergétique
        </h2>
        <div class="summary-grid">
          <div class="summary-card">
            <div class="summary-icon positive">
              <mat-icon>bolt</mat-icon>
            </div>
            <div class="summary-content">
              <div class="summary-value">{{generatedReport.summary.totalConsumption | number:'1.2-2'}} kWh</div>
              <div class="summary-label">Consommation Totale</div>
            </div>
          </div>
          
          <div class="summary-card">
            <div class="summary-icon warning">
              <mat-icon>attach_money</mat-icon>
            </div>
            <div class="summary-content">
              <div class="summary-value">{{generatedReport.summary.totalCost | number:'1.2-2'}} MAD</div>
              <div class="summary-label">Coût Total</div>
            </div>
          </div>
          
          <div class="summary-card">
            <div class="summary-icon positive">
              <mat-icon>trending_up</mat-icon>
            </div>
            <div class="summary-content">
              <div class="summary-value">{{generatedReport.summary.averageEfficiency | number:'1.1-1'}}%</div>
              <div class="summary-label">Efficacité Moyenne</div>
            </div>
          </div>
          
          <div class="summary-card">
            <div class="summary-icon negative">
              <mat-icon>cloud</mat-icon>
            </div>
            <div class="summary-content">
              <div class="summary-value">{{generatedReport.summary.totalCO2 | number:'1.2-2'}} kg</div>
              <div class="summary-label">Émissions CO2</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Monthly Data Table -->
      <div class="report-section">
        <h2 class="section-header">
          <mat-icon>table_chart</mat-icon>
          Données Mensuelles
        </h2>
        <div class="data-table">
          <table>
            <thead>
              <tr>
                <th>Mois</th>
                <th>Consommation (kWh)</th>
                <th>Coût (MAD)</th>
                <th>Efficacité (%)</th>
                <th>CO2 (kg)</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let data of generatedReport.energyData">
                <td>{{data.month}}</td>
                <td>{{data.consumption | number:'1.2-2'}}</td>
                <td>{{data.cost | number:'1.2-2'}}</td>
                <td>{{data.efficiency | number:'1.1-1'}}</td>
                <td>{{data.co2Emissions | number:'1.2-2'}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Savings Section -->
      <div class="report-section">
        <h2 class="section-header">
          <mat-icon>savings</mat-icon>
          Économies Réalisées
        </h2>
        <div class="savings-highlight">
          <div class="savings-amount">{{generatedReport.summary.savings | number:'1.2-2'}} MAD</div>
          <div class="savings-description">Économies estimées pour la période sélectionnée</div>
        </div>
      </div>
    </div>
  </div>
</div>