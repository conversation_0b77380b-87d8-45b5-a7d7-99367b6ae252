<div class="create-form-card">
  <div class="form-container">
    <form>
      <div class="form-grid">
        <div class="form-group">
          <label for="name">Nom<span class="required">*</span></label>
          <input
            id="name"
            type="text"
            formControlName="name"
            value="{{ controller?.HostName }}"
            disabled
          />
        </div>
        <div class="form-group">
          <label for="idlicence">Modèle <span class="required">*</span></label>

          <input
            id="name"
            type="text"
            formControlName="name"
            value="{{ controller?.Model }}"
            disabled
          />
        </div>
        <div class="form-group">
          <label for="geographicZone">N° de Série</label>
          <input
            id="geographicZone"
            formControlName="geographicZone"
            value="{{ controller?.SerialNumber }}"
            disabled
          />
        </div>

        <div class="form-group">
          <label for="commercialCondition">Adresse MAC</label>
          <input
            id="commercialCondition"
            formControlName="commercialCondition"
            value="{{ controller?.MacAddress }}"
            disabled
          />
        </div>
        <div class="form-group">
          <label for="installationDate">Adresse IP</label>
          <input
            id="installationDate"
            class="form-control"
            [value]="
              controller?.IpAddress
            "
            disabled
          />
        </div>

        <div class="form-group">
          <label for="lastConnection">Base Topic</label>
          <input
            id="lastConnection"
            class="form-control"
            [value]="controller?.BaseTopic"
            disabled
          />
        </div>

        <div class="form-group">
          <label for="status">Statut <span class="required">*</span></label>
          <input
            id="eventType"
            formControlName="eventType"
            disabled
            [value]="controller?.State ? 'Actif' : 'Inactif'"
          />
        </div>
        <div class="form-group">
          <label for="status">Date d'installation <span class="required">*</span></label>
          <input
            id="eventType"
            formControlName="eventType"
            disabled
            [value]="controller?.InstallationDate | date : 'dd-MM-yyyy'"
          />
        </div>
      </div>
    </form>
  </div>
</div>
