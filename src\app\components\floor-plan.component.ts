import { Component, ElementRef, EventEmitter, HostListener, Input, OnChanges, Output, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Device, DeviceType } from '../core/models/device.model';
import { DeviceIconComponent } from '../components/device-icon.component';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-floor-plan',
  standalone: true,
  imports: [CommonModule, DeviceIconComponent, MatIconModule],
  template: `
    <div class="floor-plan-wrapper">
      <!-- Move "Désactiver le mode appairage" button above the image -->
      <button *ngIf="pairingMode" (click)="disablePairingMode()" class="pairing-mode-btn">Désactiver le mode appairage</button>

      <div class="floor-plan" #floorPlanContainer [class.pairing-mode]="pairingMode">
        <div class="floor-plan-background">
          <img [src]="floorPlanImage || site.floorPlan?.imageUrl || 'assets/plans/salle vide.avif'"
               alt="Plan d'étage"
               class="floor-plan-image"
               (load)="onImageLoad()" />        </div>

        @if (devices && devices.length > 0) {
          @for (device of devices; track device.id) {
            <div
              class="device-wrapper"
              [class.dragging]="isDragging && selectedDeviceId === device.id"
              [class.selected]="selectedDeviceId === device.id"
              [style.left.px]="device.position.x"
              [style.top.px]="device.position.y"
              [attr.data-device-id]="device.id"
              (mousedown)="startDragging($event, device.id)"
              (click)="onDeviceClick(device.id)"
              [class.pairing-highlight]="pairingMode && device.type === DeviceType.CONTROLLER">

              <app-device-icon
                [deviceType]="device.type"
                [status]="device.status">
              </app-device-icon>

              <span class="device-name">{{ device.name }}</span>
            </div>
          }
        } @else {
          <div class="no-devices">Aucun appareil disponible pour ce site</div>
        }
      </div>

      <div class="floor-plan-controls">
        <div class="zoom-controls">
          <button (click)="zoomIn()">+</button>
          <button (click)="zoomOut()">-</button>
          <button (click)="resetZoom()" class="reset-btn">
                <mat-icon>refresh</mat-icon>
            </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .floor-plan-wrapper {
      position: relative;
      width: 100%;
      min-height: 300px; /* Minimum height to ensure content is visible */
      overflow: hidden;
      background-color: #f5f5f5;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
    }

    .floor-plan {
      position: relative;
      width: 100%;
      height: 0; /* Initially 0, will be set dynamically */
      padding-top: 0; /* Will be set based on aspect ratio */
      overflow: auto;
      cursor: grab;
      transform-origin: 0 0;
      flex-grow: 1;
    }

    .floor-plan.pairing-mode {
      background-color: #ffeef0;
    }

    .floor-plan-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .floor-plan-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain; /* Ensure full image is visible */
      display: block; /* Remove any default inline spacing */
    }

    .device-wrapper {
      position: absolute;
      display: flex;
      flex-direction: column;
      align-items: center;
      transform: translate(-50%, -50%);
      padding: 5px;
      border-radius: 50%;
      z-index: 10;
      cursor: grab;
      transition: transform 0.2s;
    }

    .device-wrapper.selected {
      z-index: 20;
      transform: translate(-50%, -50%) scale(1.1);
    }

    .device-wrapper.dragging {
      cursor: grabbing;
    }

    .device-wrapper.pairing-highlight {
      animation: pulse 1.5s infinite;
    }

    @keyframes pulse {
      0% { transform: translate(-50%, -50%) scale(1); }
      50% { transform: translate(-50%, -50%) scale(1.1); }
      100% { transform: translate(-50%, -50%) scale(1); }
    }

    .device-name {
      font-size: 12px;
      white-space: nowrap;
      margin-top: 4px;
      background-color: rgba(255, 255, 255, 0.8);
      padding: 2px 5px;
      border-radius: 4px;
    }

    .floor-plan-controls {
      position: relative;
      padding: 10px;
      background-color: #fff;
      border-top: 1px solid #ddd;
      display: flex;
      justify-content: center;
      gap: 10px;
    }

    .zoom-controls {
      display: flex;
      gap: 5px;
    }

    .zoom-controls button {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: white;
      border: 1px solid #ddd;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .zoom-controls button:hover {
      background-color: #f0f0f0;
      transform: scale(1.1);
    }

    .reset-btn {
      width: 40px; /* Make it square like other buttons */
      height: 40px;
      border-radius: 50%; /* Make it circular like other buttons */
      background-color: var(--primary);
      color: black;
      border: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .reset-btn:hover {
      background-color: #45a049;
      transform: translateY(-2px);
      box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
    }

    .reset-btn mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
      line-height: 20px;
    }

    .pairing-mode-btn {
      position: absolute;
      top: 10px;
      right: 10px;
      padding: 0.5rem 1.5rem;
      border-radius: 20px;
      background-color: #e74c3c;
      color: white;
      border: none;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
      z-index: 100;
    }

    .pairing-mode-btn:hover {
      background-color: #c0392b;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .no-devices {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #7f8c8d;
      font-style: italic;
    }
  `]
})
export class FloorPlanComponent implements OnChanges, AfterViewInit {
  @Input() site!: any;
  @Input() pairingMode = false;
  @Input() devices: Device[] = [];
  @Output() devicePositionChanged = new EventEmitter<{ deviceId: string, position: { x: number, y: number } }>();
  @ViewChild('floorPlanContainer') floorPlanContainer!: ElementRef;
    @Input() floorPlanImage?: string;  // Add this

  isDragging = false;
  selectedDeviceId: string | null = null;
  dragStartX = 0;
  dragStartY = 0;
  scale = 1;
  imageAspectRatio: number | null = null;

  DeviceType = DeviceType;

  ngOnChanges(): void {
    if (this.pairingMode) {
      this.selectedDeviceId = null;
      this.isDragging = false;
    }
    if (!this.devices || this.devices.length === 0) {
      console.warn('Aucun appareil fourni pour le plan d\'étage:', this.site?.id);
    }
  }

  ngAfterViewInit(): void {
    if (this.site.floorPlan?.imageUrl) {
      const img = new Image();
      img.src = this.site.floorPlan.imageUrl;
      img.onload = () => {
        this.imageAspectRatio = img.width / img.height;
        this.updateContainerSize();
      };
    }
  }

  onImageLoad(): void {
    const img = this.floorPlanContainer.nativeElement.querySelector('img') as HTMLImageElement;
    if (img && !this.imageAspectRatio) {
      this.imageAspectRatio = img.naturalWidth / img.naturalHeight;
      this.updateContainerSize();
    }
  }

  private updateContainerSize(): void {
    if (this.imageAspectRatio && this.floorPlanContainer) {
      const container = this.floorPlanContainer.nativeElement;
      const wrapper = container.parentElement;
      if (wrapper) {
        container.style.paddingTop = `${(1 / this.imageAspectRatio) * 100}%`;
        container.style.height = 'auto'; // Allow height to adjust dynamically
      }
    }
  }

  startDragging(event: MouseEvent, deviceId: string): void {
    if (this.pairingMode || !this.devices) return;

    this.isDragging = true;
    this.selectedDeviceId = deviceId;
    this.dragStartX = event.clientX;
    this.dragStartY = event.clientY;

    event.preventDefault();
  }

  @HostListener('mousemove', ['$event'])
  onMouseMove(event: MouseEvent): void {
    if (!this.isDragging || !this.selectedDeviceId || !this.devices) return;

    const device = this.devices.find(d => d.id === this.selectedDeviceId);
    if (!device) return;

    const dx = (event.clientX - this.dragStartX) / this.scale;
    const dy = (event.clientY - this.dragStartY) / this.scale;

    const newX = device.position.x + dx;
    const newY = device.position.y + dy;

    const deviceIndex = this.devices.findIndex(d => d.id === this.selectedDeviceId);
    if (deviceIndex !== -1) {
      this.devices[deviceIndex].position.x = newX;
      this.devices[deviceIndex].position.y = newY;
    }

    this.dragStartX = event.clientX;
    this.dragStartY = event.clientY;
  }

  @HostListener('mouseup')
  @HostListener('mouseleave')
  stopDragging(): void {
    if (this.isDragging && this.selectedDeviceId && this.devices) {
      const device = this.devices.find(d => d.id === this.selectedDeviceId);
      if (device) {
        this.devicePositionChanged.emit({
          deviceId: this.selectedDeviceId,
          position: { x: device.position.x, y: device.position.y }
        });
      }
      this.isDragging = false;
    }
  }

  zoomIn(): void {
    if (this.scale < 2) {
      this.scale += 0.1;
      this.updateScale();
    }
  }

  zoomOut(): void {
    if (this.scale > 0.5) {
      this.scale -= 0.1;
      this.updateScale();
    }
  }

  resetZoom(): void {
    this.scale = 1;
    this.updateScale();
  }

  private updateScale(): void {
    if (this.floorPlanContainer) {
      this.floorPlanContainer.nativeElement.style.transform = `scale(${this.scale})`;
    }
  }

  onDeviceClick(deviceId: string): void {
    if (this.pairingMode) {
      this.disablePairingMode();
    }
  }

  disablePairingMode(): void {
    this.pairingMode = false;
  }
}
