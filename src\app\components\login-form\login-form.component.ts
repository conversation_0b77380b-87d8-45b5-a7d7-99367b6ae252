import { Component, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { first } from 'rxjs/operators';
import { AuthService } from '@app/core/services/auth.service';
import { NgClass, NgIf } from '@angular/common';

@Component({
  selector: 'app-login-form',
  standalone: true,
  templateUrl: './login-form.component.html',
  styleUrls: ['./login-form.component.css'],
  imports: [NgClass, NgIf, ReactiveFormsModule],
})
export class LoginFormComponent implements OnInit {
  form!: FormGroup;
  loading = false;
  submitted = false;
  error?: string;

  constructor(
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private accountService: AuthService
  ) {
    // ✅ Nouvelle vérification avec getCurrentUser()
    if (this.accountService.isLoggedIn()) {
      this.router.navigate(['/accueil']);
    }
  }

  ngOnInit() {
    this.form = this.formBuilder.group({
      username: ['', [Validators.required, Validators.email]],
      password: ['', Validators.required],
      remember: [true],
    });
  }

  get f() {
    return this.form.controls;
  }

  onSubmit() {
    this.submitted = true;
    this.error = '';

    if (this.form.invalid) {
      return;
    }

    this.loading = true;

    this.accountService
      .login(this.f['username'].value, this.f['password'].value)
      .pipe(first())
      .subscribe({
        next: () => {
          const redirect = this.route.snapshot.queryParams['returnUrl'] || '/accueil';
          this.router.navigateByUrl(redirect);
        },
        error: (error) => {
          this.error = this.translateError(error.message || error);
          this.loading = false;
        },
      });
  }

  private translateError(error: string): string {
    const errorTranslations: { [key: string]: string } = {
      'Invalid credentials': 'Identifiants invalides',
      'User not found': 'Utilisateur non trouvé',
      'Invalid email or password': 'Email ou mot de passe invalide',
      'Network error': 'Erreur de réseau',
      'Server error': 'Erreur du serveur',
    };
    return errorTranslations[error] || error;
  }
}
