import { Capteur } from './capteur';
import { Controller } from './controller';
import { Local } from './local';
import { AuditModel } from './models-audit/audit-model';
import { Transaction } from './transaction';
export class Log extends AuditModel {
  Message!: string;
  IdTransaction!: string;
  Transaction!: Transaction;
  ControllerId!: string;
  Controller!: Controller;
  IdCapteur!: string;
  Capteur!: Capteur;
  LocalId!: string;
  Local!: Local;
}
