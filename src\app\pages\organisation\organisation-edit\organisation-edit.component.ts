// organisation-edit.component.ts
import { Component, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import {
  FormsModule,
  ReactiveFormsModule,
  FormGroup,
  FormControl,
  Validators,
} from '@angular/forms';
import { Client } from '../../../core/models/client';
import { Organisation } from '../../../core/models/organisation';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { OrganisationApiService } from '@app/core/services/administrative/organisation.service';
import { Location } from '@angular/common';
import { Site } from '@app/core/models/site';
import { FormaValidationService } from '@app/shared/services/forma-validation.service';
import { formElements } from './form-element';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { NgToastComponent, NgToastService, TOAST_POSITIONS } from 'ng-angular-popup';

@Component({
  selector: 'app-organisation-edit',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    NgToastComponent
  ],
  templateUrl: './organisation-edit.component.html',
  styleUrls: ['./organisation-edit.component.css'],
})
export class OrganisationEditComponent implements OnInit {
  client: Client | null = null;
  organisations: Organisation[] = [];
  isLoading = true;
  uploadedLogo: File | undefined;
  showMoreFields: boolean = false;
  showErrorMessages: string[] = [];
  datePipe = new DatePipe('en-US');
  date: string = "";
  event: Event | undefined;
  base64Image: string = "";
  TOAST_POSITIONS = TOAST_POSITIONS;

  constructor(
    readonly route: ActivatedRoute,
    readonly router: Router,
    readonly clientService: ClientApiService,
    readonly organisationService: OrganisationApiService,
    private location: Location,
    private formValidationService: FormaValidationService,
    private readonly dialog: MatDialog,
    private toast: NgToastService
  ) { }

  ngOnInit(): void {
    this.loadOrganisations();
    this.loadClient();
  }

  loadOrganisations(): void {
    this.organisationService.getAll().subscribe({
      next: (organisations: Organisation[]) => {
        this.organisations = organisations;
      },
      error: (error) => {
        console.error('Error loading organisations:', error);
      },
    });
  }

  formatDate(dateString: string) {
    const date = new Date(dateString);
    const x = date.toISOString().split('T')[0];
    const [year, month, day] = x.split('-');
    this.date = `${year}-${month}-${day}`; // Extracts 'YYYY-MM-DD'
  }
  // formatDateToInput(dateString: string): string {
  //   const date = new Date(dateString);
  //   return date.toISOString().split('T')[0]; // Extracts 'YYYY-MM-DD'
  // }
  dateString: string | undefined = "";

  loadClient(): void {
    this.isLoading = true;
    const clientId = this.route.snapshot.paramMap.get('id');

    if (clientId) {
      this.clientService.getOne(clientId).subscribe({
        next: (data) => {
          this.client = data;
          this.dateString = this.datePipe.transform(this.client?.CompanyCreationDate, 'MM-dd-yyyy')?.toString();
          this.isLoading = false;
          this.formatDate(this.client?.CompanyCreationDate.toString());
        },
        error: (error: any) => {
          console.error(
            'Erreur lors du chargement des détails du client:',
            error
          );
          this.isLoading = false;
        },
      });
    } else {
      console.error('No client ID found in route');
      this.router.navigate(['/clients']);
    }
  }

  onFileSelecteds(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      const reader = new FileReader();

      reader.onload = () => {
        const base64String = reader.result as string;
        if (base64String) {
          const splitter = base64String.split(',')[1];
          // this.uploadedLogo = splitter.toString();
        }
        console.log("Image", this.uploadedLogo);
      };

      reader.onerror = (error) => {
        console.error('Error reading file:', error);
      };
    }
  }

  onFileSelected(event: any): void {
    const file: File = event.target.files[0];
    if (file) {
      this.uploadedLogo = file;
      console.log('File selected:', file.name);

      const reader = new FileReader();
      reader.onload = () => {
        var base64String = reader.result as string;

        // Example: Assign it to a variable in your model
        this.base64Image = base64String.split(",")[1];
      };
      reader.onerror = (error) => {
        console.error('Error reading file:', error);
      };

      reader.readAsDataURL(file); // This reads the file as Base64 (data URL)
    } else {
      this.uploadedLogo = undefined;
    }
  }

  async onSubmit() {
    this.showErrorMessages = this.formValidationService.validate(this.client, formElements);
    if (this.client && this.showErrorMessages.length == 0) {
      this.client.CompanyCreationDate = new Date(this.date) ;
      this.client.ClientLogo = this.base64Image != "" ? this.base64Image : this.client.ClientLogo;
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        width: '400px',
        data: {
          title: 'Confirmation de modification',
          message: 'Êtes-vous sûr de vouloir modifier ce client ?',
          icon: 'warning'
        }
      });
      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.performUpdate();
        }
      });
    }
  }

  public performUpdate(): void {
    this.clientService.update(this.client).subscribe({
      next: (response) => {
        this.showSuccess("Le Client a été modifié avec succes", "Information");
        setTimeout(() => {
          this.goBack();
        }, 3000);
      },
      error: (error: any) => {
        this.showError("'Erreur lors de la mise à jour du client:'", "Erreur");
      },
    });
  }

  goBack(): void {
    this.location.back();
  }

  navigateToList(): void {
    this.router.navigate(['/organisation-management']);
  }
  
  public showSuccess(message: string, title: string) {
    this.toast.info(message, title, 3000, false);
  }

  public showError(message: string, title: string) {
    this.toast.warning(message, title, 3000, false);
  }
}
