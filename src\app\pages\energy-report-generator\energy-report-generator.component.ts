import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatNativeDateModule,MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { saveAs } from 'file-saver';
import { ClientApiService } from '../../core/services/administrative/client.service';
import { Client } from '../../core/models/client';


// Remove the old Organization interface as we'll use Client model directly

export interface EnergyData {
  month: string;
  consumption: number;
  cost: number;
  efficiency: number;
  co2Emissions: number;
}

interface ExtendedJsPDF extends jsPDF {
  lastAutoTable?: {
    finalY: number;
  };
}

export interface ReportData {
  client: Client;  // Changed from organization to client
  startDate: Date;
  endDate: Date;
  energyData: EnergyData[];
  summary: {
    totalConsumption: number;
    totalCost: number;
    averageEfficiency: number;
    totalCO2: number;
    savings: number;
  };
}

@Component({
  selector: 'app-energy-report-generator',
  templateUrl: './energy-report-generator.component.html',
  styleUrls: ['./energy-report-generator.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatDatepickerModule,
    MatInputModule,
    MatFormFieldModule,
    MatNativeDateModule,
    MatSelectModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  providers: [
    { 
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: 'DD/MM/YYYY',
        },
        display: {
          dateInput: 'DD/MM/YYYY',
          monthYearLabel: 'MMM YYYY',
          dateA11yLabel: 'DD/MM/YYYY',
          monthYearA11yLabel: 'MMMM YYYY',
        },
      },
    },
    { provide: MAT_DATE_LOCALE, useValue: 'fr-FR' }
  ]
})
export class EnergyReportGeneratorComponent implements OnInit {
  selectedClient: Client | null = null;
  startDate: Date | null = null;
  endDate: Date | null = null;
  exportFormat: 'PDF' | 'CSV' = 'PDF';
  searchTerm: string = '';
  isLoading: boolean = false;
  showPreview: boolean = false;
  generatedReport: ReportData | null = null;
  clients: Client[] = [];

  constructor(
    public readonly clientService: ClientApiService,
    readonly snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    // Initialize with current date range (last 3 months)
    this.endDate = new Date();
    this.startDate = new Date();
    this.startDate.setMonth(this.startDate.getMonth() - 3);
    this.loadClients();
  }

  private loadClients(): void {
    this.clientService.getAll().subscribe({
      next: (clients: Client[]) => {
        this.clients = clients;
      },
      error: (error: any) => {
        this.showError('Erreur lors du chargement des clients');
        console.error('Error loading clients:', error);
      }
    });
  }

  get filteredClients(): Client[] {
    if (!this.searchTerm) return this.clients;
    return this.clients.filter(client =>
      client.Name?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
      client.Organisation?.Nom?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
      client.CompanyType?.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  get isFormValid(): boolean {
    return !!(this.selectedClient && this.startDate && this.endDate &&
             this.startDate <= this.endDate);
  }

  onClientSelect(client: Client): void {
    this.selectedClient = client;
    this.searchTerm = client.Name;
  }

  validateDates(): boolean {
    if (!this.startDate || !this.endDate) {
      this.showError('Veuillez sélectionner les dates de début et de fin');
      return false;
    }
    
    if (this.startDate > this.endDate) {
      this.showError('La date de début doit être antérieure à la date de fin');
      return false;
    }

    const monthsDiff = (this.endDate.getFullYear() - this.startDate.getFullYear()) * 12 + 
                      (this.endDate.getMonth() - this.startDate.getMonth());
    
    if (monthsDiff > 12) {
      this.showError('La période ne peut pas dépasser 12 mois');
      return false;
    }

    return true;
  }

  generateDummyEnergyData(): EnergyData[] {
    const data: EnergyData[] = [];
    const current = new Date(this.startDate!);
    const end = new Date(this.endDate!);

    while (current <= end) {
      // Use client's active equipment count or company size as base for consumption calculation
      const baseConsumption = (this.selectedClient!.ActiveEquipment || this.selectedClient!.CompanySize || 100) * 10; // 10 kWh per equipment/employee
      const variation = (Math.random() - 0.5) * 0.3; // ±15% variation

      data.push({
        month: current.toLocaleDateString('fr-FR', { year: 'numeric', month: 'long' }),
        consumption: Math.round(baseConsumption * (1 + variation) * 100) / 100,
        cost: Math.round(baseConsumption * (1 + variation) * 0.8 * 100) / 100, // 0.8 MAD per kWh
        efficiency: Math.round((85 + Math.random() * 10) * 100) / 100, // 85-95% efficiency
        co2Emissions: Math.round(baseConsumption * (1 + variation) * 0.5 * 100) / 100 // 0.5 kg CO2 per kWh
      });

      current.setMonth(current.getMonth() + 1);
    }

    return data;
  }

generateReport(): void {
    if (!this.isFormValid || !this.validateDates()) return;

    this.isLoading = true;

    const energyData = this.generateDummyEnergyData();

    this.generatedReport = {
      client: this.selectedClient!,
      startDate: this.startDate!,
      endDate: this.endDate!,
      energyData,
      summary: {
        totalConsumption: energyData.reduce((sum, data) => sum + data.consumption, 0),
        totalCost: energyData.reduce((sum, data) => sum + data.cost, 0),
        averageEfficiency: energyData.reduce((sum, data) => sum + data.efficiency, 0) / energyData.length,
        totalCO2: energyData.reduce((sum, data) => sum + data.co2Emissions, 0),
        savings: Math.round(Math.random() * 1000 + 500)
      }
    };

    this.showPreview = true;
    this.showSuccess('Rapport généré avec succès!');
    this.isLoading = false;
  }

  downloadReport(): void {
    if (!this.generatedReport) return;

    if (this.exportFormat === 'PDF') {
      this.downloadPDF();
    } else {
      this.downloadCSV();
    }
  }

private downloadPDF(): void {
  const doc = new jsPDF() as ExtendedJsPDF;
  const pageWidth = doc.internal.pageSize.getWidth();

  // === Couleurs personnalisées ===
  const primary: [number, number, number] = [73, 179, 142]; // #49b38e
  const textColor: [number, number, number] = [74, 85, 104]; // #4a5568
  const accent: [number, number, number] = [37, 97, 169]; // #2561a9
  const warning: [number, number, number] = [242, 158, 76]; // #f29e4c
  const highlight: [number, number, number] = [245, 215, 131]; // #f5d783

  // === Titre Principal ===
  doc.setFontSize(26);
  doc.setTextColor(primary[0], primary[1], primary[2]);
  doc.text(' RAPPORT ÉNERGÉTIQUE', pageWidth / 2, 20, { align: 'center' });

  // === Infos Organisation ===
  doc.setFontSize(12);
  doc.setTextColor(textColor[0], textColor[1], textColor[2]);
  const lineHeight = 8;
  const startY = 35;

  doc.text(` Client : ${this.generatedReport!.client.Name}`, 20, startY);
  doc.text(` Organisation : ${this.generatedReport!.client.Organisation?.Nom || 'N/A'}`, 20, startY + lineHeight);
  doc.text(` Période : ${this.startDate!.toLocaleDateString('fr-FR')} - ${this.endDate!.toLocaleDateString('fr-FR')}`, 20, startY + lineHeight * 2);

  // === Ligne séparatrice ===
  doc.setDrawColor(primary[0], primary[1], primary[2]);
  doc.setLineWidth(0.5);
  doc.line(20, startY + lineHeight * 3.2, pageWidth - 20, startY + lineHeight * 3.2);

  // === Résumé ===
  doc.setFontSize(16);
  doc.setTextColor(accent[0], accent[1], accent[2]);
  doc.text(' Résumé', 20, startY + lineHeight * 4.5);

  const summaryData = [
    ['Consommation totale', `${this.generatedReport!.summary.totalConsumption.toFixed(2)} kWh`],
    ['Coût total', `${this.generatedReport!.summary.totalCost.toFixed(2)} MAD`],
    ['Efficacité moyenne', `${this.generatedReport!.summary.averageEfficiency.toFixed(2)} %`],
    ['Émissions CO2', `${this.generatedReport!.summary.totalCO2.toFixed(2)} kg`],
    ['Économies réalisées', `${this.generatedReport!.summary.savings.toFixed(2)} MAD`]
  ];

  autoTable(doc, {
    startY: startY + lineHeight * 5.5,
    head: [[' Métrique', ' Valeur']],
    body: summaryData,
    theme: 'grid',
    styles: {
      fontSize: 11,
      cellPadding: 4,
      textColor: textColor,
    },
    headStyles: {
      fillColor: primary,
      textColor: 255,
      fontStyle: 'bold'
    },
    alternateRowStyles: {
      fillColor: highlight
    }
  });

  // === Données Mensuelles ===
  const secondTableY = (doc.lastAutoTable?.finalY ?? 120) + 15;
  doc.setFontSize(16);
  doc.setTextColor(accent[0], accent[1], accent[2]);
  doc.text(' Données Mensuelles', 20, secondTableY);

  const monthlyData = this.generatedReport!.energyData.map(data => [
    data.month,
    `${data.consumption.toFixed(2)} kWh`,
    `${data.cost.toFixed(2)} MAD`,
    `${data.efficiency.toFixed(2)} %`,
    `${data.co2Emissions.toFixed(2)} kg`
  ]);

  autoTable(doc, {
    startY: secondTableY + 5,
    head: [['Mois', 'Consommation', 'Coût', 'Efficacité', 'CO2']],
    body: monthlyData,
    theme: 'grid',
    styles: {
      fontSize: 11,
      cellPadding: 4,
      textColor: textColor
    },
    headStyles: {
      fillColor: primary,
      textColor: 255,
      fontStyle: 'bold'
    },
    alternateRowStyles: {
      fillColor: [248, 248, 248]
    }
  });

  // === Export PDF ===
  doc.save('rapport-energetique.pdf');
}



  private downloadCSV(): void {
    const headers = ['Mois', 'Consommation (kWh)', 'Coût (MAD)', 'Efficacité (%)', 'Émissions CO2 (kg)'];
    
    let csv = headers.join(',') + '\n';
    
    // Add client info
    csv += `Client,${this.generatedReport!.client.Name}\n`;
    csv += `Organisation,${this.generatedReport!.client.Organisation?.Nom || 'N/A'}\n`;
    csv += `Période,${this.startDate!.toLocaleDateString('fr-FR')} - ${this.endDate!.toLocaleDateString('fr-FR')}\n\n`;
    
    // Add summary
    csv += 'RÉSUMÉ\n';
    csv += `Consommation totale,${this.generatedReport!.summary.totalConsumption.toFixed(2)} kWh\n`;
    csv += `Coût total,${this.generatedReport!.summary.totalCost.toFixed(2)} MAD\n`;
    csv += `Efficacité moyenne,${this.generatedReport!.summary.averageEfficiency.toFixed(2)}%\n`;
    csv += `Émissions CO2,${this.generatedReport!.summary.totalCO2.toFixed(2)} kg\n`;
    csv += `Économies réalisées,${this.generatedReport!.summary.savings.toFixed(2)} MAD\n\n`;
    
    // Add monthly data
    csv += 'DONNÉES MENSUELLES\n';
    csv += headers.join(',') + '\n';
    this.generatedReport!.energyData.forEach(data => {
      csv += `${data.month},${data.consumption.toFixed(2)},${data.cost.toFixed(2)},${data.efficiency.toFixed(2)},${data.co2Emissions.toFixed(2)}\n`;
    });

    // Create and download file
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const fileName = `rapport-energetique-${this.selectedClient!.Name}-${new Date().toISOString().split('T')[0]}.csv`;
    saveAs(blob, fileName);
    this.showSuccess(`Le rapport CSV a été téléchargé`);
  }

    // Add a preview method
  previewPDF(): void {
    const doc = new jsPDF();
    // ... (same PDF generation code as downloadPDF)
    
    // Instead of saving, open in new window
    const pdfDataUri = doc.output('datauristring');
    window.open(pdfDataUri, '_blank');
  }

  private generatePDFContent(): string {
    // Simplified PDF content (in real app, use a proper PDF library)
    return `
RAPPORT ÉNERGÉTIQUE
================

Client: ${this.generatedReport!.client.Name}
Organisation: ${this.generatedReport!.client.Organisation?.Nom || 'N/A'}
Période: ${this.startDate!.toLocaleDateString('fr-FR')} - ${this.endDate!.toLocaleDateString('fr-FR')}

RÉSUMÉ
------
Consommation totale: ${this.generatedReport!.summary.totalConsumption.toFixed(2)} kWh
Coût total: ${this.generatedReport!.summary.totalCost.toFixed(2)} MAD
Efficacité moyenne: ${this.generatedReport!.summary.averageEfficiency.toFixed(2)}%
Émissions CO2: ${this.generatedReport!.summary.totalCO2.toFixed(2)} kg
Économies réalisées: ${this.generatedReport!.summary.savings.toFixed(2)} MAD

DONNÉES MENSUELLES
-----------------
${this.generatedReport!.energyData.map(data =>
  `${data.month}: ${data.consumption.toFixed(2)} kWh, ${data.cost.toFixed(2)} MAD, ${data.efficiency.toFixed(2)}%`
).join('\n')}
    `;
  }

  private generateCSVContent(): string {
    let csv = 'Mois,Consommation (kWh),Coût (MAD),Efficacité (%),Émissions CO2 (kg)\n';
    
    this.generatedReport!.energyData.forEach(data => {
      csv += `${data.month},${data.consumption},${data.cost},${data.efficiency},${data.co2Emissions}\n`;
    });
    
    return csv;
  }

  private downloadFile(blob: Blob, filename: string): void {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    window.URL.revokeObjectURL(url);
    
    this.showSuccess(`Fichier ${filename} téléchargé avec succès!`);
  }

  resetForm(): void {
    this.selectedClient = null;
    this.searchTerm = '';
    this.startDate = null;
    this.endDate = null;
    this.exportFormat = 'PDF';
    this.showPreview = false;
    this.generatedReport = null;
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }
}