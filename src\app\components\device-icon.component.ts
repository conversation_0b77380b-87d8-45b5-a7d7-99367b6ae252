import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DeviceType, DeviceStatus } from '../core/models/device.model';

@Component({
  selector: 'app-device-icon',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="device-icon" [ngClass]="deviceClass">
      <svg *ngIf="deviceType === 'LAMP'" viewBox="0 0 24 24" width="24" height="24">
        <path d="M12 2C8.13 2 5 5.13 5 9c0 2.38 1.19 4.47 3 5.74V17c0 0.55 0.45 1 1 1h6c0.55 0 1-0.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.87-3.13-7-7-7z" [attr.fill]="getStatusColor()"/>
        <path d="M9 20h6v1c0 0.55-0.45 1-1 1h-4c-0.55 0-1-0.45-1-1v-1z" fill="#333"/>
      </svg>
      
      <svg *ngIf="deviceType === 'CLIMATE'" viewBox="0 0 24 24" width="24" height="24">
        <path d="M22 11h-4.17l3.24-3.24-1.41-1.42L15 11h-2V9l4.66-4.66-1.42-1.41L13 6.17V2h-2v4.17L7.76 2.93 6.34 4.34 11 9v2H9L4.34 6.34 2.93 7.76 6.17 11H2v2h4.17l-3.24 3.24 1.41 1.42L9 13h2v2l-4.66 4.66 1.42 1.41L11 17.83V22h2v-4.17l3.24 3.24 1.42-1.41L13 15v-2h2l4.66 4.66 1.41-1.42-3.24-3.24H22z" [attr.fill]="getStatusColor()"/>
      </svg>
      
      <svg *ngIf="deviceType === 'CONTROLLER'" viewBox="0 0 24 24" width="24" height="24">
        <path d="M2 20h20v-4H2v4zm2-3h2v2H4v-2zM2 4v4h20V4H2zm4 3H4V5h2v2zm-4 7h20v-4H2v4zm2-3h2v2H4v-2z" [attr.fill]="getStatusColor()"/>
      </svg>
      
      <div *ngIf="status === DeviceStatus.RUNNING" class="status-indicator running"></div>
      <div *ngIf="status === DeviceStatus.PAUSED" class="status-indicator paused"></div>
      <div *ngIf="status === DeviceStatus.STOPPED" class="status-indicator offline"></div>
      <div *ngIf="status === DeviceStatus.PAIRING" class="status-indicator pairing"></div>
    </div>
  `,
  styles: [`
    .device-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: white;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }
    
    .status-indicator {
      position: absolute;
      bottom: -2px;
      right: -2px;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      border: 2px solid white;
    }
    
    .status-indicator.running {
      background-color: #2ecc71;
    }
    
    .status-indicator.paused {
      background-color: #f39c12;
    }
    
    .status-indicator.offline {
      background-color: #e74c3c;
    }
    
    .status-indicator.pairing {
      background-color: #3498db;
      animation: blink 1s infinite;
    }
    
    @keyframes blink {
      0% { opacity: 0.5; }
      50% { opacity: 1; }
      100% { opacity: 0.5; }
    }
    
    .device-icon.lamp svg {
      color: #f1c40f;
    }
    
    .device-icon.climate svg {
      color: #3498db;
    }
    
    .device-icon.controller svg {
      color: #2c3e50;
    }
  `]
})
export class DeviceIconComponent {
  @Input() deviceType!: DeviceType;
  @Input() status!: DeviceStatus;

  DeviceStatus = DeviceStatus;

  get deviceClass(): Record<string, boolean> {
    const classes: Record<string, boolean> = {};
    switch (this.deviceType) {
      case DeviceType.LAMP:
        classes['lamp'] = true;
        break;
      case DeviceType.CLIMATE:
        classes['climate'] = true;
        break;
      case DeviceType.CONTROLLER:
        classes['controller'] = true;
        break;
    }
    return classes;
  }

  getStatusColor(): string {
    switch (this.status) {
      case DeviceStatus.RUNNING:
        return '#2ecc71';
      case DeviceStatus.PAUSED:
        return '#f39c12';
      case DeviceStatus.STOPPED:
        return '#bdc3c7';
      case DeviceStatus.PAIRING:
        return '#3498db';
      default:
        return '#bdc3c7';
    }
  }
}