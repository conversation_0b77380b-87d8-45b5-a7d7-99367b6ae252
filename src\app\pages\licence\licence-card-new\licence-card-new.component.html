<ngx-loading 
  [show]="isLoading" 
  [config]="{ 
    animationType: 'threeBounce', 
    backdropBackgroundColour: 'rgba(0,0,0,0.2)', 
    primaryColour: '#10b981',
    secondaryColour: '#10b981',
    tertiaryColour: '#10b981'
  }"></ngx-loading>

<div class="container">
    <div class="header improved-header">
        <div class="back-button-container" style="margin-bottom: 1rem;">
        <button 
            class="back-btn" 
            (click)="goBackToSubscriptionList()"
            onmouseover="this.style.background='#e5e7eb'"
            onmouseout="this.style.background='#f3f4f6'"
        >
            <span class="material-icons" style="font-size: 1.125rem;">arrow_back</span>
            Retour à la liste des abonnements
        </button>
        </div>
    </div>
      <!-- <div class="return-to-table-btn-container" style="margin-bottom: 16px; display: flex; justify-content: flex-start;">
      <button class="return-to-table-btn"
          style="
            background: linear-gradient(135deg, #10b981 60%, #34d399 100%);
            color: #fff;
            border: none;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(16,185,129,0.15);
            cursor: pointer;
            transition: box-shadow 0.2s;
          "
          title="Retour"
          >
    <span class="material-icons" style="font-size: 28px;">arrow_back</span>
  </button>
    </div> -->

    <div class="header improved-header">
    <h1 class="title improved-title">Choisir un client </h1>
    <p class="subtitle improved-subtitle">
      Choisir un client pour pouvez affecter une abonnement.
    </p>
    </div>
  <div class="licence-cards-container">

    <div class="client-billing-section">
      <div class="search-container">
        <div class="search-input-container">
          <div class="search-input-glass-wrapper">
            <span class="search-input-glass-icon material-icons" *ngIf="!selectedClient">search</span>
            <input
              type="text"
              class="search-input glass"
              [ngClass]="{'input-error': showChooseClientError}"
              [(ngModel)]="searchQuery"
              (focus)="showDropdown = true; filterClients()"
              (click)="showDropdown = true; filterClients()"
              (input)="filterClients()"
              *ngIf="!selectedClient"
              placeholder="Rechercher un client..."
              [readonly]="selectedClient !== null"
              autocomplete="off"
            />
            <button
              *ngIf="searchQuery && !selectedClient"
              class="search-input-glass-clear-btn"
              type="button"
              (click)="filterClients()"
              tabindex="-1"
              aria-label="Effacer"
            >
            </button>
            <div class="choose-client-error" *ngIf="showChooseClientError">
              Veuillez choisir un client d'abord.
            </div>
            <div class="dropdown-glass"
                 *ngIf="showDropdown"
                 style="max-height: 320px; overflow-y: auto;"
                 @slideInOut>
              <div 
                *ngFor="let client of filteredClients"
                class="dropdown-glass-item"
                (click)="selectClient(client)"
              >
                <div class="dropdown-glass-info">
                  <div class="dropdown-glass-name">{{ client.Name }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="search-input-container" *ngIf="selectedClient" style="display: flex; align-items: center; justify-content: center;">
          <div class="selected-client-container improved-client-card" @growIn>
            <div class="client-avatar-section">
            </div>
            <div class="client-details-section">
              <div class="client-name-row">
                <span class="client-name-large">{{ selectedClient.Name }}</span>
              </div>
              <div class="client-subtitle">Gérez les licences et options pour ce client</div>
            </div>
            <button class="clear-button improved-clear-btn" 
                    (click)="onClearSelection()" 
                    title="Désélectionner le client">
              <span class="material-icons">close</span>
            </button>
          </div>
        </div>
        <div class="select-type-frequence-payement improved-select-type-frequence-payement">
          <div class="frequence-payement improved-frequence-payement improved-frequence-inline">
            <select class="improved-select" [(ngModel)]="selectedPaymentFrequency">
              <option *ngFor="let freq of paymentFrequencies" [value]="freq.value">
                {{ freq.label }}
              </option>
            </select>
          </div>
        </div>
      </div>
</div>

<div class="license-grid improved-license-grid"
  style="overflow-x: hidden; position: relative;"
>
  <ng-container *ngIf="isAffecterCard()">
    <div 
      *ngFor="let licence of pagedLicences; let i = index" 
      class="license-card improved-license-card"
      [ngClass]="{'single-card-inner': pagedLicences.length === 1}"
    >
      <div class="card-header improved-card-header">
        <h3 class="improved-title">{{ licence.Name }}</h3>
        <div class="card-description-wrapper">
          <p class="card-description improved-card-description" [title]="licence.Description">
            {{ licence.Description }}
          </p>
        </div>
      </div>
      <div class="features-list-wrapper">
        <ul class="features-list improved-features-list">
          <li *ngFor="let option of getOptionsForLicence(licence)">
            <div class="feature-item">
              <label class="custom-checkbox">
                <input 
                  type="checkbox"
                  [checked]="isOptionChecked(licence, option.Id)"
                  (change)="toggleOption(licence, option.Id, $event)"
                  [disabled]="!selectedClient"
                />
                <span class="checkmark" [ngClass]="{
                  'checked-green': isOptionChecked(licence, option.Id),
                  'unchecked-red': !isOptionChecked(licence, option.Id)
                }">
                  <span 
                    class="material-icons verified-icon"
                    *ngIf="isOptionChecked(licence, option.Id)"
                    style="color: #10b981; opacity: 1; transform: scale(1);"
                  >check_circle</span>
                  <span 
                    class="material-icons cancel-icon"
                    *ngIf="!isOptionChecked(licence, option.Id)"
                    style="color: #ef4444; opacity: 1; transform: scale(1);"
                  >cancel</span>
                </span>
              </label>
              <div class="feature-details">
                <span class="feature-name">{{ option.Name }}</span>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <div class="license-actions">
        <button class="select-btn"
                (click)="selectLicense(licence)"
                [disabled]="!selectedClient">
          Affecter
        </button>
      </div>
    </div>
  </ng-container>
</div>

  <div class="overlay" *ngIf="showConfirmationPopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Confirmer l'affectation de la licence</h3>
        <button class="close-popup-btn" (click)="cancelLicenseApplication()">
          <span class="material-icons">close</span>
        </button>
      </div>
      
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon">help_outline</span>
          <p>Êtes-vous sûr de vouloir affecter la licence <strong>{{ selectedLicenseForConfirmation?.Name }}</strong> au client <strong>{{ selectedClient?.Name }}</strong> ?</p>
        </div>
        
        <div class="client-license-summary">
          <div class="summary-item">
            <span class="label">Client :</span>
            <div class="client-info-summary">
              <img *ngIf="selectedClient?.ClientLogo" [src]="'data:image/png;base64,' + selectedClient?.ClientLogo"
                   [alt]="selectedClient?.Name" class="client-image">
              <span>{{ selectedClient?.Name }}</span>
            </div>
          </div>
          
          <div class="summary-item">
            <span class="label">Licence :</span>
            <span class="license-name">{{ selectedLicenseForConfirmation?.Name }}</span>
          </div>

          <div class="summary-item">
            <span class="label">Total des options :</span>
            <span class="license-total">
              {{ selectedLicenseForConfirmation ? (getLicenceOptionsTotal(selectedLicenseForConfirmation) | number:'1.0-2') : '0.00' }} €
            </span>
          </div>

          <div class="summary-item" *ngIf="selectedLicenseForConfirmation">
            <span class="label">Options choisies :</span>
           <ul class="chosen-options-list" style="list-style: none; margin: 0; padding: 0; display: flex; flex-wrap: wrap; gap: 8px;">
              <li *ngFor="let option of getSelectedOptionsForConfirmation()" style="margin: 0; padding: 0; border: none; background: none;">
                <span style="
                  display: inline-block;
                  background: linear-gradient(90deg, #10b981 60%, #34d399 100%);
                  color: #fff;
                  border-radius: 18px;
                  padding: 5px 16px;
                  font-size: 1rem;
                  font-weight: 600;
                  margin-bottom: 2px;
                  letter-spacing: 0.01em;
                  box-shadow: 0 2px 8px rgba(16,185,129,0.10);
                  border: none;
                ">
                  {{ option.Name }}
                </span>
              </li>
            </ul>
          </div>

          <div class="summary-item" *ngIf="selectedLicenseForConfirmation && selectedClient && clientSubscription">
            <span class="label">Début :</span>
            <span>{{ formatDate(clientSubscription.DateDebut) }}</span>
          </div>
          <div class="summary-item" *ngIf="selectedLicenseForConfirmation && selectedClient && clientSubscription">
            <span class="label">Fin :</span>
            <span>{{ formatDate(clientSubscription.DateFin) }}</span>
          </div>
        </div>
      </div>
      
      <div class="popup-actions">
        <button class="cancel-btn" (click)="cancelLicenseApplication()">
          Annuler
        </button>
        <button class="confirm-btn" type="button" (click)="confirmLicenseApplication()">
          Oui, affecter la licence
        </button>
      </div>
    </div>
  </div>

  <div class="success-notification" *ngIf="showSuccessNotification" @slideDown>
    <div class="notification-content">
      <span class="material-icons success-icon">check_circle</span>
      <div class="notification-text">
        <h4>Licence affectée avec succès !</h4>
        <p>La licence a été affectée avec succès.</p>
      </div>
      <button class="close-notification-btn" (click)="closeSuccessNotification()">
        <span class="material-icons">close</span>
      </button>
    </div>
  </div>

  <div class="overlay" *ngIf="showNoOptionCheckedPopup" @fadeInOut>
    <div class="confirmation-popup" @slideDown>
      <div class="popup-header">
        <h3>Attention</h3>
      </div>
      <div class="popup-content">
        <div class="confirmation-message">
          <span class="material-icons confirmation-icon" style="color:#ef4444;">error_outline</span>
          <p>Vous devez cocher au moins une option.</p>
        </div>
      </div>
      <div class="popup-actions">
        <button class="confirm-btn" (click)="closeNoOptionCheckedPopup()">OK</button>
      </div>
    </div>
  </div>

 <div class="pagination-controls improved-pagination-controls">
    <button (click)="prevPage()" [disabled]="currentPage === 0" class="pagination-btn prev-btn improved-pagination-btn">
      &lt;
    </button>
    <span class="pagination-indicator improved-pagination-indicator">
      <span class="pagination-page-number improved-pagination-page-number">
        {{ currentPage + 1 }}
      </span>
    </span>
    <button (click)="nextPage()" [disabled]="currentPage === totalPages - 1" class="pagination-btn next-btn improved-pagination-btn">
      &gt;
    </button>
  </div>
  </div>
</div>