import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { Client } from '@app/core/models/client';
import { LicenceApiService } from '@app/core/services/administrative/licence.service';
import { LicenceOptionApiService } from '@app/core/services/administrative/licenceOption.service';
import { OptionApiService } from '@app/core/services/administrative/option.service';
import { Licence } from '@app/core/models/licence';
import { LicenceOption } from '@app/core/models/licenceOption';
import { Option } from '@app/core/models/option';
import { Subscription } from '@app/core/models/subscription';
import { SubscriptionApiService } from '@app/core/services/administrative/subscription.service';
import { SubscribedOptionsApiService } from '@app/core/services/administrative/subscribedOptions.service';
import { SubscribedOptions } from '@app/core/models/subscribedoptions';
import { NgxLoadingModule } from 'ngx-loading';

@Component({
  selector: 'app-licence',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NgxLoadingModule,
  ],
  templateUrl: './licence-cards-edit.component.html',
  styleUrls: ['./licence-cards-edit.component.css'],
  animations: [
    trigger('slideInOut', [
      state('void', style({
        transform: 'translateY(-10px)',
        opacity: 0
      })),
      state('*', style({
        transform: 'translateY(0)',
        opacity: 1
      })),
      transition('void <=> *', animate('200ms ease-in-out'))
    ]),
    trigger('growIn', [
      state('void', style({
        transform: 'scale(0.8)',
        opacity: 0
      })),
      state('*', style({
        transform: 'scale(1)',
        opacity: 1
      })),
      transition('void <=> *', animate('150ms ease-in-out'))
    ]),
    trigger('fadeInOut', [
      state('void', style({
        opacity: 0,
        transform: 'scale(0.9)'
      })),
      state('*', style({
        opacity: 1,
        transform: 'scale(1)'
      })),
      transition('void <=> *', animate('300ms ease-in-out'))
    ]),
    trigger('slideDown', [
      state('void', style({
        transform: 'translateY(-20px)',
        opacity: 0
      })),
      state('*', style({
        transform: 'translateY(0)',
        opacity: 1
      })),
      transition('void <=> *', animate('400ms ease-out'))
    ])
  ]
})
export class LicenceCardsEditComponent implements OnInit {
  constructor(
    private clientApiService: ClientApiService,
    private licenceApiService: LicenceApiService,
    private licenceOptionApiService: LicenceOptionApiService,
    private optionApiService: OptionApiService,
    private subscriptionApiService: SubscriptionApiService,
    private subscribedOptionsApiService: SubscribedOptionsApiService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  // Core component state
  isLoading = false;
  searchQuery = '';
  showDropdown = false;
  selectedClient: Client | null = null;
  clientSubscription: Subscription | null = null;
  selectedPaymentFrequency: string = 'Mensuel';
  subscriptionIdFromRoute: string | null = null;
  
  // Data arrays
  clients: Client[] = [];
  filteredClients: Client[] = [];
  licences: Licence[] = [];
  options: Option[] = [];
  licenceOptions: LicenceOption[] = [];
  subscriptions: Subscription[] = [];
  
  // Option management
  checkedOptions: { [licenceId: string]: Set<string> } = {};
  initialCheckedOptions: { [licenceId: string]: Set<string> } = {};
  
  // Popups and notifications
  showChooseClientError = false;
  showModifyPopup = false;
  showSaveNotification = false;
  showUpgradePopup = false;
  showSaveOrDiscardPopup = false;
  showNoOptionCheckedPopup = false;
  
  // Popup data
  modifyingLicence: Licence | null = null;
  oldLicence: Licence | null = null;
  selectedLicenseForConfirmation: Licence | null = null;
  licenceWithUnsavedChanges: Licence | null = null;
  oldOptions: Option[] = [];
  
  // Pricing calculations
  proratedOldForUpgrade: number = 0;
  proratedNewForUpgrade: number = 0;
  proratedDiferencePay: number = 0;
  proratedDiferenceReturn: number = 0;
  proratedTotalForUpgrade: number = 0;
  
  // Pagination
  pageSize = 3;
  currentPage = 0;
  
  // Pending restore state
  _pendingRestoreLicence: Licence | null = null;
  _pendingRestoreType: 'modify' | 'affecter' | 'upgrade' | null = null;
  
  // Touch/swipe
  private swipeStartX: number | null = null;

  get totalPages() {
    return Math.ceil(this.licences.length / this.pageSize);
  }
  
  get pagedLicences() {
    const start = this.currentPage * this.pageSize;
    return this.licences.slice(start, start + this.pageSize);
  }

  fetchClients(): Promise<void> {
    return new Promise((resolve) => {
      this.clientApiService.getAll().subscribe({
        next: (data: Client[]) => {
          this.clients = data.map(clients => ({
            ...clients,
            Name: clients.Name,
            ClientLogo: clients.ClientLogo
          }));
          this.filteredClients = [...this.clients];
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  fetchLicences(): Promise<void> {
    return new Promise((resolve) => {
      this.licenceApiService.getAll().subscribe({
        next: (data: Licence[]) => {
          this.licences = data.map(licence => ({
            ...licence,
            name: licence.Name,
            description: licence.Description
          }));
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  fetchOptions(): Promise<void> {
    return new Promise((resolve) => {
      this.optionApiService.getAll().subscribe({
        next: (data: Option[]) => {
          this.options = data.map(options => ({
            ...options,
            name: options.Name,
            price: options.Price
          }));
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  fetchLicenceOptions(): Promise<void> {
    return new Promise((resolve) => {
      this.licenceOptionApiService.getAll().subscribe({
        next: (data: LicenceOption[]) => {
          this.licenceOptions = data;
          resolve();
        },
        error: () => resolve()
      });
    });
  }

  fetchSubscriptions(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.subscriptionApiService.getAll().subscribe({
        next: async (data: Subscription[]) => {
          this.subscriptions = data;
          const today = new Date();
          for (const sub of this.subscriptions) {
            if (
              sub.Status &&
              sub.Status.toLowerCase() === 'payé' &&
              sub.DateFin &&
              new Date(sub.DateFin) < today
            ) {
              const updatedSub = { ...sub, Status: 'En attente' };
              await this.subscriptionApiService.update(updatedSub).toPromise();
              sub.Status = 'En attente';
            }
          }
          resolve();
        },
        error: (error) => {
          console.error('Error fetching subscriptions:', error);
          reject(error);
        }
      });
    });
  }

  filterClients() {
    if (!this.searchQuery.trim()) {
      this.filteredClients = [];
      this.showDropdown = false;
      return;
    }
    const query = this.searchQuery.toLowerCase().trim();
    this.filteredClients = this.clients
      .filter(client => client.Name && client.Name.toLowerCase().includes(query))
      .slice(0, 5);
    this.showDropdown = this.filteredClients.length > 0;
  }

  selectClient(client: Client) {
    console.log('selectClient called with:', client.Name);
    
    // If we're coming from route with subscription ID, don't allow client selection
    if (this.subscriptionIdFromRoute) {
      return;
    }
    
    this.selectedClient = client;
    this.searchQuery = '';
    this.showDropdown = false;
    this.filteredClients = [];

    // Clear all previous states after 1 second delay
    setTimeout(() => {
      this.checkedOptions = {};
      this.initialCheckedOptions = {};
    }, 1000);

    // Find client subscription for modify mode
    this.clientSubscription = this.subscriptions.find(sub =>
      sub.ClientId === client.Id && sub.Status !== 'Resilié'
    ) || null;

    if (this.clientSubscription) {
      const assignedLicenceId = this.clientSubscription.LicenceId;
      console.log('Found subscription for licence:', assignedLicenceId);

      // Load payment frequency from subscription
      this.selectedPaymentFrequency = this.clientSubscription.PaymentFrequency || 'Mensuel';

      // Initialize all licences with empty sets first (after delay)
      setTimeout(() => {
        this.licences.forEach(licence => {
          this.checkedOptions[licence.Id] = new Set();
          this.initialCheckedOptions[licence.Id] = new Set();
        });

        // Load ONLY the actually subscribed options from the database
        this.subscribedOptionsApiService.getAll().subscribe({
          next: (subscribedOptions: SubscribedOptions[]) => {
            console.log('All subscribed options from DB:', subscribedOptions);
            
            const thisSubscriptionOptions = subscribedOptions.filter(so =>
              so.SubscriptionId === this.clientSubscription!.Id
            );
            
            const actuallySubscribedOptionIds = thisSubscriptionOptions
              .filter(so => this.isOptionLinkedToLicence(assignedLicenceId, so.OptionId))
              .map(so => so.OptionId);

            this.checkedOptions[assignedLicenceId] = new Set(actuallySubscribedOptionIds);
            this.initialCheckedOptions[assignedLicenceId] = new Set(actuallySubscribedOptionIds);

            console.log(`Modify mode - Assigned licence ${assignedLicenceId}: checked ${actuallySubscribedOptionIds.length} options`);
          },
          error: (error) => {
            console.error('Error loading subscribed options:', error);
          }
        });
      }, 1000);
    } else {
      console.log('No active subscription found - all options unchecked');
      this.selectedPaymentFrequency = 'Mensuel';
    }
  }

  toggleOption(licence: Licence, optionId: string, event: Event) {
    const licenceId = licence.Id;
    if (!this.checkedOptions[licenceId]) {
      this.checkedOptions[licenceId] = new Set();
    }
    if (this.checkedOptions[licenceId].has(optionId)) {
      this.checkedOptions[licenceId].delete(optionId);
    } else {
      this.checkedOptions[licenceId].add(optionId);
    }
  }

  isLicenceAssignedToClient(licence: Licence): boolean {
    if (!this.selectedClient || !this.clientSubscription) return false;
    return this.clientSubscription.LicenceId === licence.Id && this.clientSubscription.Status !== 'Resilié';
  }

  hasOptionChanges(licence: Licence): boolean {
    const checked = this.checkedOptions[licence.Id] || new Set();
    const initial = this.initialCheckedOptions[licence.Id] || new Set();
    if (checked.size !== initial.size) return true;
    for (const id of checked) {
      if (!initial.has(id)) return true;
    }
    return false;
  }

  isSubscriptionCancelled(licence: Licence): boolean {
    if (!this.selectedClient || !this.clientSubscription) return false;
    if (this.clientSubscription.LicenceId !== licence.Id) return false;
    const status = this.clientSubscription.Status;
    return typeof status === 'string' && status.toLowerCase() === 'resilié';
  }

  isOptionChecked(licence: Licence, optionId: string): boolean {
    return this.checkedOptions[licence.Id]?.has(optionId) ?? false;
  }

  getOptionsForLicence(licence: Licence): Option[] {
    if (!licence) return [];
    const linkedOptionIds = this.licenceOptions
      .filter((lo: LicenceOption) => lo.LicenceId === licence.Id)
      .map((lo: LicenceOption) => lo.OptionId);
    return this.options.filter((opt: Option) => linkedOptionIds.includes(opt.Id));
  }

  isOptionLinkedToLicence(licenceId: string, optionId: string): boolean {
    return this.licenceOptions.some((lo: LicenceOption) => lo.LicenceId === licenceId && lo.OptionId === optionId);
  }

  openModifyPopup(licence: Licence) {
    if (!this.selectedClient || !this.clientSubscription) return;
    const checked = this.checkedOptions[licence.Id] || new Set();
    if (checked.size === 0) {
      this.showNoOptionCheckedPopup = true;
      this._pendingRestoreLicence = licence;
      this._pendingRestoreType = 'modify';
      return;
    }
    this.modifyingLicence = licence;
    this.oldOptions = this.getOptionsForLicence(licence).filter(opt =>
      this.initialCheckedOptions[licence.Id]?.has(opt.Id)
    );
    this.showModifyPopup = true;
  }

  closeNoOptionCheckedPopup() {
    this.showNoOptionCheckedPopup = false;
    if (this._pendingRestoreLicence) {
      const licence = this._pendingRestoreLicence;
      const initial = this.initialCheckedOptions[licence.Id] || new Set();
      this.checkedOptions[licence.Id] = new Set(Array.from(initial));
      this._pendingRestoreLicence = null;
      this._pendingRestoreType = null;
    }
  }

  upgradeLicenceForClient(licence: Licence) {
    if (!this.selectedClient || !this.clientSubscription) return;
    const checked = this.checkedOptions[licence.Id] || new Set();
    if (checked.size === 0) {
      this.showNoOptionCheckedPopup = true;
      this._pendingRestoreLicence = licence;
      this._pendingRestoreType = 'upgrade';
      return;
    }
    this.selectedLicenseForConfirmation = licence;
    this.oldLicence = this.licences.find(l => l.Id === this.clientSubscription?.LicenceId) || null;
    if (this.clientSubscription) {
      this.subscribedOptionsApiService.getAll().subscribe({
        next: (subscribedOptions: SubscribedOptions[]) => {
          const checkedOptionIds = subscribedOptions
            .filter(so =>
              so.SubscriptionId === this.clientSubscription!.Id &&
              this.isOptionLinkedToLicence(this.clientSubscription!.LicenceId, so.OptionId))
            .map(so => so.OptionId);
          this.oldOptions = this.getOptionsForLicence(this.oldLicence!).filter(opt =>
            checkedOptionIds.includes(opt.Id)
          );
          this.calculateUpgradePrices();
          this.showUpgradePopup = true;
        }
      });
    }
  }

  getOldOptionsForModifying(): Option[] {
    return this.oldOptions;
  }

  getNewOptionsForModifying(): Option[] {
    if (!this.modifyingLicence) return [];
    return this.getOptionsForLicence(this.modifyingLicence).filter(opt =>
      this.checkedOptions[this.modifyingLicence!.Id]?.has(opt.Id)
    );
  }

  getNewOptionsForUpgrade(): Option[] {
    if (!this.selectedLicenseForConfirmation) return [];
    const checkedSet = this.checkedOptions[this.selectedLicenseForConfirmation.Id] || new Set();
    return this.getOptionsForLicence(this.selectedLicenseForConfirmation).filter(opt => checkedSet.has(opt.Id));
  }

  getOldTotalForModifying(): number {
    if (
      this.modifyingLicence &&
      this.clientSubscription &&
      this.clientSubscription.LicenceId === this.modifyingLicence.Id
    ) {
      return this.clientSubscription.Price || 0;
    }
    
    if (!this.modifyingLicence) return 0;
    const checkedSet = this.initialCheckedOptions[this.modifyingLicence.Id] || new Set();
    
    const baseTotal = this.getOptionsForLicence(this.modifyingLicence)
      .filter(opt => checkedSet.has(opt.Id))
      .reduce((sum, opt) => sum + (opt.Price || 0), 0);

    if (this.selectedPaymentFrequency === 'Annuel') {
      return baseTotal * 12;
    } else {
      return baseTotal;
    }
  }

  getPriceToPayForModification(): number {
    if (!this.modifyingLicence) return 0;
    const oldSet = this.initialCheckedOptions[this.modifyingLicence.Id] || new Set();
    const newSet = this.checkedOptions[this.modifyingLicence.Id] || new Set();
    let price = 0;

    if (this.selectedPaymentFrequency === 'Annuel') {
      const today = new Date();
      const startOfYear = new Date(today.getFullYear(), 0, 1);
      const endOfYear = new Date(today.getFullYear(), 11, 31);
      const totalDaysInYear = Math.ceil((endOfYear.getTime() - startOfYear.getTime()) / (1000 * 60 * 60 * 24)) + 1;
      const daysPassed = Math.ceil((today.getTime() - startOfYear.getTime()) / (1000 * 60 * 60 * 24));
      const daysRemaining = totalDaysInYear - daysPassed + 1;

      for (const id of newSet) {
        if (!oldSet.has(id)) {
          const option = this.options.find(opt => opt.Id === id);
          if (option && typeof option.Price === 'number') {
            const annualPrice = option.Price * 12;
            price += annualPrice * (daysRemaining / totalDaysInYear);
          }
        }
      }
    } else {
      const today = new Date();
      const daysInMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0).getDate();
      const currentDay = today.getDate();
      const daysRemaining = daysInMonth - currentDay + 1;

      for (const id of newSet) {
        if (!oldSet.has(id)) {
          const option = this.options.find(opt => opt.Id === id);
          if (option && typeof option.Price === 'number') {
            price += (option.Price / daysInMonth) * daysRemaining;
          }
        }
      }
    }
    return price;
  }

  getPriceToRefundForModification(): number {
    if (!this.modifyingLicence) return 0;
    const oldSet = this.initialCheckedOptions[this.modifyingLicence.Id] || new Set();
    const newSet = this.checkedOptions[this.modifyingLicence.Id] || new Set();
    let refund = 0;

    if (this.selectedPaymentFrequency === 'Annuel') {
      const today = new Date();
      const startOfYear = new Date(today.getFullYear(), 0, 1);
      const endOfYear = new Date(today.getFullYear(), 11, 31);
      const totalDaysInYear = Math.ceil((endOfYear.getTime() - startOfYear.getTime()) / (1000 * 60 * 60 * 24)) + 1;
      const daysPassed = Math.ceil((today.getTime() - startOfYear.getTime()) / (1000 * 60 * 60 * 24));
      const daysRemaining = totalDaysInYear - daysPassed + 1;

      for (const id of oldSet) {
        if (!newSet.has(id)) {
          const option = this.options.find(opt => opt.Id === id);
          if (option && typeof option.Price === 'number') {
            const annualPrice = option.Price * 12;
            refund += annualPrice * (daysRemaining / totalDaysInYear);
          }
        }
      }
    } else {
      const today = new Date();
      const daysInMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0).getDate();
      const currentDay = today.getDate();
      const daysRemaining = daysInMonth - currentDay + 1;

      for (const id of oldSet) {
        if (!newSet.has(id)) {
          const option = this.options.find(opt => opt.Id === id);
          if (option && typeof option.Price === 'number') {
            refund += (option.Price / daysInMonth) * daysRemaining;
          }
        }
      }
    }
    return refund;
  }

  getNewTotalWithOldForModifying(): number {
    if (this.modifyingLicence && this.clientSubscription) {
      const today = new Date();
      const dateFinRaw = this.clientSubscription.DateFin;
      if (dateFinRaw) {
        const dateFin = new Date(typeof dateFinRaw === 'string' ? dateFinRaw : dateFinRaw.toString());
        today.setHours(0,0,0,0);
        dateFin.setHours(0,0,0,0);
        if (today >= dateFin) {
          const months = this.getMonthsForPaymentFrequency(this.selectedPaymentFrequency);
          return this.getNewOptionsForModifying().reduce((sum, opt) => sum + ((opt.Price || 0) * months), 0);
        }
      }
    }
    return (
      this.getOldTotalForModifying() +
      this.getPriceToPayForModification() -
      this.getPriceToRefundForModification()
    );
  }

  getModificationType(): 'add' | 'remove' | 'both' | null {
    if (!this.modifyingLicence) return null;
    const oldSet = this.initialCheckedOptions[this.modifyingLicence.Id] || new Set();
    const newSet = this.checkedOptions[this.modifyingLicence.Id] || new Set();
    let added = false, removed = false;
    for (const id of newSet) {
      if (!oldSet.has(id)) added = true;
    }
    for (const id of oldSet) {
      if (!newSet.has(id)) removed = true;
    }
    if (added && removed) return 'both';
    if (added) return 'add';
    if (removed) return 'remove';
    return null;
  }

  getMonthsForPaymentFrequency(paymentFrequency: string): number {
    const freq = this.paymentFrequencies.find(f => f.value === paymentFrequency);
    if (!freq) return 1;
    return freq.months;
  }

  private calculateUpgradePrices() {
    if (!this.selectedLicenseForConfirmation || !this.oldLicence || !this.clientSubscription) {
      this.proratedOldForUpgrade = 0;
      this.proratedNewForUpgrade = 0;
      this.proratedDiferencePay = 0;
      this.proratedDiferenceReturn = 0;
      this.proratedTotalForUpgrade = 0;
      return;
    }

    const oldPriceFromSubscription = this.clientSubscription.Price || 0;
    
    const checkedSet = this.checkedOptions[this.selectedLicenseForConfirmation.Id] || new Set();
    const newOptions = this.getOptionsForLicence(this.selectedLicenseForConfirmation).filter(opt => checkedSet.has(opt.Id));

    let newTotalPrice: number;

    if (this.selectedPaymentFrequency === 'Annuel') {
      const today = new Date();
      const startOfYear = new Date(today.getFullYear(), 0, 1);
      const endOfYear = new Date(today.getFullYear(), 11, 31);
      const totalDaysInYear = Math.ceil((endOfYear.getTime() - startOfYear.getTime()) / (1000 * 60 * 60 * 24)) + 1;
      const daysPassed = Math.ceil((today.getTime() - startOfYear.getTime()) / (1000 * 60 * 60 * 24));
      const daysRemaining = totalDaysInYear - daysPassed + 1;

      newTotalPrice = newOptions.reduce((sum, opt) => {
        const annualPrice = (opt.Price || 0) * 12;
        const proratedPrice = annualPrice * (daysRemaining / totalDaysInYear);
        return sum + proratedPrice;
      }, 0);
    } else {
      const today = new Date();
      const daysInMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0).getDate();
      const currentDay = today.getDate();
      const daysRemaining = daysInMonth - currentDay + 1;

      newTotalPrice = newOptions.reduce((sum, opt) => {
        const monthlyPrice = opt.Price || 0;
        const proratedPrice = monthlyPrice * (daysRemaining / daysInMonth);
        return sum + proratedPrice;
      }, 0);
    }

    let priceToPay = 0;
    let priceToReturn = 0;

    if (oldPriceFromSubscription < newTotalPrice) {
      priceToPay = newTotalPrice - oldPriceFromSubscription;
      priceToReturn = 0;
    } else if (oldPriceFromSubscription > newTotalPrice) {
      priceToPay = 0;
      priceToReturn = oldPriceFromSubscription - newTotalPrice;
    } else {
      priceToPay = 0;
      priceToReturn = 0;
    }

    this.proratedOldForUpgrade = oldPriceFromSubscription;
    this.proratedNewForUpgrade = newTotalPrice;
    this.proratedDiferencePay = priceToPay;
    this.proratedDiferenceReturn = priceToReturn;
    this.proratedTotalForUpgrade = newTotalPrice;
  }

  confirmSaveModifiedLicenceOptions() {
    if (
      !this.modifyingLicence ||
      !this.selectedClient ||
      !this.clientSubscription ||
      this.clientSubscription.LicenceId !== this.modifyingLicence.Id
    ) {
      this.showModifyPopup = false;
      this.modifyingLicence = null;
      return;
    }

    const subscriptionId = this.clientSubscription.Id;
    const updatedSubscription = {
      ...this.clientSubscription,
      Price: this.getNewTotalWithOldForModifying(),
      DateDebut: this.clientSubscription.DateDebut,
      DateFin: this.clientSubscription.DateFin,
      Status: 'En attente'
    };

    const licenceId = this.modifyingLicence.Id;
    const checkedSet = this.checkedOptions[licenceId] || new Set();
    const checkedOptionIds = Array.from(checkedSet);

    this.subscriptionApiService.update({ ...updatedSubscription, Id: subscriptionId }).subscribe({
      next: () => {
        this.saveSubscribedOptionsForLicence(subscriptionId, checkedOptionIds).then(() => {
          this.fetchSubscriptions();
          this.fetchClients().then(() => {
            const refreshedClient = this.clients.find(c => c.Id === this.selectedClient?.Id);
            if (refreshedClient) {
              this.selectClient(refreshedClient);
            }
          });

          this.showModifyPopup = false;
          this.modifyingLicence = null;
          this.showSaveNotification = true;
          setTimeout(() => {
            this.showSaveNotification = false;
            // If we came from route, navigate back to subscription list
            if (this.subscriptionIdFromRoute) {
              this.router.navigate(['/licence']);
            }
          }, 3000);
        });
      },
      error: (error) => {
        console.error('Error updating subscription:', error);
        this.showModifyPopup = false;
        this.modifyingLicence = null;
      }
    });
  }

  cancelModifyLicence() {
    Object.keys(this.initialCheckedOptions).forEach(licenceId => {
      this.checkedOptions[licenceId] = new Set<string>(Array.from(this.initialCheckedOptions[licenceId]));
    });
    this.showModifyPopup = false;
    this.modifyingLicence = null;
  }

  cancelUpgradePopup() {
    this.showUpgradePopup = false;
    this.selectedLicenseForConfirmation = null;
    this.oldLicence = null;
    this.oldOptions = [];
  }

  async confirmUpgradeLicence() {
    if (!this.selectedClient || !this.selectedLicenseForConfirmation) {
      return;
    }
    try {
      const dateDebut = new Date();
      const dateFin = this.calculateDateFin(dateDebut, this.selectedPaymentFrequency, 1);
      const dateFinStr = this.formatDateForBackend(dateFin);

      const clientId = this.selectedClient.Id;
      const licenceId = this.selectedLicenseForConfirmation.Id;

      const checkedSet = this.checkedOptions[licenceId] || new Set();
      let totalPrice: number;
      const today = new Date();
      today.setHours(0,0,0,0);
      dateFin.setHours(0,0,0,0);
      if (today >= dateFin) {
        totalPrice = this.getOptionsForLicence(this.selectedLicenseForConfirmation)
          .filter(opt => checkedSet.has(opt.Id))
          .reduce((sum, opt) => sum + ((opt.Price || 0) * this.getMonthsForPaymentFrequency(this.selectedPaymentFrequency)), 0);
      } else {
        totalPrice = Math.abs(this.proratedTotalForUpgrade);
      }

      const subscriptions = await this.subscriptionApiService.getAll().toPromise() ?? [];
      const existing = subscriptions.find(sub => sub.ClientId === clientId && sub.Status !== 'Resilié');

      const checkedOptionIds = Array.from(checkedSet);

      if (existing) {
        const updated = {
          ...existing,
          LicenceId: licenceId,
          DateDebut: this.formatDateForBackend(new Date()),
          DateFin: dateFinStr,
          Price: totalPrice,
          Status: 'En attente',
          PaymentFrequency: this.selectedPaymentFrequency
        };
        await this.subscriptionApiService.update(updated).toPromise();
        await this.saveSubscribedOptionsForLicence(updated.Id, checkedOptionIds);
        this.showSaveNotification = true;
        this.showUpgradePopup = false;
        this.selectedLicenseForConfirmation = null;
        this.oldLicence = null;
        this.oldOptions = [];
        await Promise.all([
          this.fetchSubscriptions(),
          this.fetchClients()
        ]);
        const refreshedClient = this.clients.find(c => c.Id === this.selectedClient?.Id);
        if(refreshedClient) {
          this.selectClient(refreshedClient);
        }
        
        // Navigate back if we came from route
        setTimeout(() => {
          this.showSaveNotification = false;
          if (this.subscriptionIdFromRoute) {
            this.router.navigate(['/licence']);
          }
        }, 3000);
      }
    } catch (error) {
      console.error('Error upgrading licence:', error);
    }
  }

  confirmSaveAndClearSelection() {
    if (this.licenceWithUnsavedChanges) {
      this.modifyingLicence = this.licenceWithUnsavedChanges;
      const licenceId = this.modifyingLicence ? this.modifyingLicence.Id : '';
      this.oldOptions = this.getOptionsForLicence(this.modifyingLicence!).filter(opt =>
        licenceId && this.initialCheckedOptions[licenceId]?.has(opt.Id)
      );
      this.showModifyPopup = true;
      this.showSaveOrDiscardPopup = false;
      this.licenceWithUnsavedChanges = null;
    }
  }

  discardChangesAndClearSelection() {
    Object.keys(this.initialCheckedOptions).forEach(licenceId => {
      this.checkedOptions[licenceId] = new Set<string>(Array.from(this.initialCheckedOptions[licenceId]));
    });
    this.showSaveOrDiscardPopup = false;
    this.modifyingLicence = null;
    this.selectedClient = null;
    setTimeout(() => {
      window.location.reload();
    }, 500);
  }

  // Navigation methods
  goBackToSubscriptionList(): void {
    this.router.navigate(['/licence']);
  }
  prevPage() {
    if (this.currentPage > 0) {
      this.currentPage--;
      this.scrollToTop();
    }
  }

  nextPage() {
    if (this.currentPage < this.totalPages - 1) {
      this.currentPage++;
      this.scrollToTop();
    }
  }

  scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  // Touch/swipe handlers for card pagination
  onTouchStart(event: TouchEvent | MouseEvent) {
    if (event instanceof TouchEvent) {
      this.swipeStartX = event.touches[0].clientX;
    } else if (event instanceof MouseEvent) {
      this.swipeStartX = event.clientX;
    }
  }

  onTouchEnd(event: TouchEvent | MouseEvent) {
    if (this.swipeStartX === null) return;
    let endX: number;
    if (event instanceof TouchEvent) {
      endX = event.changedTouches[0].clientX;
    } else if (event instanceof MouseEvent) {
      endX = event.clientX;
    } else {
      return;
    }
    const deltaX = endX - this.swipeStartX;
    if (Math.abs(deltaX) > 60) {
      if (deltaX > 0) {
        this.prevPage();
      } else {
        this.nextPage();
      }
    }
  }

  // Utility methods
  formatDateForBackend(date: Date | string | null): string {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  calculateDateFin(dateDebut: Date, paymentFrequency: string, customMonths: number): Date {
    let monthsToAdd = 1;
    switch (paymentFrequency) {
      case 'Mensuel':
        monthsToAdd = 1;
        break;
      case 'Annuel':
        monthsToAdd = 12;
        break;
      default:
        monthsToAdd = 1;
    }
    const result = new Date(dateDebut);
    const originalDay = result.getDate();
    let newMonth = result.getMonth() + monthsToAdd;
    let newYear = result.getFullYear();
    newYear += Math.floor(newMonth / 12);
    newMonth = newMonth % 12;
    result.setFullYear(newYear, newMonth, 1);
    const lastDay = new Date(result.getFullYear(), result.getMonth() + 1, 0).getDate();
    result.setDate(Math.min(originalDay, lastDay));
    return result;
  }

  private async saveSubscribedOptionsForLicence(subscriptionId: string, checkedOptionIds: string[]) {
    return new Promise<void>((resolve) => {
      this.subscribedOptionsApiService.getAll().subscribe({
        next: (allSubscribedOptions: SubscribedOptions[]) => {
          const toDelete = allSubscribedOptions.filter(
            (so: SubscribedOptions) => so.SubscriptionId === subscriptionId
          );
          let deleteCount = 0;
          if (toDelete.length === 0) {
            checkedOptionIds.forEach(optionId => {
              const subscribedOption: Partial<SubscribedOptions> = {
                SubscriptionId: subscriptionId,
                OptionId: optionId,
                checked: true
              };
              this.subscribedOptionsApiService.create(subscribedOption).subscribe();
            });
            resolve();
          } else {
            toDelete.forEach((so: SubscribedOptions) => {
              this.subscribedOptionsApiService.delete(so.Id).subscribe({
                next: () => {
                  deleteCount++;
                  if (deleteCount === toDelete.length) {
                    checkedOptionIds.forEach(optionId => {
                      const subscribedOption: Partial<SubscribedOptions> = {
                        SubscriptionId: subscriptionId,
                        OptionId: optionId,
                        checked: true
                      };
                      this.subscribedOptionsApiService.create(subscribedOption).subscribe();
                    });
                    resolve();
                  }
                },
                error: () => {
                  deleteCount++;
                  if (deleteCount === toDelete.length) {
                    checkedOptionIds.forEach(optionId => {
                      const subscribedOption: Partial<SubscribedOptions> = {
                        SubscriptionId: subscriptionId,
                        OptionId: optionId,
                        checked: true
                      };
                      this.subscribedOptionsApiService.create(subscribedOption).subscribe();
                    });
                    resolve();
                  }
                }
              });
            });
          }
        }
      });
    });
  }

  paymentFrequencies: { label: string, value: string, months: number }[] = [
    { label: 'Mensuel', value: 'Mensuel', months: 1 },
    { label: 'Annuel', value: 'Annuel', months: 12 }
  ];

  ngOnInit(): void {
    this.isLoading = true;
    console.log('LicenceCardsEditComponent ngOnInit called');
    
    // Get subscription ID from route parameters
    this.subscriptionIdFromRoute = this.route.snapshot.paramMap.get('id');
    console.log('Subscription ID from route:', this.subscriptionIdFromRoute);
    
    Promise.all([
      this.fetchClients(),
      this.fetchLicences(),
      this.fetchOptions(),
      this.fetchLicenceOptions(),
      this.fetchSubscriptions()
    ]).then(() => {
      console.log('All data loaded successfully');
      console.log('Clients:', this.clients.length);
      console.log('Licences:', this.licences.length);
      console.log('Options:', this.options.length);
      console.log('Subscriptions:', this.subscriptions.length);
      
      this.initialCheckedOptions = {};
      
      // If we have a subscription ID from route, load that specific subscription
      if (this.subscriptionIdFromRoute) {
        console.log('Loading subscription from route...');
        this.loadSubscriptionFromRoute();
      } else {
        console.log('No subscription ID in route - normal mode');
      }
      
      this.isLoading = false;
    }).catch((error) => {
      console.error('Error loading data:', error);
      this.isLoading = false;
    });
  }

  private loadSubscriptionFromRoute(): void {
    console.log('Looking for subscription with ID:', this.subscriptionIdFromRoute);
    
    // Find the subscription by ID
    const targetSubscription = this.subscriptions.find(sub => sub.Id === this.subscriptionIdFromRoute);
    
    if (!targetSubscription) {
      console.error('Subscription not found:', this.subscriptionIdFromRoute);
      console.log('Available subscriptions:', this.subscriptions.map(s => s.Id));
      // Navigate back to subscription list if not found
      this.router.navigate(['/licence']);
      return;
    }

    console.log('Found subscription:', targetSubscription);

    // Find the client for this subscription
    const targetClient = this.clients.find(client => client.Id === targetSubscription.ClientId);
    
    if (!targetClient) {
      console.error('Client not found for subscription:', this.subscriptionIdFromRoute);
      console.log('Available clients:', this.clients.map(c => ({ id: c.Id, name: c.Name })));
      this.router.navigate(['/licence']);
      return;
    }

    console.log('Found client:', targetClient.Name);

    // Set the subscription and client data
    this.clientSubscription = targetSubscription;
    this.selectedClient = targetClient;
    this.selectedPaymentFrequency = targetSubscription.PaymentFrequency || 'Mensuel';

    // Hide search and dropdown since we're in edit mode
    this.showDropdown = false;
    this.searchQuery = '';

    // Load the subscription options
    this.loadSubscriptionOptions(targetSubscription);
  }

  private loadSubscriptionOptions(subscription: Subscription): void {
    console.log('Loading options for subscription:', subscription.Id, 'licence:', subscription.LicenceId);
    
    // Initialize all licences with empty sets
    this.licences.forEach(licence => {
      this.checkedOptions[licence.Id] = new Set();
      this.initialCheckedOptions[licence.Id] = new Set();
    });

    // Load the subscribed options for this subscription
    this.subscribedOptionsApiService.getAll().subscribe({
      next: (subscribedOptions: SubscribedOptions[]) => {
        console.log('All subscribed options from DB:', subscribedOptions);
        
        const thisSubscriptionOptions = subscribedOptions.filter(so =>
          so.SubscriptionId === subscription.Id
        );
        
        console.log('Options for this subscription:', thisSubscriptionOptions);
        
        const subscribedOptionIds = thisSubscriptionOptions
          .filter(so => this.isOptionLinkedToLicence(subscription.LicenceId, so.OptionId))
          .map(so => so.OptionId);

        console.log('Valid subscribed option IDs:', subscribedOptionIds);

        // Set the checked options for the assigned licence
        this.checkedOptions[subscription.LicenceId] = new Set(subscribedOptionIds);
        this.initialCheckedOptions[subscription.LicenceId] = new Set(subscribedOptionIds);

        console.log(`Loaded subscription ${subscription.Id} for licence ${subscription.LicenceId}: ${subscribedOptionIds.length} options`);
        console.log('Final checked options state:', this.checkedOptions);
      },
      error: (error) => {
        console.error('Error loading subscription options:', error);
      }
    });
  }
}