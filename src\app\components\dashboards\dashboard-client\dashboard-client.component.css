.statistics-header {
  background: white;
  border-radius: 8px;
  padding: 15px 20px;
  margin-bottom: 15px;
  width: 100%;
}

.statistics-title {
  font-size: 18px;
  font-weight: 600;
  color: #212529;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.dashboard-container {
  padding: 15px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #333;
  line-height: 1.5;
}

.dashboard-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Cards at the top - flex with wrapping */
.cards-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  width: 100%;
  justify-content: center; /* Center the cards */
}

.card {
  background: white;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  height: fit-content;
  min-height: 120px;
  width: 280px; /* Increased from 220px to 280px */
  flex: 0 0 280px; /* Don't grow or shrink, fixed width - updated to match */
}

/* When more than 4 cards per row, they wrap to next line */
.cards-container .card:nth-child(n+5) {
  margin-top: 0; /* Reset any margin for wrapped items */
}

/* Charts section - restructured layout */
.charts-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Top chart (third chart) - full width */
.top-chart {
  width: 80%;
  margin: 0 auto;
}

/* Bottom two charts - horizontal stack */
.bottom-charts {
  display: flex;
  gap: 20px;
  justify-content: center; /* Center the two charts */
  max-width: 900px; /* Limit width to keep them reasonable size */
  margin: 0 auto; /* Center the container itself */
}

.bottom-charts .chart-card {
  flex: 1;
  min-width: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.card-title {
  font-size: 13px;
  font-weight: 500;
  color: #6c757d;
  margin-bottom: 6px;
}

.card-value {
  font-size: 20px;
  font-weight: 600;
  color: #212529;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.kwh-unit {
  font-size: 14px;
  font-weight: 400;
  color: #6c757d;
}

.percentage {
  font-size: 11px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  gap: 2px;
}

.percentage.positive {
  color: #198754;
  background-color: #d1e7dd;
}

.percentage.negative {
  color: #dc3545;
  background-color: #f8d7da;
}

.na-badge {
  background-color: #fff3cd;
  color: #856404;
  font-size: 11px;
  font-weight: 500;
  padding: 3px 6px;
  border-radius: 4px;
}

.objective-section {
  margin-top: 12px;
}

.objective-label {
  font-size: 11px;
  color: #6c757d;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.objective-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.objective-dot.blue { background-color: #0d6efd; }
.objective-dot.green { background-color: #20c997; }
.objective-dot.purple { background-color: #6f42c1; }
.objective-dot.yellow { background-color: #ffc107; }

.progress-container {
  display: flex;
  align-items: center;
  gap: 6px;
}

.progress-bar {
  flex: 1;
  height: 3px;
  background-color: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-fill.blue { background-color: #0d6efd; }
.progress-fill.green { background-color: #20c997; }
.progress-fill.purple { background-color: #6f42c1; }
.progress-fill.yellow { background-color: #ffc107; }

.progress-text {
  font-size: 11px;
  font-weight: 600;
  color: #495057;
  min-width: 28px;
}

.chart-card {
  background: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #212529;
}

.chart-menu {
  color: #6c757d;
  font-size: 20px;
  cursor: pointer;
}

.chart-legend {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #6c757d;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.legend-dot.blue { background-color: #0d6efd; }
.legend-dot.light-blue { background-color: #b3d7ff; }

.chart-container {
  position: relative;
  height: 250px; /* Reduced to fit card proportions */
  width: 100%;
}

/* Map section - full width */
.map-section {
  width: 100%;
}

.map-section .chart-container {
  height: 250px; /* Reduced map height to fit better */
}

/* Responsive design */
@media (max-width: 1200px) {
  .bottom-charts {
    flex-direction: column;
  }

  .bottom-charts .chart-card {
    min-width: unset;
  }
}

@media (max-width: 768px) {
  .cards-container {
    justify-content: center;
  }

  .card {
    width: calc(50% - 8px);
    min-width: 240px; /* Increased from 200px to 240px for better text accommodation */
    flex: 0 0 calc(50% - 8px);
  }

  .card-value {
    font-size: 18px;
  }

  .chart-container {
    height: 180px; /* Smaller for mobile */
  }

  .map-section .chart-container {
    height: 200px; /* Smaller map on mobile */
  }
}

@media (max-width: 480px) {
  .card {
    width: 100%;
    flex: 0 0 100%;
  }
}
