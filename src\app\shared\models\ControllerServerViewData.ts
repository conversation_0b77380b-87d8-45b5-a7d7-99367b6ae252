

export interface ControllerServerViewData {
  Id: string;
  Name: string;
  MaxControllers?: number;
  MaxSensors?: number;
  GeographicZone?: string;
  CommercialCondition?: string;
  TriggerType?: string;
  ActionType?: string;
  EventType?: string;
  Status?: string;
  CreatedAt?: Date;
  LastUpdatedAt?: Date;
  // Client fields
  ClientId: string;
  ClientName: string;
  // Licence fields
  LicenceId: string;
  LicenceName: string;
  LicenceDescription?: string;
  // Subscription fields
  SubscriptionId: string;
  SubscriptionStartDate?: Date;
  SubscriptionEndDate?: Date;
  SubscriptionStatus: string;
}
