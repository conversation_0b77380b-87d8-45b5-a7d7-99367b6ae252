import { ComponentFixture, TestBed } from '@angular/core/testing';

import { GenericTableClientComponent } from './generic-table-client.component';

describe('GenericTableClientComponent', () => {
  let component: GenericTableClientComponent;
  let fixture: ComponentFixture<GenericTableClientComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [GenericTableClientComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(GenericTableClientComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
