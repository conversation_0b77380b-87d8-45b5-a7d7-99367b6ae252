import {AfterViewInit, Component} from '@angular/core';
import {Chart, registerables} from "chart.js";
import {Ng<PERSON><PERSON>, Ng<PERSON>orO<PERSON>, NgIf} from "@angular/common";

Chart.register(...registerables);

interface Product {
  title: string;
  id: number;
}

@Component({
  selector: 'app-site-dashboard-page',
  imports: [
    NgForO<PERSON>,
    Ng<PERSON>lass,
    NgIf,
  ],
  templateUrl: './site-dashboard-page.component.html',
  styleUrl: './site-dashboard-page.component.css'
})
export class SiteDashboardPageComponent implements AfterViewInit {
  products: Product[] = [
    { id: 1, title: 'Laptop' },
    { id: 2, title: 'Smartphone' },
  ];

  displayProduct(product: Product): string {
    return product.title;
  }

  filterProduct(product: Product, search: string): boolean {
    return product.title.toLowerCase().includes(search.toLowerCase());
  }

  onProductSelected(product: Product) {
    console.log('Selected:', product);
  }


  cards = [
    {
      title: 'Économisations',
      amount: '€23,523',
      percentage: 6.71,
      progress: 70,
      isPositive: true,
      objectiveColor: 'blue',
      unit: ''
    },
    {
      title: 'Dépense',
      amount: '€8,753',
      percentage: -3.26,
      progress: 60,
      isPositive: false,
      objectiveColor: 'green',
      unit: ''
    },
    {
      title: 'Cons. d\'électricité',
      amount: '235.6',
      percentage: -2.71,
      progress: 45,
      isPositive: false,
      objectiveColor: 'yellow',
      unit: 'KWh'
    },
    {
      title: 'Équipement installer',
      amount: '54',
      percentage: null, // N/A
      progress: 90,
      isPositive: null,
      objectiveColor: 'purple',
      unit: ''
    },
    {
      title: 'Statistique gaz',
      amount: '19.3 m³/min',
      percentage: 1.71,
      progress: 70,
      isPositive: true,
      objectiveColor: 'blue',
      unit: ''
    },
    {
      title: "Statistique d'électricité",
      amount: '235.6 KWh',
      percentage: -3.26,
      progress: 60,
      isPositive: false,
      objectiveColor: 'green',
      unit: ''
    },
    {
      title: 'Température d\'air',
      amount: ' 23.4 ℃ ',
      percentage: null,
      progress: null,
      isPositive: false,
      objectiveColor: 'yellow',
      unit: 'KWh'
    },
    {
      title: 'Humidité d\'air',
      amount: ' 37.9% ',
      percentage: null, // N/A
      progress: null,
      isPositive: null,
      objectiveColor: 'purple',
      unit: ''
    }
  ];

  ngAfterViewInit(): void {
    this.initChart();
  }


  initChart(): void {
    const ctx = document.getElementById('consumptionChart') as HTMLCanvasElement;
    if (!ctx) return;

    new Chart(ctx, {
      type: 'bar',
      data: {
        labels: ['Mar', 'Avr', 'Mai', 'Jul.', 'Juil.', 'Août'],
        datasets: [
          {
            label: 'Dépense',
            data: [22, 30, 35, 45, 52, 44],
            backgroundColor: '#0d6efd',
            borderRadius: 4,
            maxBarThickness: 24,
            categoryPercentage: 0.8,
            barPercentage: 0.9
          },
          {
            label: 'Économisations',
            data: [20, 25, 32, 42, 48, 40],
            backgroundColor: '#b3d7ff',
            borderRadius: 4,
            maxBarThickness: 24,
            categoryPercentage: 0.8,
            barPercentage: 0.9
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            max: 80,
            ticks: {
              stepSize: 20,
              color: '#6c757d',
              font: {
                size: 12
              }
            },
            grid: {
              color: '#e9ecef',
            }
          },
          x: {
            ticks: {
              color: '#6c757d',
              font: {
                size: 12
              }
            },
            grid: {
              display: false
            }
          }
        },
        elements: {
          bar: {
            borderWidth: 0
          }
        }
      }
    });
  }

  getPercentageClass(isPositive: boolean | null): string {
    if (isPositive === null) return '';
    return isPositive ? 'positive' : 'negative';
  }

  getPercentageIcon(isPositive: boolean | null): string {
    if (isPositive === null) return '';
    return isPositive ? '↗' : '↘';
  }

  getObjectiveDotClass(color: string): string {
    return `objective-dot ${color}`;
  }

  getProgressFillClass(color: string): string {
    return `progress-fill ${color}`;
  }

  protected readonly Math = Math;


}
