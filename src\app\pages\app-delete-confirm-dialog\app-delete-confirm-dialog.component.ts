import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-delete-confirm-dialog',
  template: `
    <div class="dialog-content">
      <div class="dialog-header">
        <div class="icon-container">
          <mat-icon class="warning-icon">warning</mat-icon>
        </div>
        <div>
          <h3 class="dialog-title">Confirmer la suppression</h3>
          <p class="dialog-subtitle">Cette action ne peut pas être annulée.</p>
        </div>
      </div>
      <p class="dialog-message">
        Êtes-vous sûr de vouloir supprimer cette règle ? <span class="font-medium">"{{ data.ruleName }}"</span>
      </p>
      <div class="dialog-actions">
        <button mat-button (click)="dialogRef.close(false)">Annuler</button>
        <button mat-raised-button color="warn" (click)="dialogRef.close(true)">Supprimer</button>
      </div>
    </div>
  `,
  styles: [
    `
      .dialog-content {
        padding: 24px;
      }
      .dialog-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 16px;
      }
      .icon-container {
        width: 40px;
        height: 40px;
        border-radius: 9999px;
        background-color: #fed7d7;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .warning-icon {
        color: #c53030;
      }
      .dialog-title {
        font-size: 18px;
        font-weight: 600;
        color: #1a202c;
      }
      .dialog-subtitle {
        font-size: 14px;
        color: #718096;
      }
      .dialog-message {
        margin-bottom: 24px;
        color: #4a5568;
      }
      .dialog-actions {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
      }
      .font-medium {
        font-weight: 500;
      }
    `
  ],
  standalone: true,
  imports: [CommonModule, MatDialogModule, MatButtonModule, MatIconModule]
})
export class DeleteConfirmDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<DeleteConfirmDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { ruleName: string }
  ) {}
}