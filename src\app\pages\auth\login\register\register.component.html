<div class="login-container">
    <div class="login-box">
      <h2><PERSON><PERSON>er un compte</h2>
  
      <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
        <div class="input-group">
          <label>Email</label>
          <input type="email" pInputText formControlName="email" placeholder="<EMAIL>" />
        </div>
  
        <div class="input-group">
          <label>Téléphone</label>
          <input type="text" pInputText formControlName="phoneNumber" placeholder="06XXXXXXXX" />
        </div>
  
        <div class="input-group">
          <label>Rôle</label>
          <input type="text" pInputText formControlName="userRole" placeholder="ADMIN, USER, etc." />
        </div>
  
        <div class="input-group">
          <label>Mot de passe</label>
          <input type="password" pPassword formControlName="password" placeholder="••••••••" />
        </div>
  
        <div class="input-group">
          <label>Confirmer le mot de passe</label>
          <input type="password" pPassword formControlName="confirmPassword" placeholder="••••••••" />
        </div>
  
        <button pButton type="submit" label="Créer un compte" class="btn-login" [disabled]="registerForm.invalid"></button>
      </form>
  
      <div class="footer">
        <a routerLink="/login">Déjà un compte ? Se connecter</a>
      </div>
    </div>
  </div>
  