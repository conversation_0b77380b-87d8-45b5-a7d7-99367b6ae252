<div
    class="breadcrumb-nav"
    style="display: flex !important; align-items: center !important; gap: 8px"
  >
    <button
      class="back-button"
      style="
        display: flex !important;
        align-items: center !important;
        border: none;
        background: none;
        cursor: pointer;
      "
      (click)="goBack()"
    >
      <i class="material-icons">arrow_back</i>
    </button>
    <span class="breadcrumb-text">Organisations de type : {{ selectedType }}</span>
  </div>
<div *ngIf="viewMode === 'table' && !isLoading">
  <app-generic-table
    [data]="filteredOrganisations"
    [headers]="headers"
    [keys]="keys"
    [actions]="['edit', 'view', 'delete']"
    (actionTriggered)="handleAction($event)"
  >
  </app-generic-table>
</div>
  