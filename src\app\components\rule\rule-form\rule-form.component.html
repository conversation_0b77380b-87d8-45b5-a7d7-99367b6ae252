<!-- Updated rule-form.component.html - Enhanced operator and value selection -->
<div class="dialog-container">
    <!-- <PERSON><PERSON>er -->
    <div class="dialog-header">
        <h1 class="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-2">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z">
                </path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            Générateur de Règles IoT
        </h1>

        <!-- Close button -->
        <button mat-icon-button (click)="onCancel()" class="close-button">
            <mat-icon>close</mat-icon>
        </button>
    </div>

    <!-- Dialog Content -->
    <div class="dialog-content" [class.pointer-events-none]="isBusy">
        <!-- Loading State -->
        <ngx-ui-loader *ngIf="!isLoading"></ngx-ui-loader>

        <!-- Error State -->
        <div *ngIf="loadingError && !isLoading" class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                        clip-rule="evenodd"></path>
                </svg>
                <div>
                    <h3 class="text-sm font-medium text-red-800">Erreur de chargement</h3>
                    <p class="text-sm text-red-700 mt-1">{{ loadingError }}</p>
                </div>
            </div>
            <div class="mt-3">
                <button (click)="retryLoadData()"
                    class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700">
                    Réessayer
                </button>
            </div>
        </div>

        <!-- Main Content (only show when not loading and no error) -->
        <div *ngIf="!isLoading && !loadingError">
            <!-- Configuration de base -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Nom de la règle
                    </label>
                    <input type="text" [(ngModel)]="rule.rule_name"
                        class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Ex: Cube Side 1 and Temperature high" />
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Topics utilisés
                    </label>
                    <div class="p-2 border border-gray-300 rounded-md bg-gray-50 min-h-[40px]">
                        <div *ngIf="rule.topic_pattern.length === 0" class="text-gray-500 text-sm">
                            Les topics seront ajoutés automatiquement selon les appareils sélectionnés
                        </div>
                        <div *ngFor="let topic of rule.topic_pattern" class="text-sm text-blue-600 mb-1">
                            {{ topic }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Priorité
                    </label>
                    <select [(ngModel)]="rule.priority"
                        class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option *ngFor="let p of priorities" [value]="p">Priorité {{ p }}</option>
                    </select>
                </div>
                <div class="flex items-center gap-4 pt-6">
                    <label class="flex items-center gap-2">
                        <input type="checkbox" [(ngModel)]="rule.enabled" />
                        <span class="text-sm font-medium text-gray-700">Règle activée</span>
                    </label>
                </div>
            </div>

            <!-- Conditions with Drag & Drop -->
            <div class="mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold text-gray-800 flex items-center gap-2">
                        <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        Conditions
                    </h2>
                    <button (click)="addConditionGroup()"
                        class="groupe-btn">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Groupe
                    </button>
                </div>

                <!-- Global operator selector -->
                <div class="flex items-center gap-2 mb-4">
                    <label class="text-sm font-semibold text-gray-700">Opérateur global :</label>
                    <select [(ngModel)]="rule.conditions.operator"
                        class="p-1 border border-gray-300 rounded text-sm w-auto">
                        <option *ngFor="let op of operator" [value]="op">{{ op }}</option>
                    </select>
                    <span class="text-xs text-gray-500">(Tous les groupes seront reliés par cet opérateur)</span>
                </div>

                <!-- Draggable Condition Groups -->
                <div cdkDropList [id]="'condition-groups-list-' + dialogRef.id"
                    [cdkDropListData]="rule.conditions.groups" (cdkDropListDropped)="onGroupDrop($event)"
                    class="condition-groups-list">

                    <div *ngFor="let group of rule.conditions.groups; let groupIndex = index; trackBy: trackByGroupIndex"
                        cdkDrag [cdkDragData]="group" [id]="'condition-group-' + groupIndex + '-' + dialogRef.id"
                        class="bg-gray-50 p-4 rounded-md mb-4 border-2 border-gray-200 draggable-group">

                        <!-- Drag Handle for Group -->
                        <div cdkDragHandle class="drag-handle group-drag-handle"
                            title="Glisser pour réorganiser les groupes">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 8h16M4 16h16"></path>
                            </svg>
                        </div>

                        <!-- Group Header -->
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center gap-3">
                                <span class="text-sm font-semibold text-gray-700">Groupe {{ groupIndex + 1 }}</span>
                                <select [(ngModel)]="group.operator" class="p-1 border border-gray-300 rounded text-sm">
                                    <option *ngFor="let op of operator" [value]="op">{{ op }}</option>
                                </select>
                            </div>
                            <div class="flex gap-2">
                                <button (click)="addCondition(groupIndex)"
                                    class="groupe-btn">
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Condition
                                </button>
                                <button (click)="addTimeCondition(groupIndex)"
                                    class="groupe-btn">
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Horaire
                                </button>
                                <button *ngIf="rule.conditions.groups.length > 1"
                                    (click)="removeConditionGroup(groupIndex)"
                                    class="text-red-600 hover:text-red-800 p-1">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Draggable Conditions in Group -->
                        <div cdkDropList [id]="'conditions-list-' + groupIndex + '-' + dialogRef.id"
                            [cdkDropListData]="group.conditions"
                            (cdkDropListDropped)="onConditionDrop($event, groupIndex)" class="conditions-list">

                            <div *ngFor="let condition of group.conditions; let conditionIndex = index; trackBy: trackByConditionIndex"
                                cdkDrag [cdkDragData]="condition"
                                [id]="'condition-' + groupIndex + '-' + conditionIndex + '-' + dialogRef.id"
                                class="bg-white p-3 rounded-md mb-2 border border-gray-200 draggable-condition">

                                <!-- Drag Handle for Condition -->
                                <div cdkDragHandle class="drag-handle condition-drag-handle"
                                    title="Glisser pour réorganiser les conditions">
                                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
                                    </svg>
                                </div>

                                <!-- Sensor Data Condition -->
                                <div *ngIf="condition.type === 'payload'" class="grid grid-cols-1 md:grid-cols-5 gap-3">
                                    <div>
                                        <label class="block text-xs font-medium text-gray-600 mb-1">Capteur</label>
                                        <select [(ngModel)]="condition.device"
                                            (ngModelChange)="onConditionDeviceChange(condition, 'sensors', $event)"
                                            class="w-full p-2 border border-gray-300 rounded-md text-sm">
                                            <option *ngFor="let deviceKey of getSensorDeviceNames()"
                                                [value]="deviceKey">
                                                {{ deviceTypes.sensors[deviceKey].DisplayName ||
                                                deviceTypes.sensors[deviceKey].device_name }}
                                            </option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-xs font-medium text-gray-600 mb-1">Propriété</label>
                                        <select [(ngModel)]="condition.key"
                                            (ngModelChange)="onConditionKeyChange(condition)"
                                            class="w-full p-2 border border-gray-300 rounded-md text-sm">
                                            <option
                                                *ngFor="let prop of getPropertiesForDevice('sensors', condition.device)"
                                                [value]="prop.key">
                                                {{ prop.key }} ({{ prop.type }})
                                            </option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-xs font-medium text-gray-600 mb-1">Opérateur</label>
                                        <!-- ENHANCED: Dynamic operators based on property type -->
                                        <select [(ngModel)]="condition.operator"
                                            (ngModelChange)="onConditionOperatorChange(condition)"
                                            class="w-full p-2 border border-gray-300 rounded-md text-sm">
                                            <option value="">Choisir...</option>
                                            <option *ngFor="let operator of getAvailableOperators(condition)"
                                                [value]="operator.value">
                                                {{ operator.label }}
                                            </option>
                                        </select>
                                        <!-- Type indicator -->
                                        <div *ngIf="getSelectedProperty(condition)" class="text-xs text-gray-500 mt-1">
                                            Type: {{ getSelectedProperty(condition)?.type }}
                                        </div>
                                    </div>
                                    <!-- Final Value Selection Section - Replace the existing value div -->
                                    <div>
                                        <label class="block text-xs font-medium text-gray-600 mb-1">Valeur</label>
                                        <ng-container *ngIf="getSelectedProperty(condition); let selectedProp">
                                            <!-- Input with datalist for suggestions -->
                                            <div class="relative">
                                                <!-- Number input for numeric types -->
                                                <input type="number" [value]="condition.value || ''"
                                                    (input)="updateConditionValue(condition, $event)"
                                                    (blur)="onConditionValueBlur(condition, $event)"
                                                    [placeholder]="getValuePlaceholder(condition)"
                                                    class="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                    [class.border-red-300]="condition.value && !isValidValue(condition, condition.value)"
                                                    [class.focus:border-red-500]="condition.value && !isValidValue(condition, condition.value)"
                                                    autocomplete="off" spellcheck="false"
                                                    [id]="'input-' + groupIndex + '-' + conditionIndex"
                                                    *ngIf="getSelectedProperty(condition)?.type == 'number'">

                                                <!-- Enhanced Select for non-numeric types with auto-selection -->
                                                <select [value]="getSelectDisplayValue(condition)"
                                                    (change)="updateConditionValueFromSelect(condition, $event)"
                                                    [id]="'select-' + groupIndex + '-' + conditionIndex"
                                                    class="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                    *ngIf="getSelectedProperty(condition)?.type != 'number' && hasSuggestedValues(condition)">
                                                    <!-- No placeholder option - directly show suggested values -->
                                                    <option
                                                        *ngFor="let suggestion of getSuggestedValues(condition); trackBy: trackBySuggestion"
                                                        [value]="suggestion">
                                                        {{ suggestion }}
                                                    </option>
                                                </select>

                                                <!-- Fallback text input for non-numeric types without suggestions -->
                                                <input type="text" [value]="condition.value || ''"
                                                    (input)="updateConditionValue(condition, $event)"
                                                    (blur)="onConditionValueBlur(condition, $event)"
                                                    [placeholder]="getValuePlaceholder(condition)"
                                                    class="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                    [class.border-red-300]="condition.value && !isValidValue(condition, condition.value)"
                                                    [class.focus:border-red-500]="condition.value && !isValidValue(condition, condition.value)"
                                                    autocomplete="off" spellcheck="false"
                                                    [id]="'input-text-' + groupIndex + '-' + conditionIndex"
                                                    *ngIf="getSelectedProperty(condition)?.type != 'number' && !hasSuggestedValues(condition)">

                                                <!-- Validation indicator -->
                                                <div *ngIf="condition.value && !isValidValue(condition, condition.value)"
                                                    class="text-xs text-red-500 mt-1 flex items-center gap-1">
                                                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd"
                                                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                                                            clip-rule="evenodd"></path>
                                                    </svg>
                                                    Valeur invalide pour le type {{ selectedProp.type }}
                                                </div>

                                                <!-- Info indicator for suggested values -->
                                                <div *ngIf="hasSuggestedValues(condition)"
                                                    class="text-xs text-green-600 mt-1 flex items-center gap-1">
                                                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd"
                                                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                                                            clip-rule="evenodd"></path>
                                                    </svg>
                                                    {{ getSuggestedValues(condition).length }} valeur(s)
                                                    pré-configurée(s)
                                                </div>
                                            </div>
                                        </ng-container>

                                        <!-- Disabled state when no property is selected -->
                                        <input *ngIf="!getSelectedProperty(condition)" type="text"
                                            [value]="condition.value || ''"
                                            placeholder="Sélectionner d'abord une propriété"
                                            class="w-full p-2 border border-gray-300 rounded-md text-sm bg-gray-100"
                                            disabled>
                                    </div>
                                    <div class="flex items-end">
                                        <button (click)="removeCondition(groupIndex, conditionIndex)"
                                            class="text-red-600 hover:text-red-800 p-2">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                                </path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <!-- Time Condition -->
                                <div *ngIf="condition.type === 'time'" class="grid grid-cols-1 md:grid-cols-3 gap-3">
                                    <div>
                                        <label class="block text-xs font-medium text-gray-600 mb-1">Heure début</label>
                                        <input type="time" [(ngModel)]="condition.start_time"
                                            (ngModelChange)="onTimeConditionChange(condition)"
                                            class="w-full p-2 border border-gray-300 rounded-md text-sm" />
                                    </div>
                                    <div>
                                        <label class="block text-xs font-medium text-gray-600 mb-1">Heure fin</label>
                                        <input type="time" [(ngModel)]="condition.end_time"
                                            (ngModelChange)="onTimeConditionChange(condition)"
                                            class="w-full p-2 border border-gray-300 rounded-md text-sm" />
                                    </div>
                                    <div class="flex items-end">
                                        <button (click)="removeCondition(groupIndex, conditionIndex)"
                                            class="text-red-600 hover:text-red-800 p-2">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                                </path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions with Drag & Drop -->
            <div class="mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">Actions</h2>
                    <div class="flex gap-2">
                        <button (click)="addAction()"
                            class="groupe-btn">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Action
                        </button>
                        <button (click)="addLogAction()"
                            class="groupe-btn">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Log
                        </button>
                    </div>
                </div>

                <!-- Draggable Actions -->
                <div cdkDropList [cdkDropListData]="rule.actions" (cdkDropListDropped)="onActionDrop($event)"
                    class="actions-list">

                    <div *ngFor="let action of rule.actions; let i = index; trackBy: trackByActionIndex" cdkDrag
                        [cdkDragData]="action"
                        class="bg-gray-50 p-4 rounded-md mb-3 border border-gray-200 draggable-action">

                        <!-- Drag Handle for Action -->
                        <div cdkDragHandle class="drag-handle action-drag-handle"
                            title="Glisser pour réorganiser les actions">
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
                            </svg>
                        </div>

                        <div *ngIf="action.type !== 'log'" class="grid grid-cols-1 md:grid-cols-3 gap-3">
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">Actionneur</label>
                                <select [(ngModel)]="action.topic"
                                    (ngModelChange)="onActionDeviceChange(action, $event)"
                                    class="w-full p-2 border border-gray-300 rounded-md text-sm">
                                    <option *ngFor="let deviceKey of getActuatorDeviceNames()"
                                        [value]="deviceTypes.actuators[deviceKey].topic">
                                        {{ deviceTypes.actuators[deviceKey].DisplayName ||
                                        deviceTypes.actuators[deviceKey].device_name }}
                                    </option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">Type d'action</label>
                                <select [(ngModel)]="action.type" (ngModelChange)="onActionTypeChange(action, $event)"
                                    class="w-full p-2 border border-gray-300 rounded-md text-sm">
                                    <ng-container
                                        *ngIf="getDeviceNameKeyByTopic(action.topic, 'actuators'); let deviceKey">
                                        <option *ngFor="let act of getActionsForDevice('actuators', deviceKey)"
                                            [value]="act.type">
                                            {{ capitalizeFirst(act.type) }}
                                        </option>
                                    </ng-container>
                                </select>
                            </div>
                            <div class="flex items-end">
                                <button (click)="removeAction(i)" class="text-red-600 hover:text-red-800 p-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <div *ngIf="action.type && action.topic && action.type !== 'log'" class="mt-2">
                            <label class="block text-xs font-medium text-gray-600 mb-1">Valeur du Payload</label>
                            <ng-container *ngIf="getSelectedActionType(action); let selectedAction">
                                <select *ngIf="selectedAction.options && selectedAction.options.length > 0"
                                    [(ngModel)]="action.payload[action.type]"
                                    (ngModelChange)="onActionPayloadChange(action)"
                                    class="w-full p-2 border border-gray-300 rounded-md text-sm">
                                    <option *ngFor="let opt of selectedAction.options" [value]="opt.value">
                                        {{ opt.display }}
                                    </option>
                                </select>
                                <input *ngIf="!selectedAction.options || selectedAction.options.length === 0"
                                    type="text" [(ngModel)]="action.payload[action.type]"
                                    (ngModelChange)="onActionPayloadChange(action)" placeholder="Entrer une valeur"
                                    class="w-full p-2 border border-gray-300 rounded-md text-sm">
                            </ng-container>
                        </div>

                        <div *ngIf="action.type === 'log'" class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">Message de log</label>
                                <input type="text" [(ngModel)]="action.message"
                                    (ngModelChange)="onActionMessageChange(action)"
                                    class="w-full p-2 border border-gray-300 rounded-md text-sm"
                                    placeholder="Message à enregistrer" />
                            </div>
                            <div class="flex items-end">
                                <button (click)="removeAction(i)" class="text-red-600 hover:text-red-800 p-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- JSON Preview -->
            <div class="mt-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">Aperçu de la règle (JSON)</h3>
                <pre
                    class="bg-gray-900 text-green-400 p-4 rounded-md text-sm overflow-x-auto max-h-48">{{ generateJSON() }}</pre>
            </div>
        </div>
    </div>

    <!-- Dialog Footer -->
    <div class="dialog-footer">
        <button mat-button (click)="onCancel()" class="cancel-button" [disabled]="isBusy">
            <mat-icon>cancel</mat-icon>
            Annuler
        </button>

        <button mat-button (click)="downloadJSON()" class="download-button" [disabled]="isBusy">
            <mat-icon>download</mat-icon>
            Télécharger JSON
        </button>

        <button mat-raised-button (click)="onSave()" class="save-button" [disabled]="!rule.rule_name.trim() || isBusy">
            <mat-icon>save</mat-icon>
            Enregistrer la Règle
        </button>
    </div>

    <!-- Busy Indicator -->
    <div *ngIf="isBusy"
        class="absolute inset-0 bg-white bg-opacity-75 z-50 flex items-center justify-center pointer-events-auto">
        <div class="flex flex-col items-center">
            <div class="animate-spin rounded-full h-12 w-12 border-t-4 border-primary border-solid mb-4"></div>
            <span class="text-primary font-semibold text-lg">{{ statusMessage }}</span>
            <div class="progress-bar mt-2">
                <div class="progress-bar-fill" [style.width.%]="operationProgress"></div>
            </div>
        </div>
    </div>
</div>