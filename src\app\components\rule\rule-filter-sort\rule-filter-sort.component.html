<!-- rule-filter-sort.component.html -->
<div class="sorting-filtering-controls" *ngIf="!isLoading">
    <div class="quick-sort-section">
        <div class="sort-controls">
            <span class="sort-label">Trier par:</span>
            <div class="sort-options">
                <button *ngFor="let option of sortOptions" mat-stroked-button [class.active]="isSortedBy(option.value)"
                    (click)="handleSortClick(option.value)" [disabled]="hasPendingOperations" class="sort-button">
                    <span>{{ option.label }}</span>
                    <mat-icon class="sort-icon">{{ getSortIcon(option.value) }}</mat-icon>
                </button>
            </div>
        </div>
    </div>
</div>