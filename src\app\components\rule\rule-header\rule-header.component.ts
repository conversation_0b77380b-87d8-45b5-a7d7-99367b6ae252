import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-rule-header',
  templateUrl: `./rule-header.component.html`,
  styleUrls: [`./rule-header.component.css`],
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule
  ]
})
export class RuleHeaderComponent {
  @Input() isLoading: boolean | null = false;
  @Output() refreshData = new EventEmitter<void>();
}