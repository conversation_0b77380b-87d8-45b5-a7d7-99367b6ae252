<div class="ai-rule-generator-dialog">
  <div class="dialog-header">
    <mat-icon class="ai-icon">smart_toy</mat-icon>
    <h2 mat-dialog-title>Générer une Règle avec l'IA</h2>
  </div>

  <mat-dialog-content class="dialog-content">
    <ngx-ui-loader [fgsColor]="'var(--primary)'"></ngx-ui-loader>

    <div *ngIf="!showPreview" class="input-card">
      <p class="description">
        Décrivez la règle que vous souhaitez créer en langage naturel.
        L'IA générera automatiquement la configuration complète de la règle.
      </p>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Description de la règle</mat-label>
        <textarea matInput [(ngModel)]="ruleDescription" [disabled]="isGenerating"
          placeholder="Ex: Allumer les lumières du bureau quand quelqu'un entre dans la pièce entre 8h et 18h en semaine"
          rows="4" maxlength="500">
        </textarea>
        <mat-hint>{{ ruleDescription.length }}/500 caractères</mat-hint>
      </mat-form-field>

      <div class="ai-tips">
        <h4>
          <mat-icon>lightbulb</mat-icon>
          Conseils pour une meilleure génération
        </h4>
        <ul>
          <li>Soyez précis sur les conditions de déclenchement</li>
          <li>Spécifiez les actions à effectuer</li>
          <li>Mentionnez les horaires ou contraintes temporelles</li>
          <li>Indiquez les appareils ou zones concernés</li>
        </ul>
      </div>

    </div>

    <div *ngIf="isGenerating" class="generating-status">
      <span>Génération de la règle en cours...</span>
    </div>

    <div *ngIf="showPreview && generatedRule" class="preview-section">
      <h3>Règle générée</h3>
      <p><strong>Résumé:</strong> {{ generatedSummary }}</p>

      <mat-accordion>
        <mat-expansion-panel>
          <mat-expansion-panel-header>
            <mat-panel-title>
              Détails de la règle
            </mat-panel-title>
          </mat-expansion-panel-header>
          <pre>{{ formatRuleForDisplay(generatedRule) }}</pre>
        </mat-expansion-panel>
      </mat-accordion>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions class="dialog-actions" align="end">
    <button mat-button (click)="onCancel()" [disabled]="isGenerating || isSaving" class="cancel-button">
      <mat-icon>close</mat-icon>
      Annuler
    </button>

    <ng-container *ngIf="!showPreview">
      <button mat-raised-button color="primary" (click)="onGenerate()"
        [disabled]="!ruleDescription.trim() || isGenerating" class="generate-button">
        <mat-spinner *ngIf="isGenerating" diameter="20"></mat-spinner>
        <mat-icon *ngIf="!isGenerating">auto_awesome</mat-icon>
        {{ isGenerating ? 'Génération...' : 'Générer la Règle' }}
      </button>
    </ng-container>

    <ng-container *ngIf="showPreview">
      <button mat-button (click)="onEdit()" [disabled]="isSaving" class="edit-button">
        <mat-icon>edit</mat-icon>
        Modifier
      </button>
      <button mat-raised-button color="primary" (click)="onConfirm()" [disabled]="isSaving" class="confirm-button">
        <mat-spinner *ngIf="isSaving" diameter="20"></mat-spinner>
        <mat-icon *ngIf="!isSaving">check</mat-icon>
        {{ isSaving ? 'Sauvegarde...' : 'Confirmer' }}
      </button>
    </ng-container>
  </mat-dialog-actions>
  <ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>

</div>