import { RuleExecutionChartData, RuleExecutionSimple } from "@app/core/services/administrative/rules.service";
import { RuleApplication } from "./RuleApplication";
import { RulePerformanceAnalytic } from "./RulePerformanceAnalytic";


export interface ControllerForRule {
  id: string;
  name: string;
  model: string;
  lastSeen: string;
  status: boolean;
  // UPDATED: Use new execution types instead of old ones
  executions: RuleExecutionSimple[];
  executionChartData: RuleExecutionChartData[];
}
