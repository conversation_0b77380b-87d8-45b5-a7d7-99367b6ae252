import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { AuthService } from '@app/core/services/auth.service';
import { MatIconModule } from '@angular/material/icon';
import { trigger, style, animate, transition } from '@angular/animations';

@Component({
  selector: 'app-header',
  standalone: true,
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.css'],
  imports: [
    CommonModule,
    MatIconModule
  ],
  animations: [
    trigger('dropdownAnimation', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(-10px)' }),
        animate('200ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ]),
      transition(':leave', [
        animate('150ms ease-in', style({ opacity: 0, transform: 'translateY(-10px)' }))
      ])
    ])
  ]
})
export class HeaderComponent implements OnInit {
  @Input() isSidebarCollapsed = false;

  userMenuItems = [];
  profileMenuVisible = false;
  userInitials = '';
  UserName: string = '';
  userRole: string = '';

  constructor(
    public authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    const user = this.authService.getCurrentUser();
  
    this.UserName = user?.user?.username || 'Utilisateur';
    this.userRole = (user?.user?.roles && user.user.roles.length > 0)
      ? user.user.roles.join(', ')
      : 'Rôle non défini';
  
    this.setUserInitials();
  }
   

  toggleProfileMenu(): void {
    this.profileMenuVisible = !this.profileMenuVisible;
  }

  logout(): void {
    this.authService.logout();
    this.profileMenuVisible = false;
  }

  private setUserInitials(): void {
    const username = this.authService.getUsername() || '';
    const names = username.split(' ');
    this.userInitials = names.map(n => n.charAt(0).toUpperCase()).join('').substring(0, 2);
  }
}
