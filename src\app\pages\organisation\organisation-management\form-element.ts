import { FormElement } from "@app/shared/models/form-element";
 
 
export let formElements: FormElement[] = [
  {
    name: 'IdOrganisation',
    libelle: 'Organisation',
    isRequired: true,
    type: 'string',
  },
  {
    name: 'Name',
    libelle: 'Raison Sociale',
    isRequired: true,
    type: 'string',
  },
  {
    name: 'Address',
    libelle: 'Adresse',
    isRequired: true,
    type: 'string',
  },
  {
    name: 'PhoneNumber',
    libelle: 'Téléphone',
    isRequired: true,
    type: 'string',
    min: 9,
    max: 14,
  },
  {
    name: 'CompanyCreationDate',
    libelle: "Date de création d'entreprise",
    isRequired: true,
    type: 'number',
  },
  {
    name: 'LegalForm',
    libelle: 'Forme Juridique',
    isRequired: true,
    type: 'string',
  },
  {
    name: 'BusinessSector',
    libelle: "Secteur d'activité",
    isRequired: true,
    type: 'string',
  },
  {
    name: '<PERSON>',
    libelle: 'RC',
    isRequired: true,
    type: 'string',
  },
  {
    name: 'IF',
    libelle: 'IF',
    isRequired: true,
    type: 'string',
  },
  {
    name: 'ICE',
    libelle: 'ICE',
    isRequired: true,
    type: 'string',
  },
  {
    name: 'Patente',
    libelle: 'Patente',
    isRequired: true,
    type: 'string',
  },
  {
    name: 'SIRET',
    libelle: 'SIRET',
    isRequired: true,
    type: 'string',
  },
  {
    name: 'SIREN',
    libelle: 'SIREN',
    isRequired: true,
    type: 'string',
  },
  {
    name: 'ContactEmail',
    libelle: 'Email du contact',
    isRequired: true,
    type: 'string'
  }
]