import { Injectable } from '@angular/core';
import { FormElement } from '../models/form-element';

@Injectable({
  providedIn: 'root'
})
export class FormaValidationService {
  validate(target: any, formElements: FormElement[]): string[] {
    const messages: string[] = [];

    for (const formElement of formElements) {
      const value = target[formElement?.name];
      const message = this.getErrorMessage(value, formElement);
      if (message !== null) {
        messages.push(message);
      }
    }

    return messages;
  }

  isValid(target: any, formElements: FormElement[]): boolean {
    for (const formElement of formElements) {
      const value = target[formElement?.name];
      const message = this.getErrorMessage(value, formElement);
      if (message !== null) {
        return false;
      }
    }

    return true;
  }

  private getErrorMessage(val: any, formElement: FormElement): string | null {
    const { libelle, isRequired, isNotUnique, type, min, max, pattern  } = formElement;

    if (this.isEmpty(val)) {
      return isRequired ? this.getRequiredMessage(libelle) : null;
    }

    if (isNotUnique) {
      return this.getUniqueMessage(libelle);
    }

    if (type === 'number') {
      const numberVal = parseFloat(val);

      if (!isNaN(numberVal)) {
        if (min != null && max != null && (numberVal < min || numberVal > max)) {
          return this.getBetweenNumbersMessage(libelle, min, max);
        } else if (min != null && numberVal < min) {
          return this.getMinNumberMessage(libelle, min);
        } else if (max != null && numberVal > max) {
          return this.getMaxNumberMessage(libelle, max);
        }
      }
    }

    if (type === 'string') {
      const strVal = (val || '').toString().trim();
      const length = strVal.length;

      if (min != null && max != null && (length < min || length > max)) {
        return this.getBetweenLengthMessage(libelle, min, max);
      } else if (min != null && length < min) {
        return this.getMinLengthMessage(libelle, min);
      } else if (max != null && length > max) {
        return this.getMaxLengthMessage(libelle, max);
      }

      if (pattern) {
        const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;
        if (!regex.test(strVal)) {
          return this.getPatternMessage(libelle);
        }
      }
    }

    return null;
  }

  private isEmpty(val: any): boolean {
    if (typeof val === 'string') {
      val = val.trim();
    }
    return val === undefined || val === null || val === '' || (Array.isArray(val) && val.length === 0);
  }

  private getRequiredMessage(name?: string): string {
    return `Le champ ${name ?? 'inconnu'} est obligatoire.`;
  }

  private getBetweenNumbersMessage(name?: string, min?: number, max?: number): string {
    return `La valeur du champ ${name} doit être comprise entre ${min} et ${max}.`;
  }

  private getMinNumberMessage(name?: string, min?: number): string {
    return `La valeur du champ ${name} doit être supérieure à ${min}.`;
  }

  private getMaxNumberMessage(name?: string, max?: number): string {
    return `La valeur du champ ${name} doit être inférieure à ${max}.`;
  }

  private getBetweenLengthMessage(name?: string, min?: number, max?: number): string {
    return `La valeur du champ ${name} doit contenir entre ${min} et ${max} caractères.`;
  }

  private getMinLengthMessage(name?: string, min?: number): string {
    return `La valeur du champ ${name} doit contenir au moins ${min} caractères.`;
  }

  private getMaxLengthMessage(name?: string, max?: number): string {
    return `La valeur du champ ${name} doit contenir au plus ${max} caractères.`;
  }

  private getUniqueMessage(name?: string): string {
    return `La valeur du champ ${name} existe déjà. Merci de saisir une nouvelle valeur.`;
  }

  private getPatternMessage(name?: string): string {
    return `Le champ ${name ?? 'inconnu'} ne respecte pas le format attendu.`;
  }
}
