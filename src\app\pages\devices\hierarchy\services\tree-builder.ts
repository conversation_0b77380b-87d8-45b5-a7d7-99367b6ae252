function buildTree(data: any[]): any {
  const root: any = {
    name: data[0]?.ClientName,
    type: 'client',
    children: []
  };

  const siteMap = new Map();
  const localMap = new Map();
  const controllerMap = new Map();

  data.forEach(item => {
    const siteKey = item.SiteId;
    const localKey = item.LocalId;
    const controllerKey = item.ControllerId;

    // SITE
    if (!siteMap.has(siteKey)) {
      const siteNode = {
        name: item.SiteName,
        type: 'site',
        children: []
      };
      siteMap.set(siteKey, siteNode);
      root.children.push(siteNode);
    }

    // LOCAL
    const siteNode = siteMap.get(siteKey);
    if (!localMap.has(localKey)) {
      const localNode = {
        name: item.LocalName,
        type: 'local',
        children: []
      };
      localMap.set(localKey, localNode);
      siteNode.children.push(localNode);
    }

    // CONTROLLER
    const localNode = localMap.get(localKey);
    if (!controllerMap.has(controllerKey)) {
      const controllerNode = {
        name: `Controller ${controllerKey.slice(0, 8)}`,
        type: 'controller',
        children: []
      };
      controllerMap.set(controllerKey, controllerNode);
      localNode.children.push(controllerNode);
    }

    // SENSOR
    const controllerNode = controllerMap.get(controllerKey);
    const sensorNode = {
      name: item.DisplayName || item.FriendlyName,
      type: 'capteur',
      data: item
    };
    controllerNode.children.push(sensorNode);
  });

  return root;
}
