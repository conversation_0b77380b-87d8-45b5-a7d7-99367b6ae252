import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MaterialModule } from '../../core/models/material.module';
import { TableConfig, Column } from '../../core/models/table-config.module';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-data-table',
  standalone: true,
  imports: [
    CommonModule, 
    FormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatTooltipModule
  ],
  templateUrl: './data-table.component.html',
  styleUrls: ['./data-table.component.css']
})
export class DataTableComponent implements OnInit {

  protected readonly Object = Object;

  @Input() data: any[] = [];
  @Input() config!: TableConfig;
  @Input() loading = false;

  @Output() rowAction = new EventEmitter<{action: string, item: any}>();
  @Output() pageChange = new EventEmitter<{pageIndex: number, pageSize: number}>();
  @Output() sortChange = new EventEmitter<{active: string, direction: string}>();
  @Output() filterChange = new EventEmitter<{[key: string]: any}>();

  displayedColumns: string[] = [];
  filters: {[key: string]: any} = {};
  sortedColumn: string = '';
  sortDirection: 'asc' | 'desc' = 'asc';
  
  currentPage = 0;
  pageSize: number;
  totalItems = 0;

  constructor() {
    this.pageSize = 10;
  }

  ngOnInit() {
    this.initializeColumns();
    this.pageSize = this.config.pageSize || 10;
  }

  private initializeColumns() {
    this.displayedColumns = this.config.columns.map(col => col.key);
    if (this.config.actions) {
      this.displayedColumns.push('actions');
    }
  }

  onFilter(column: Column, value: any) {
    this.filters[column.key] = value;
    this.filterChange.emit(this.filters);
  }

  clearFilter(key: string): void {
    delete this.filters[key];
    this.filterChange.emit(this.filters);
  }

  clearAllFilters(): void {
    this.filters = {};
    this.filterChange.emit(this.filters);
  }

  onSort(column: Column) {
    if (!column.sortable) return;

    if (this.sortedColumn === column.key) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortedColumn = column.key;
      this.sortDirection = 'asc';
    }

    this.sortChange.emit({
      active: this.sortedColumn,
      direction: this.sortDirection
    });
  }

  onPageChange(event: any) {
    this.pageChange.emit({
      pageIndex: event.pageIndex,
      pageSize: event.pageSize
    });
  }

  onAction(action: string, item: any) {
    this.rowAction.emit({ action, item });
  }

  formatValue(column: Column, value: any): string {
    if (column.format) {
      return column.format(value);
    }
    return value?.toString() || '';
  }


}