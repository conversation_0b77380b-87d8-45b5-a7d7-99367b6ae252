import { Component, OnInit, ViewChild } from '@angular/core';
import { SensorsBackEndDataService } from '@app/core/sensors-back-end-data.service';

import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatTableModule } from '@angular/material/table';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';

import { MatTableDataSource } from '@angular/material/table';

interface SensorSummary {
  sensorType: string;
  value: number | string | null;
  unit: string | null;
  battery: number | null;
  timestamp: string | null; // ISO string for date pipe
}

@Component({
  selector: 'app-iot',
  templateUrl: './iot.component.html',
  styleUrls: ['./iot.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatDialogModule,
    MatSnackBarModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    FormsModule,
  ],
})
export class IotComponent implements OnInit {
  displayedColumns: string[] = [
    'sensorType',
    'value',
    'unit',
    'battery',
    'timestamp',
  ];

  dataSource = new MatTableDataSource<SensorSummary>([]);

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  constructor(private sensorsBackEndService: SensorsBackEndDataService) {}

  ngOnInit(): void {
    this.loadBackendSensors();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  loadBackendSensors(): void {
    this.sensorsBackEndService.getRowDataOnly().subscribe({
      next: (data: any[]) => {
        console.log('Raw backend sensor data:', data);

        const mappedData = data.map((item) => {
          const rowData = item.RowData || {};
          const state = item.State || {};

          // Dynamically pick the first known sensor value key in State
          const preferredKeys = [
            'device_temperature',
            'illuminance',
            'humidity',
            'motion',
            'water_leak',
            'contact',
            'presence',
            'temperature',
            'pressure',
          ];
          let value: number | string | null = null;
          let unit: string | null = null;

          for (const key of preferredKeys) {
            if (state[key] !== undefined) {
              value = state[key];
              // Simple unit map for known keys
              const unitMap: { [key: string]: string } = {
                device_temperature: '°C',
                temperature: '°C',
                illuminance: 'lx',
                humidity: '%',
                motion: '',
                water_leak: '',
                contact: '',
                presence: '',
                pressure: 'hPa',
              };
              unit = unitMap[key] ?? null;
              break;
            }
          }

          // Use RowData.LastSeenOriginal (ISO string) or fallback to RowData.LastSeen (localized string)
          // We want ISO string to use Angular DatePipe properly
          let timestamp: string | null = null;
          if (rowData.LastSeenOriginal) {
            timestamp = rowData.LastSeenOriginal;
          } else if (rowData.LastSeen) {
            // Try to parse local date string back to ISO
            const parsedDate = new Date(rowData.LastSeen);
            timestamp = isNaN(parsedDate.getTime())
              ? null
              : parsedDate.toISOString();
          }

          return {
            sensorType: rowData.friendly_name || rowData.TypeCapteur || 'N/A',
            value,
            unit,
            battery: state.battery ?? null,
            timestamp,
          };
        });

        console.log('Mapped backend sensor data:', mappedData);

        this.dataSource.data = mappedData;
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
      },
      error: (err: any) => {
        console.error(
          'Erreur lors du chargement des données capteurs backend.',
          err
        );
      },
    });
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();

    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  // Method to get battery level CSS class for color coding
  getBatteryClass(battery: number | null): string {
    if (!battery) return '';
    if (battery > 70) return 'battery-high';
    if (battery > 30) return 'battery-medium';
    return 'battery-low';
  }
}