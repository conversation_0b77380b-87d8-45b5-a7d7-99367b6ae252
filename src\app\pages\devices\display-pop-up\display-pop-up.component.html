<div class="overlay" *ngIf="show">
  <div class="confirmation-popup">
    <div class="popup-header">
      <h3>{{ title }}</h3>
      <button class="close-popup-btn" (click)="closePopup()">
        <span class="material-icons">close</span>
      </button>
    </div>
    <div class="device-details" *ngIf="sensorData">
    <div class="device-info" *ngIf="sensorData.friendly_name">
        <span class="bullet">•</span>
        <span class="device-info-label">Friendly Name:</span>
        <span class="device-info-value">{{ sensorData.friendly_name }}</span>
    </div>

    <div class="device-info" *ngIf="sensorData.Model">
        <span class="bullet">•</span>
        <span class="device-info-label">Model:</span>
        <span class="device-info-value">{{ sensorData.Model }}</span>
    </div>
    </div>




    <div class="popup-content">
      <div class="add-option-inputs-wrapper">
        <div
          class="form-group improved-form-group"
          *ngFor="let input of inputs"
        >
          <label class="label">{{ input.label }}</label>
          <div class="input-icon-wrapper">
            <input
              [type]="input.type || 'text'"
              class="form-input improved-input"
                [(ngModel)]="formValues[input.key]"
            />
            <button
              *ngIf="formValues[input.key]"
              class="input-clear-btn"
              type="button"
              (click)="formValues[input.key] = ''"
              aria-label="Effacer"
            >
              <span class="material-icons">close</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="popup-actions">
      <button class="cancel-btn" (click)="closePopup()">Annuler</button>
      <button class="confirm-btn" (click)="confirm()">Confirmer</button>
    </div>
  </div>
</div>
