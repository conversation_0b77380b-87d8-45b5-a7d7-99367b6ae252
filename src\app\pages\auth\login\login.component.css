/* Modern Login Styles with Advanced Effects */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f7 100%);
  padding: 1rem;
  font-family: var(--font-family);
  position: relative;
  overflow: hidden;
  perspective: 1000px;
}

/* IoT Animated background */
.login-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.6;
  z-index: 0;
  background-image: 
    /* Circuit pattern */
    url("data:image/svg+xml,%3Csvg width='120' height='120' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='circuit' x='0' y='0' width='120' height='120' patternUnits='userSpaceOnUse'%3E%3Cpath d='M0,60 L30,60 L30,30 L60,30 L60,60 L90,60 L90,90 L120,90' stroke='%234CAF50' stroke-width='1' fill='none' opacity='0.3'/%3E%3Cpath d='M60,0 L60,30 L90,30 L90,60 L120,60' stroke='%234CAF50' stroke-width='1' fill='none' opacity='0.3'/%3E%3Cpath d='M0,90 L30,90 L30,120' stroke='%234CAF50' stroke-width='1' fill='none' opacity='0.3'/%3E%3Ccircle cx='30' cy='60' r='3' fill='%234CAF50' opacity='0.4'/%3E%3Ccircle cx='60' cy='30' r='3' fill='%234CAF50' opacity='0.4'/%3E%3Ccircle cx='90' cy='60' r='3' fill='%234CAF50' opacity='0.4'/%3E%3Ccircle cx='90' cy='90' r='3' fill='%234CAF50' opacity='0.4'/%3E%3Cg transform='translate(10,10)'%3E%3Cpath d='M10,15 A8,8 0 0,1 18,15 M12,15 A6,6 0 0,1 16,15 M14,15 A4,4 0 0,1 14,15' stroke='%2381C784' stroke-width='1.5' fill='none' opacity='0.5'/%3E%3Ccircle cx='14' cy='17' r='1' fill='%2381C784' opacity='0.6'/%3E%3C/g%3E%3Cg transform='translate(80,80)'%3E%3Crect x='0' y='0' width='12' height='8' rx='2' fill='none' stroke='%2381C784' stroke-width='1' opacity='0.5'/%3E%3Ccircle cx='6' cy='4' r='2' fill='%2381C784' opacity='0.4'/%3E%3Cpath d='M6,6 L6,12' stroke='%2381C784' stroke-width='1' opacity='0.5'/%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='url(%23circuit)'/%3E%3C/svg%3E"),
    /* Mesh pattern */
    url("data:image/svg+xml,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='mesh' x='0' y='0' width='100' height='100' patternUnits='userSpaceOnUse'%3E%3Cpath d='M20,20 L80,20 L80,80 L20,80 Z' stroke='%234CAF50' stroke-width='0.5' fill='none' opacity='0.2'/%3E%3Cpath d='M20,20 L80,80 M80,20 L20,80' stroke='%234CAF50' stroke-width='0.5' opacity='0.15'/%3E%3Cpath d='M50,20 L50,80 M20,50 L80,50' stroke='%234CAF50' stroke-width='0.5' opacity='0.15'/%3E%3Ccircle cx='20' cy='20' r='2' fill='%2381C784' opacity='0.3'/%3E%3Ccircle cx='80' cy='20' r='2' fill='%2381C784' opacity='0.3'/%3E%3Ccircle cx='20' cy='80' r='2' fill='%2381C784' opacity='0.3'/%3E%3Ccircle cx='80' cy='80' r='2' fill='%2381C784' opacity='0.3'/%3E%3Ccircle cx='50' cy='50' r='2.5' fill='%234CAF50' opacity='0.4'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='url(%23mesh)'/%3E%3C/svg%3E"),
    /* Dot pattern */
    url("data:image/svg+xml,%3Csvg width='80' height='80' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='dots' x='0' y='0' width='80' height='80' patternUnits='userSpaceOnUse'%3E%3Ccircle cx='20' cy='20' r='1.5' fill='%234CAF50' opacity='0.2'/%3E%3Ccircle cx='60' cy='20' r='1' fill='%2381C784' opacity='0.3'/%3E%3Ccircle cx='20' cy='60' r='1' fill='%2381C784' opacity='0.3'/%3E%3Ccircle cx='60' cy='60' r='1.5' fill='%234CAF50' opacity='0.2'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='url(%23dots)'/%3E%3C/svg%3E");
  background-size: 120px 120px, 100px 100px, 80px 80px;
  animation: iotPatternMove 20s linear infinite;
}

/* Add subtle floating animation for the background */
@keyframes iotPatternMove {
  0% {
    background-position: 0 0, 0 0, 0 0;
  }
  100% {
    background-position: 120px 120px, -100px 100px, 80px -80px;
  }
}

/* Add floating IoT elements */
.login-container::after {
  content: "";
  position: absolute;
  top: 10%;
  right: 10%;
  width: 40px;
  height: 30px;
  background: url("data:image/svg+xml,%3Csvg width='40' height='30' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='5' y='5' width='30' height='20' rx='3' fill='none' stroke='%234CAF50' stroke-width='1.5' opacity='0.4'/%3E%3Ccircle cx='20' cy='15' r='4' fill='%2381C784' opacity='0.3'/%3E%3Cpath d='M20,11 L20,7 M16,15 L12,15 M24,15 L28,15 M20,19 L20,23' stroke='%234CAF50' stroke-width='1' opacity='0.5'/%3E%3C/svg%3E") no-repeat center;
  opacity: 0.6;
  z-index: 0;
  animation: float 6s ease-in-out infinite;
}

/* Floating animation keyframes */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(2deg);
  }
}

.login-card {
  width: 100%;
  max-width: 420px;
  background: var(--white);
  border-radius: 15px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.07), 
              0 5px 15px var(--primary-shadow);
  overflow: hidden;
  padding: 2.5rem 2rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1;
  transition: transform var(--transition-normal) cubic-bezier(0.34, 1.56, 0.64, 1),
              box-shadow var(--transition-normal) ease;
  transform-style: preserve-3d;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.login-card:hover {
  box-shadow: 0 25px 50px rgba(0, 0, 0, 1), 
              0 15px 25px var(--primary-shadow);
}

/* Card header with logo */
.card-header {
  text-align: center;
  margin-bottom: 2.5rem;
  position: relative;
}

.logo-container {
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
}

.logo-container::after {
  content: "";
  position: absolute;
  width: 140%;
  height: 140%;
  top: -20%;
  left: -20%;
  background: radial-gradient(circle, rgba(76, 175, 80, 0.15) 0%, transparent 70%);
  opacity: 0.6;
  border-radius: 50%;
  z-index: -1;
}

.logo-circle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 200px;
  height: 200px;
  border-radius: 15px;
}

.logo-circle::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0.1;
  z-index: -1;
  border-radius: inherit;
}

.logo-image {
  width: 85%;
  height: 85%;
  object-fit: contain;
  border-radius: 12px;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
  transform-origin: center;
  transition: all var(--transition-normal) ease;
}

.card-subtitle {
  color: var(--text-secondary);
  font-size: 0.95rem;
  margin: 0;
  font-weight: 400;
  opacity: 0.85;
}

/* Modern input fields with centered icons */
.form-field {
  position: relative;
  margin-bottom: 1.75rem;
  transition: all var(--transition-normal) ease;
}

.p-float-label {
  display: block;
}

.p-float-label label {
  position: absolute;
  top: 1rem;
  left: 3.5rem;
  color: var(--text-secondary);
  font-weight: 400;
  pointer-events: none;
  transition: all var(--transition-fast) ease;
}

.custom-input {
  width: 100%;
  border-radius: 10px;
  padding: 1rem 1rem 1rem 3.5rem;
  border: 2px solid var(--grey-medium);
  transition: all var(--transition-normal) cubic-bezier(0.34, 1.56, 0.64, 1);
  background-color: var(--white);
  font-size: 0.95rem;
  color: var(--text-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.custom-input:enabled:hover {
  border-color: var(--primary-light);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.1);
}

.custom-input:enabled:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2), 0 6px 16px rgba(76, 175, 80, 0.1);
  outline: none;
}

.input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: white;
  z-index: 2;
  font-size: 1.2rem;
  background: var(--primary-light);
  width: 2.2rem;
  height: 2.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all var(--transition-normal) ease;
}

.form-field:focus-within .input-icon {
  background: var(--primary);
  transform: translateY(-50%) scale(1.1);
}

.form-field:focus-within label {
  color: var(--primary);
  transform: translateY(-1.2rem) scale(0.9);
  left: 3.5rem;
}

.error-message {
  color: #f44336;
  background-color: #fde8e8;
  padding: 10px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

/* Button styles */
.button-container {
  margin-top: 1.5rem;
}

.custom-button {
  width: 100%;
  padding: 1rem;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  background: var(--green-main);
  border: none;
  border-radius: 10px;
  transition: all var(--transition-normal) cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
  color: white;
  position: relative;
  overflow: hidden;
}

.custom-button:enabled:hover {
  box-shadow: 0 8px 20px rgba(46, 125, 50, 0.4);
  color: var(--green-main);
  border: var(--green-main) 1.5px solid;
  background: white;
}

.custom-button:enabled:active {
  transform: translateY(0);
  box-shadow: 0 4px 10px rgba(46, 125, 50, 0.3);
}

.custom-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.custom-button:focus:not(:active)::after {
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  100% {
    transform: scale(20, 20);
    opacity: 0;
  }
}

.button-icon {
  font-size: 1.25rem;
  margin-right: 0.75rem;
  transition: transform var(--transition-normal) ease;
}

.custom-button:hover .button-icon {
  transform: translateX(3px);
}

/* Toast notifications */
:host ::ng-deep .p-toast {
  max-width: 25rem;
}

:host ::ng-deep .p-toast .p-toast-message {
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: none;
  padding: 1.25rem 1.5rem;
}

:host ::ng-deep .p-toast .p-toast-message-success {
  background: linear-gradient(135deg, #E8F5E9, #F1F8E9);
  border-left: 4px solid var(--primary);
  color: var(--primary-dark);
}

:host ::ng-deep .p-toast .p-toast-message-success .p-toast-message-icon {
  color: var(--primary);
}

:host ::ng-deep .p-toast .p-toast-message-error {
  background: linear-gradient(135deg, #FFEBEE, #FFCDD2);
  border-left: 4px solid var(--danger);
  color: #C62828;
}

:host ::ng-deep .p-toast .p-toast-message-error .p-toast-message-icon {
  color: var(--danger);
}

:host ::ng-deep .p-toast .p-toast-message-content {
  align-items: flex-start;
}

:host ::ng-deep .p-toast .p-toast-icon-close {
  color: var(--text-secondary);
  transition: all var(--transition-fast) ease;
}

:host ::ng-deep .p-toast .p-toast-icon-close:hover {
  color: var(--text-primary);
  transform: rotate(90deg);
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .login-card {
    padding: 2rem 1.5rem;
    border-radius: 10px;
  }
  
  .logo-circle {
    width: 80px;
    height: 80px;
  }
  
  .custom-input {
    padding-left: -3rem;
  }
  
  .input-icon {
    left: 1rem;
    width: 2rem;
    height: 2rem;
    font-size: 1.1rem;
  }
  
  .p-float-label label {
    left: 3rem;
  }
  
  .form-field:focus-within label {
    left: 3rem;
  }

  .error-message {
    padding-left: 3rem;
  }
  
  /* Adjust background pattern for mobile */
  .login-container::before {
    background-size: 80px 80px, 60px 60px, 40px 40px;
    opacity: 0.4;    
  }
  
  .login-container::after {
    width: 30px;
    height: 22px;
    top: 15%;
    right: 15%;
  }
}

.footer-link {
  margin-top: 15px;
  text-align: center;
  font-size: 0.9rem;
  color: #333;
}

.footer-link a {
  color: var(--green-main);
  font-weight: bold;
  text-decoration: none;
  margin-left: 5px;
}

.footer-link a:hover {
  text-decoration: underline;
}