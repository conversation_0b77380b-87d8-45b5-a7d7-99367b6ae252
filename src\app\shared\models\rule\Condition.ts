// Interfaces for the component's internal rule representation (parsed from RawData)
// These define the structure of the JSON expected in RuleComprehensive.rawData

export interface Condition {
  type: string;
  operator: string;
  value: string;
  inputType?: string; // from getRulePreviewJson
  id?: number; // from getRulePreviewJson
  deviceId?: string | null; // from getRulePreviewJson
  propertyName?: string; // from getRulePreviewJson
}
