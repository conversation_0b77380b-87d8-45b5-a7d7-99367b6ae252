.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 24px;
    padding: 12px 20px;
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    flex-wrap: wrap;
  }

  .page-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
    padding: 0 8px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .page-number:hover:not(.active):not(.disabled) {
    background: var(--background);
    color: var(--text-primary);
  }

  .page-number.active {
    background: var(--green-main);
    color: white;
    font-weight: 600;
  }

  .page-number.disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .pagination-controls button[mat-icon-button] {
    color: var(--text-secondary);
  }

  .pagination-controls button[mat-icon-button]:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .page-info {
    margin-left: 16px;
    font-size: 13px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
  }

  .operation-indicator {
    display: flex;
    align-items: center;
    font-style: italic;
    color: var(--green-main);
    gap: 4px;
  }