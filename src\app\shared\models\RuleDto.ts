import { ConditionsStructure } from './ConditionsStructure';
import { PublishAction } from './PublishAction';

export interface RuleDto {
  id?: string;
  isEdit?: boolean;
  rule_name: string;
  topic_pattern: string[];
  conditions: ConditionsStructure;
  actions: PublishAction[];
  schedule_config: {
    enabled: boolean;
    start_time?: string;
    end_time?: string;
  };
  enabled: boolean;
  priority: number;
}
