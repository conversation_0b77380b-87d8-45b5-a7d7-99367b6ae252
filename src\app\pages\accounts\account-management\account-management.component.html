<div class="dashboard-container">
  <mat-card class="admin-card">
    <mat-card-header>
      <mat-card-title>Gestion des comptes</mat-card-title>
    </mat-card-header>
    
    <mat-card-content>
      <!-- Action Buttons -->
      <div class="button-group">
        <button mat-raised-button routerLink="/register-enterprise" class="add-button">
          <mat-icon class="action-icon">add_business</mat-icon>
          Nouveau compte entreprise
        </button>
        <button *ngIf="isSuperAdmin" mat-raised-button routerLink="/register-admin" class="add-button">
          <mat-icon class="action-icon">person_add</mat-icon>
          Nouveau compte admin
        </button>
      </div>

      <!-- Admin Users Section -->
      <div *ngIf="isSuperAdmin" class="table-section">
        <h2 class="section-title">Administrateurs</h2>
        

        <p-table
          [value]="adminUsers"
          [paginator]="true"
          [rows]="adminRows"
          [first]="adminFirst"
          [showCurrentPageReport]="true"
          [tableStyle]="{'min-width': '50rem'}"
          currentPageReportTemplate="Affichage de {first} à {last} sur {totalRecords} admins"
          (onPage)="adminPageChange($event)"
          [rowsPerPageOptions]="[5, 10, 25, 50]"
          styleClass="user-table"
          [@tableAnimation]
        >
          <ng-template pTemplate="header">
            <tr>
              <th>Nom d'utilisateur</th>
              <th>Email</th>
              <th>Nom complet</th>
              <th class="actions-column">Actions</th>
            </tr>
          </ng-template>

          <ng-template pTemplate="body" let-admin>
            <tr class="user-row">
              <td>{{ admin.userName }}</td>
              <td>{{ admin.email }}</td>
              <td>{{ admin.fullName }}</td>
              <td class="actions-cell">
                <button mat-icon-button class="action-edit" matTooltip="Modifier" (click)="openEditAdminDialog(admin)">
                  <mat-icon>edit</mat-icon>
                </button>
                <button mat-icon-button class="action-delete" matTooltip="Supprimer" (click)="deleteAdmin(admin.id)">
                  <mat-icon>delete</mat-icon>
                </button>
              </td>
            </tr>
          </ng-template>

          
        </p-table>
      </div>

      <!-- Section Divider -->
      <div *ngIf="isSuperAdmin" class="section-divider"></div>

      <!-- Enterprise Users Section -->
      <div class="table-section">
        <h2 class="section-title">Entreprises</h2>
        

        <p-table
          [value]="users"
          [paginator]="true"
          [rows]="enterpriseRows"
          [first]="enterpriseFirst"
          [showCurrentPageReport]="true"
          [tableStyle]="{'min-width': '50rem'}"
          currentPageReportTemplate="Affichage de {first} à {last} sur {totalRecords} entreprises"
          (onPage)="enterprisePageChange($event)"
          [rowsPerPageOptions]="[5, 10, 25, 50]"
          styleClass="user-table"
          [@tableAnimation]
        >
          <ng-template pTemplate="header">
            <tr>
              <th>Nom d'utilisateur</th>
              <th>Email</th>
              <th>Nom complet</th>
              <th>Entreprise</th>
              <th>Employés</th>
              <th>Date contrat</th>
              <th>Catégorie</th>
              <th class="actions-column">Actions</th>
            </tr>
          </ng-template>

          <ng-template pTemplate="body" let-user>
            <tr class="user-row">
              <td>{{ user.userName }}</td>
              <td>{{ user.email }}</td>
              <td>{{ user.fullName }}</td>
              <td>{{ user.enterpriseName }}</td>
              <td>{{ user.numberOfEmployees }}</td>
              <td>{{ user.contractDate }}</td>
              <td>{{ user.category }}</td>
              <td class="actions-cell">
                <button mat-icon-button class="action-edit" matTooltip="Modifier" (click)="openEditDialog(user)">
                  <mat-icon>edit</mat-icon>
                </button>
                <button mat-icon-button class="action-delete" matTooltip="Supprimer" (click)="deleteUser(user.id)">
                  <mat-icon>delete</mat-icon>
                </button>
              </td>
            </tr>
          </ng-template>

          
        </p-table>
      </div>

      <!-- Loading and Empty States -->
      <div class="loading-shade" *ngIf="isLoading">
        <mat-spinner diameter="40"></mat-spinner>
      </div>
      <div class="no-data" *ngIf="users.length === 0 && !isLoading">
        <mat-icon class="no-data-icon">folder_off</mat-icon>
        <p>Aucun utilisateur trouvé</p>
      </div>
    </mat-card-content>
  </mat-card>
</div>