import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import {
  FormsModule,
  ReactiveFormsModule,
  FormGroup,
  FormControl,
  Validators,
} from '@angular/forms';
import {
  trigger,
  state,
  style,
  transition,
  animate,
} from '@angular/animations';
import { Local } from '@app/core/models/local';
import { TypeLocal } from '@app/core/models/TypeLocal.1';
import { PageEvent } from '@angular/material/paginator';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';
import {
  LocalApiService,
  TypeLocalApiService,
} from '@app/core/services/administrative/local.service';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatIconModule } from '@angular/material/icon';
import { SiteApiService } from '@app/core/services/administrative/site.service';
import { Page, Lister, SortPage, FilterParam, Pagination } from '@app/core/models/util/page';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import {
  NgToastComponent,
  NgToastService,
  TOAST_POSITIONS,
} from 'ng-angular-popup';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';
import { finalize } from 'rxjs/operators';
import { TransactionApiService } from '@app/core/services/administrative/transaction.service';
import { Transaction } from '@app/core/models/transaction';

@Component({
  selector: 'app-local-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    GenericTableComponent,
    MatPaginatorModule,
    MatIconModule,
    MatDialogModule,
    NgxUiLoaderModule,
    NgToastComponent,
  ],
  templateUrl: './local-management.component.html',
  styleUrls: ['./local-management.component.css'],
  animations: [
    trigger('tableRowAnimation', [
      state('void', style({ opacity: 0, transform: 'translateY(20px)' })),
      transition('void => *', animate('300ms ease-in')),
    ]),
    trigger('fadeIn', [
      state('void', style({ opacity: 0 })),
      transition('void => *', animate('400ms 300ms ease-in')),
    ]),
  ],
})
export class LocalManagementComponent implements OnInit {
  TOAST_POSITIONS = TOAST_POSITIONS;

  constructor(
    private readonly localService: LocalApiService,
    private readonly siteService: SiteApiService,
    private readonly typeLocalService: TypeLocalApiService,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private dialog: MatDialog,
    private toast: NgToastService,
    private readonly transactionService: TransactionApiService,
    private ngxUiLoaderService: NgxUiLoaderService
  ) {}

  @ViewChild('editFormSection') editFormSection!: ElementRef;
  sites: any[] = [];
  typeLocals: TypeLocal[] = [];
  isLoading: boolean = true;
  viewMode: string = 'table';

  originalImageFileName: string | null = null;
  isImageModified = false;

  currentPage: number = 0;
  pageSize: number = 5;
  paginatedLocals: Local[] = [];
  totalCount: number = 0;

  showEditForm: boolean = false;
  selectedLocal: Local | null = null;
  imageLocalPreview: string | null = null;

  isSubmitting: boolean = false;
  searchTerm: string = '';
  locals: any[] = [];
  filteredLocals: any[] = []; 
  imageLoaded: boolean = false;

  // Table configuration
  tableConfig = {
    keys: ['Name', 'Floor', 'SensorsCount', 'Capacity', 'Site.name', 'TypeLocal.Nom'],
    headers: ['Nom', 'Étage', 'Capteurs', 'Capacité', 'Site', 'Type'],
  };

  currentSort: SortPage[] = [{
    Column: 'CreatedAt',
    Sort: 'desc'
  }];

  editLocalForm = new FormGroup({
    id: new FormControl(''),
    nom: new FormControl('', [Validators.required]),
    description: new FormControl(''),
    floor: new FormControl<number>(0),
    nombreCapteurs: new FormControl<number>(0),
    capacitePersonnes: new FormControl<number>(0),
    imageLocal: new FormControl(''),
    idSite: new FormControl('', [Validators.required]),
    typeLocalId: new FormControl('', [Validators.required]),
    latittude: new FormControl(''),
    longtitude: new FormControl('')
  });

  ngOnInit(): void {
    this.currentPage = 0;
    this.pageSize = 5;
    this.loadLocals();
    this.loadSites();
    this.loadTypeLocals();
  }

  filterLocals(): void {
    this.currentPage = 0;
  
    const rawTerm = this.searchTerm.trim();
    if (!rawTerm) {
      this.loadLocals();
      return;
    }

    const searchTerm = rawTerm.toLowerCase();
    const filters: FilterParam[] = [];
  
    const isNumeric = !isNaN(Number(rawTerm)) && rawTerm !== '';
    const numericValue = Number(rawTerm);
  
    if (isNumeric) {
      ['Floor', 'SensorsCount', 'Capacity'].forEach((field, index) => {
        filters.push({
          Column: field,
          Value: numericValue,
          Op: 'equals',
          AndOr: index === 0 ? undefined : 'OR'
        });
      });
    } else {
      ['Name'].forEach((field, index) => {
        filters.push({
          Column: field,
          Value: searchTerm,
          Op: 'contains',
          AndOr: index === 0 ? undefined : 'OR'
        });
      });
    }
  
    const request: Lister = {
      Pagination: {
        CurrentPage: this.currentPage + 1,
        PageSize: this.pageSize
      },
      SortParams: this.currentSort,
      FilterParams: filters
    };
  
    this.isLoading = true;
    this.localService.gatePage(request).subscribe({
      next: (response: Page<Local>) => {
        this.filteredLocals = response.Content ?? [];
  
        const pag = response?.Lister?.Pagination;
        if (pag && pag.totalElement !== undefined) {
          this.totalCount = pag.totalElement;
          this.pageSize = pag.PageSize ?? this.pageSize;
          this.currentPage = (pag.CurrentPage ?? 1) - 1;
        } else {
          this.totalCount = this.filteredLocals.length;
        }
  
        this.loadSensorsCountForLocals(this.filteredLocals);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Search error:', error);
        this.filteredLocals = [];
        this.totalCount = 0;
        this.isLoading = false;
      }
    });
  }

  onImageError(event: any): void {
    event.target.src = 'assets/images/placeholder.png';
  }

  onImageLoad(): void {
    this.imageLoaded = true;
  }

  loadSites(): void {
    this.isLoading = true;
    this.siteService.getAll().subscribe({
      next: (sites) => {
        this.sites = sites;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading sites:', error);
        this.isLoading = false;
      },
    });
  }

  loadTypeLocals(): void {
    this.isLoading = true;
    this.typeLocalService.getAll().subscribe({
      next: (typeLocals) => {
        this.typeLocals = typeLocals;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading typeLocals:', error);
        this.isLoading = false;
      },
    });
  }

  loadLocals(): void {
    this.ngxUiLoaderService.start();
  
    const pagination: Pagination = {
      CurrentPage: this.currentPage + 1,
      PageSize: this.pageSize
    };
  
    const request: Lister = {
      Pagination: pagination,
      SortParams: this.currentSort,
      FilterParams: this.buildFilterParams(),
    };
  
    this.isLoading = true;
  
    this.localService.gatePage(request).subscribe({
      next: (result: Page<Local>) => {
        this.filteredLocals = result.Content ?? [];
  
        const pag = result?.Lister?.Pagination;
        if (pag) {
          this.totalCount = pag.totalElement ?? this.filteredLocals.length;
          this.pageSize = pag.PageSize ?? this.pageSize;
          this.currentPage = (pag.CurrentPage ?? 1) - 1;
        } else {
          this.totalCount = this.filteredLocals.length;
        }
  
        this.loadSensorsCountForLocals(this.filteredLocals);
        this.ngxUiLoaderService.stop();
        this.isLoading = false;
      },
      error: (error) => {
        this.showError('Erreur lors du chargement des locaux', 'Erreur');
        this.filteredLocals = [];
        this.totalCount = 0;
        this.ngxUiLoaderService.stop();
        this.isLoading = false;
      }
    });
  }
  
  private loadSensorsCountForLocals(locals: Local[]): void {
    this.transactionService.getAll().subscribe({
      next: (transactions: Transaction[]) => {
        const localCapteurMap = new Map<string, Set<string>>();

        transactions.forEach((t) => {
          if (!localCapteurMap.has(t.IdLocal)) {
            localCapteurMap.set(t.IdLocal, new Set());
          }
          localCapteurMap.get(t.IdLocal)?.add(t.IdCapteur);
        });

        locals.forEach((local) => {
          const capteurSet = localCapteurMap.get(local.Id) ?? new Set();
          local.SensorsCount = capteurSet.size;
        });
      },
      error: (err) =>
        console.error(
          'Erreur lors du chargement des transactions pour capteurs:',
          err
        ),
    });
  }
  
  private buildFilterParams(): FilterParam[] {
    const filters: FilterParam[] = [];
    const searchTerm = this.searchTerm.trim();

    if (searchTerm) {
      const isNumeric = !isNaN(Number(searchTerm)) && searchTerm !== '';

      if (isNumeric) {
        filters.push({
          Column: 'Floor',
          Value: Number(searchTerm),
          Op: 'equals',
          AndOr: 'AND',
        });
      } else {
        filters.push({
          Column: 'Name',
          Value: searchTerm,
          Op: 'contains',
          AndOr: 'AND',
        });
      }
    }

    return filters;
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadLocals();
  }  

  onSearchKeyup(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      this.filterLocals();
    } else if (event.key === 'Backspace' && this.searchTerm === '') {
      this.loadLocals();
    }
  }

  onSort(column: string): void {
    this.currentSort = [{
      Column: column,
      Sort: this.currentSort[0].Sort === 'asc' ? 'desc' : 'asc',
    }];
    this.loadLocals();
  }

  editLocal(id: string): void {
    const local = this.filteredLocals.find(l => l.Id === id);
    
    if (local) {
      this.selectedLocal = local;
      this.showEditForm = true;
      this.isImageModified = false;
  
      this.originalImageFileName = 'Image actuelle';
      this.imageLocalPreview = null;
  
      // Charger l'image enregistrée même si local.ImageLocal n'est pas rempli
      this.localService.downloadImage(local.Id).subscribe({
        next: (res) => {
          if (res?.image) {
            this.imageLocalPreview = 'data:image/jpeg;base64,' + res.image;
            this.editLocalForm.patchValue({ imageLocal: this.imageLocalPreview });
          }
        },
        error: (err) => {
          console.warn('Erreur lors du chargement de l’image du local :', err);
        }
      });
  
      this.editLocalForm.patchValue({
        id: local.Id,
        nom: local.Name,
        description: local.Description ?? '',
        floor: local.Floor,
        nombreCapteurs: local.SensorsCount,
        capacitePersonnes: local.Capacity,
        imageLocal: local.ImageLocal,
        idSite: local.IdSite,
        typeLocalId: local.TypeLocalId,
        latittude: local.Latitude,
        longtitude: local.Longtitude
      });
  
      setTimeout(() => {
        this.editFormSection?.nativeElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }, 100);
    }
  }
  

  deleteLocal(id: string): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirmer la suppression',
        message: 'Êtes-vous sûr de vouloir supprimer ce local ?',
        icon: 'delete',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.localService.delete(id).subscribe({
          next: () => {
            this.loadLocals();
            this.showSuccess(
              'Le local a été supprimé avec succès',
              'Information'
            );
          },
          error: (error) => {
            this.showError('Erreur lors de la suppression du local', 'Erreur');
          },
        });
      }
    });
  }

  viewDetails(id: string): void {
    this.router.navigate(['/local-details/', id]);
  }

  hideEditLocalForm(): void {
    this.showEditForm = false;
    this.editLocalForm.reset();
    this.imageLocalPreview = null;
    this.isSubmitting = false;
    this.selectedLocal = null;
    this.originalImageFileName = null;
    this.isImageModified = false;
  }

  onEditImagesSelected(event: any): void {
    if (event.target.files && event.target.files.length > 0) {
      const file = event.target.files[0];
      const reader = new FileReader();
      this.isImageModified = true;
      this.originalImageFileName = file.name;
  
      reader.onload = (e: any) => {
        this.imageLocalPreview = e.target.result;
        this.editLocalForm.patchValue({ imageLocal: e.target.result });
      };
      reader.readAsDataURL(file);
    }
  }

  submitEditForm(): void {
    if (this.editLocalForm.valid && this.selectedLocal) {
      this.isSubmitting = true;
      const formValues = this.editLocalForm.value;
  
      // Image : conserver l'image existante si non modifiée
      const imageData = this.isImageModified
        ? (formValues.imageLocal?.split(',')[1] ?? '')
        : this.selectedLocal.ImageLocal;
  
      const updatedLocal: Local = {
        ...this.selectedLocal,
        Name: formValues.nom ?? '',
        Floor: formValues.floor ?? 0,
        SensorsCount: formValues.nombreCapteurs ?? 0,
        Capacity: formValues.capacitePersonnes ?? 0,
        ImageLocal: imageData ?? (formValues.imageLocal?.split(',')[1] ?? ''),
        IdSite: formValues.idSite ?? this.selectedLocal.IdSite,
        TypeLocalId: formValues.typeLocalId ?? this.selectedLocal.TypeLocalId,
        Description: formValues.description ?? this.selectedLocal.Description,
        Latitude: this.selectedLocal.Latitude ?? 0,
        Longtitude: this.selectedLocal.Longtitude ?? 0
      };
  
      // D'abord update les données du local
      this.localService.update(updatedLocal).subscribe({
        next: () => {
          // Puis upload l'image uniquement si elle est modifiée
          if (this.isImageModified && imageData && updatedLocal.Id) {
            this.localService.uploadImage(updatedLocal.Id, imageData).subscribe({
              next: () => {
                this.loadLocals();
                this.showSuccess('Le local a été modifié avec succès', 'Information');
                this.hideEditLocalForm();
                this.isSubmitting = false;
              },
              error: (err) => {
                this.showError("Erreur lors de l'envoi de l'image", 'Erreur');
                this.isSubmitting = false;
              }
            });
          } else {
            this.loadLocals();
            this.showSuccess('Le local a été modifié avec succès', 'Information');
            this.hideEditLocalForm();
            this.isSubmitting = false;
          }
        },
        error: (error) => {
          this.isSubmitting = false;
          this.showError('Erreur lors de la modification du local', 'Erreur');
        }
      });
    }
  }
  

  handleAction(event: { action: string; row: any }): void {
    const { action, row } = event;
    if (action === 'edit') {
      this.editLocal(row.Id);
    } else if (action === 'delete') {
      this.deleteLocal(row.Id);
    } else if (action === 'view') {
      this.viewDetails(row.Id);
    }
  }

  private handleError(error: any): void {
    console.error('An error occurred:', error);
    
    if (error.error?.Message) {
      console.error('API Error:', error.error.Message);
    } else {
      console.error('Unknown error occurred');
    }

    this.isLoading = false;
    this.isSubmitting = false;
  }

  private showSuccess(message: string, title: string) {
    this.toast.success(message, title, 3000, false);
  }

  private showError(message: string, title: string) {
    this.toast.warning(message, title, 3000, false);
  }
}
