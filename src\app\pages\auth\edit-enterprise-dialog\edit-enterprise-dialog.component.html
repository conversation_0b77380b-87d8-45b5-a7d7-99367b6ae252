<!-- src/app/pages/auth/edit-enterprise-dialog/edit-enterprise-dialog.component.html -->
<h2 mat-dialog-title>Edit Enterprise User</h2>
<mat-dialog-content>
  <form [formGroup]="editForm">
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Username</mat-label>
      <input matInput formControlName="userName" required>
      <mat-error *ngIf="editForm.get('userName')?.hasError('required')">
        Username is required
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Email</mat-label>
      <input matInput type="email" formControlName="email" required>
      <mat-error *ngIf="editForm.get('email')?.hasError('required')">
        Email is required
      </mat-error>
      <mat-error *ngIf="editForm.get('email')?.hasError('email')">
        Invalid email format
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Full Name</mat-label>
      <input matInput formControlName="fullName" required>
      <mat-error *ngIf="editForm.get('fullName')?.hasError('required')">
        Full Name is required
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Enterprise Name</mat-label>
      <input matInput formControlName="enterpriseName" required>
      <mat-error *ngIf="editForm.get('enterpriseName')?.hasError('required')">
        Enterprise Name is required
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Number of Employees</mat-label>
      <input matInput type="number" formControlName="numberOfEmployees" required>
      <mat-error *ngIf="editForm.get('numberOfEmployees')?.hasError('required')">
        Number of Employees is required
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Contract Date</mat-label>
      <input matInput type="date" formControlName="contractDate" required>
      <mat-error *ngIf="editForm.get('contractDate')?.hasError('required')">
        Contract Date is required
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Category</mat-label>
      <input matInput formControlName="category" required>
      <mat-error *ngIf="editForm.get('category')?.hasError('required')">
        Category is required
      </mat-error>
    </mat-form-field>
  </form>
</mat-dialog-content>
<mat-dialog-actions>
  <button mat-button (click)="onCancel()">Cancel</button>
  <button mat-raised-button color="primary" (click)="onSave()" [disabled]="editForm.invalid">Save</button>
</mat-dialog-actions>