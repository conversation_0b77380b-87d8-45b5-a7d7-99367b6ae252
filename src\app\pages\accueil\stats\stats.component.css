.stats-section {
  margin-bottom: 2.5rem;
  margin-top: 1.5rem;
  animation: fadeIn 0.7s;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 2rem;
}

.stat-card {
  background: linear-gradient(120deg, #f8fafc 60%, #e8f5e9 100%);
  border-radius: 16px;
  box-shadow: 0 4px 18px rgba(76, 175, 80, 0.08);
  display: flex;
  align-items: center;
  padding: 1.5rem 1.2rem;
  gap: 1.2rem;
  transition: box-shadow 0.3s, transform 0.3s;
}

.stat-card:hover {
  box-shadow: 0 8px 32px rgba(76, 175, 80, 0.18);
  transform: translateY(-4px) scale(1.03);
}

.stat-icon {
  width: 54px;
  height: 54px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  background: #e0e7ef;
  color: var(--primary);
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.08);
}

.stat-icon.energy { background: #e3f2fd; color: #1976d2; }
.stat-icon.clients { background: #fffde7; color: #fbc02d; }
.stat-icon.ongoing { background: #f3e5f5; color: #8e24aa; }
.stat-icon.execution { background: #e8f5e9; color: #388e3c; }
.stat-icon.satisfaction { background: #e3fcec; color: #43a047; }
.stat-icon.impact { background: #e0f7fa; color: #00897b; }
.stat-icon.partners { background: #fff3e0; color: #f57c00; }

.stat-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.2rem;
}

.stat-label {
  font-size: 1rem;
  color: #718096;
  font-weight: 500;
  letter-spacing: 0.01em;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1.2rem;
  }
  .stat-card {
    padding: 1.2rem 0.8rem;
  }
}