/* Base Container - Aligned with site-management-container */
.logs-container {
  width: 95%; /* Use 95% width as in the example */
  margin: 30px auto; /* Centered with vertical spacing */
  padding: 25px; /* Consistent padding */
  background: linear-gradient(
    145deg,
    #ffffff,
    #f9f9f9
  ); /* Soft gradient background */
  border-radius: 12px; /* Rounded corners */
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08); /* Enhanced shadow */
}

/* Page Header - Refined */
.page-header {
  text-align: center;
  margin-bottom: 3rem;
  animation: slideIn 0.5s ease-out; /* Add animation */
}

.page-title {
  font-size: 2.8rem; /* Slightly larger title */
  font-weight: 700;
  margin-bottom: 0.8rem; /* More spacing */
  background: linear-gradient(135deg, #16a34a, #22c55e); /* Green gradient */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 0.5px; /* Add letter spacing */
}

.page-subtitle {
  color: var(--gray-600, #4b5563);
  font-size: 1.15rem; /* Slightly larger subtitle */
  font-weight: 400;
  max-width: 600px; /* Constrain width for readability */
  margin: 0 auto; /* Center subtitle */
  line-height: 1.6;
}

/* Filter Card - Modernized */
.filter-card {
  background: #fff;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2.5rem; /* Increased spacing */
  border: 1px solid var(--gray-200, #e5e7eb);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); /* Stronger shadow, like forms */
  animation: fadeIn 0.4s ease-in; /* Add fade-in animation */
}

/* Filter Grid - Adjusted for better alignment */
.filter-grid {
  display: grid;
  grid-template-columns: repeat(
    auto-fit,
    minmax(280px, 1fr)
  ); /* Adaptive columns */
  gap: 1.8rem; /* Increased gap */
  margin-bottom: 1.8rem;
  align-items: end; /* Align items to the bottom */
}

/* Form Group - Unified height */
.form-group {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: auto; /* Allow height to adjust naturally */
  min-height: 5rem; /* Minimum height to ensure alignment for all inputs */
  justify-content: flex-end; /* Push content to bottom for consistent alignment */
}

/* Form Label - Clearer and more distinct */
.form-label {
  display: flex;
  align-items: center; /* Vertically center icon and text */
  gap: 0.6rem; /* Slightly more space */
  font-size: 0.9rem; /* Slightly larger font */
  font-weight: 600;
  color: var(--gray-700, #374151);
  margin-bottom: 0.6rem; /* More spacing below label */
  line-height: 1.2;
}

.form-label mat-icon {
  font-size: 1.1rem; /* Slightly larger icon */
  line-height: 1;
  display: inline-flex;
  align-self: center; /* Center icon within its line */
  margin: 0;
  position: static;
  color: #22c55e; /* Green icon color */
}

/* Required Indicator */
.required {
  color: #ef4444;
  margin-left: 0.25rem; /* Small margin */
}

/* Form Control - Refined input styles */
.form-control {
  flex-grow: 1;
  width: 100%; /* Ensure full width */
  padding: 0.8rem 1.2rem; /* More padding */
  border: 1px solid var(--gray-300, #cbd5e0); /* Softer border */
  border-radius: 8px; /* More rounded */
  font-size: 0.95rem;
  background: #ffffff; /* White background */
  color: var(--gray-900, #111827);
  transition: all 0.2s ease; /* Smooth transition */
  box-sizing: border-box; /* Include padding in width/height */
  -webkit-appearance: none; /* Remove default select styling */
  -moz-appearance: none;
  appearance: none;
}

.form-control:focus {
  outline: none;
  border-color: #16a34a; /* Green focus border */
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2); /* Enhanced focus shadow */
}

.form-control:hover {
  border-color: var(--gray-400, #9ca3af); /* Darker hover border */
}

/* Search Wrapper - Aligned with form-group heights */
.search-wrapper {
  position: relative;
  width: 100%;
  height: auto; /* Allow height to adjust */
  min-height: 2.8rem; /* Minimum height for input, consistent with form-control padding */
}

.search-wrapper input {
  padding-right: 3rem;
  width: 100%;
  height: auto; /* Allow height to adjust */
  min-height: 2.8rem; /* Consistent min-height */
  box-sizing: border-box;
  border: 1px solid var(--gray-300, #cbd5e0);
  border-radius: 8px;
  font-size: 0.95rem;
  color: var(--gray-900, #111827);
  background: #ffffff;
  transition: all 0.2s ease;
  padding: 0.8rem 1.2rem; /* Consistent padding */
}

.search-wrapper input:focus {
  outline: none;
  border-color: #16a34a;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
}

.search-icon {
  position: absolute;
  right: 1.2rem; /* Adjusted right position */
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-500, #6b7280); /* Darker icon color */
  font-size: 1.3rem; /* Slightly larger icon */
  pointer-events: none;
}

/* Filter Actions - Clean and aligned */
.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1.2rem; /* More space between buttons */
  padding-top: 1.5rem; /* More padding above border */
  border-top: 1px solid var(--gray-200, #e5e7eb);
}

/* Buttons - Consistent with new button styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.6rem; /* More gap */
  padding: 0.85rem 1.7rem; /* More generous padding */
  border-radius: 8px;
  font-size: 0.9rem; /* Slightly larger font */
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease,
    background-color 0.2s ease; /* Add background transition */
  user-select: none;
}

.btn mat-icon {
  font-size: 1.1rem; /* Consistent icon size */
  vertical-align: middle;
  display: inline-flex;
  align-items: center;
  line-height: 1;
  margin: 0;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none; /* No transform when disabled */
  box-shadow: none; /* No shadow when disabled */
}

.btn-primary {
  background: linear-gradient(45deg, #16a34a, #22c55e); /* Green gradient */
  color: #fff;
  box-shadow: 0 3px 10px rgba(22, 163, 74, 0.3); /* Softer primary shadow */
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(22, 163, 74, 0.5); /* Enhanced hover shadow */
  background: linear-gradient(
    45deg,
    #22c55e,
    #16a34a
  ); /* Reverse gradient on hover */
}

.btn-secondary {
  background: var(--gray-100, #f3f4f6);
  color: var(--gray-700, #374151);
  border: 1px solid var(--gray-300, #cbd5e0); /* Softer border */
  box-shadow: inset 0 0 0 0 transparent;
  transition: background-color 0.2s ease, box-shadow 0.2s ease,
    border-color 0.2s ease;
}

.btn-secondary:hover:not(:disabled) {
  background: var(--gray-200, #e5e7eb);
  border-color: var(--gray-400, #9ca3af);
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.08); /* Subtle shadow on hover */
}

/* Loading State - Improved visibility */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3.5rem; /* More padding */
  background: #fff;
  border-radius: 12px;
  gap: 1.2rem; /* More gap */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* Subtle shadow */
  color: var(--gray-700, #374151); /* Darker text */
  font-size: 1.1rem;
}

.spinner {
  width: 2.2rem; /* Slightly larger spinner */
  height: 2.2rem;
  border: 4px solid #e5e7eb; /* Thicker border */
  border-top-color: #16a34a;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Logs Grid - Consistent animation */
.log-grid {
  display: grid;
  grid-template-columns: repeat(
    auto-fill,
    minmax(320px, 1fr)
  ); /* Adjusted min-width for cards */
  gap: 1.5rem;
  animation: fadeIn 0.5s ease-out;
}

/* Log Card - Enhanced with softer aesthetics */
.log-card {
  position: relative;
  padding: 1.6rem 1.8rem; /* Increased padding */
  border-radius: 16px;
  background: #ffffff;
  border: 1px solid var(--gray-200, #e2e8f0); /* Softer border */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06); /* Stronger initial shadow */
  cursor: pointer;
  transition: transform 0.25s ease, box-shadow 0.25s ease,
    border-color 0.25s ease;
  overflow: hidden;
  height: 190px; /* Slightly taller cards */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  user-select: none;
}

.log-card:hover {
  transform: translateY(-8px) scale(1.02); /* More pronounced hover effect */
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.18); /* Much stronger hover shadow */
  border-color: #16a34a; /* Green border on hover */
}

/* Left colored border for log level */
.log-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 8px; /* Wider border */
  border-radius: 16px 0 0 16px;
  background-color: var(--gray-300, #d1d5db); /* Default gray border */
  transition: background-color 0.3s ease;
  z-index: 10;
}

.log-card.info::before {
  background-color: #22c55e; /* Tailwind Green 500 */
}
.log-card.warning::before {
  background-color: #f59e0b; /* Tailwind Amber 500 */
}
.log-card.error::before {
  background-color: #ef4444; /* Tailwind Red 500 */
}
.log-card.debug::before {
  background-color: #10b981; /* Tailwind Emerald 500 */
}

/* Card Content */
.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.2rem; /* More space */
}

.log-level {
  padding: 0.45rem 1.1rem; /* Adjusted padding */
  border-radius: 9999px;
  font-size: 0.8rem; /* Slightly larger font */
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.07em;
  user-select: none;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08); /* More subtle shadow */
}

.log-level.info {
  background-color: #d4f8dc; /* Light green */
  color: #15803d; /* Dark green */
  box-shadow: 0 0 8px rgba(34, 197, 94, 0.3); /* Green shadow */
}
.log-level.warning {
  background-color: #fff4d6; /* Light amber */
  color: #b45309; /* Dark amber */
  box-shadow: 0 0 8px rgba(245, 158, 11, 0.3); /* Amber shadow */
}
.log-level.error {
  background-color: #fed7d7; /* Light red */
  color: #b91c1c; /* Dark red */
  box-shadow: 0 0 8px rgba(239, 68, 68, 0.3); /* Red shadow */
}
.log-level.debug {
  background-color: #d1fae5; /* Light emerald */
  color: #047857; /* Dark emerald */
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.3); /* Emerald shadow */
}

.log-timestamp {
  font-size: 0.8rem; /* Consistent font size */
  color: var(--gray-500, #6b7280); /* Darker gray */
  display: flex;
  align-items: center;
  gap: 0.4rem; /* More gap */
  user-select: none;
  white-space: nowrap;
}

.log-timestamp mat-icon {
  font-size: 1rem; /* Icon size */
  color: var(--gray-400, #9ca3af); /* Subtle icon color */
}

/* Log message with smooth truncation */
.log-message {
  font-size: 0.95rem; /* Slightly larger font */
  color: var(--gray-700, #374151); /* Darker text */
  line-height: 1.5;
  margin-bottom: 0.75rem;
  display: -webkit-box;
  -webkit-line-clamp: 4; /* Reduced lines for better summary */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  user-select: text;
}

.log-message.empty {
  font-style: italic;
  color: var(--gray-500, #6b7280);
}

/* Log Topic */
.log-topic {
  display: flex;
  align-items: center;
  gap: 0.6rem; /* More gap */
  font-size: 0.85rem; /* Smaller font for topic */
  color: var(--gray-600, #4b5563);
  background: var(--gray-100, #f3f4f6);
  padding: 0.4rem 0.8rem; /* Adjusted padding */
  border-radius: 8px;
  user-select: none;
  font-weight: 500;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.03);
}

.log-topic mat-icon {
  font-size: 1rem; /* Icon size */
  color: var(--gray-500, #6b7280); /* Subtle icon color */
}

.log-meta-indicator {
  position: absolute;
  bottom: 0.8rem; /* Position at the bottom */
  right: 0.8rem; /* Position at the right */
  color: var(--gray-400, #9ca3af);
  font-size: 1.1rem;
  transition: color 0.2s ease;
}

.log-card:hover .log-meta-indicator {
  color: #16a34a; /* Highlight on hover */
}

/* Empty State - Clear and engaging */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); /* Consistent shadow */
  user-select: none;
  animation: fadeIn 0.6s ease-out; /* Add animation */
}

.empty-icon mat-icon {
  font-size: 3.5rem; /* Larger icon */
  color: var(--gray-400, #9ca3af);
  margin-bottom: 1.5rem; /* More spacing */
}

.empty-title {
  font-size: 1.5rem; /* Larger title */
  font-weight: 600;
  color: var(--gray-800, #111827); /* Darker title */
  margin-bottom: 0.8rem;
}

.empty-description {
  color: var(--gray-600, #4b5563);
  font-weight: 400;
  font-size: 1.05rem;
}

/* Modal - Continued and further refined */
.log-modal-backdrop {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.6); /* Slightly darker backdrop */
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease-in-out;
}

.log-modal {
  background: #ffffff;
  width: 90%;
  max-width: 700px; /* Even wider modal for content */
  max-height: 90vh; /* Taller modal */
  overflow: hidden;
  border-radius: 16px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3); /* Stronger, more diffused shadow */
  animation: fadeInUp 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
}

/* Modal Header - Fixed and Stylish */
.log-modal-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background: linear-gradient(135deg, #16a34a, #22c55e);
  padding: 1.5rem 2rem; /* More padding */
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px 16px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15); /* Stronger header shadow */
}

.log-modal-header h2 {
  color: #ffffff;
  font-size: 1.8rem; /* Larger title font */
  font-weight: 700;
  text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.3); /* Stronger text shadow */
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 2rem; /* Larger close button */
  color: rgba(255, 255, 255, 0.95); /* Even lighter close button */
  cursor: pointer;
  transition: color 0.2s ease, transform 0.2s ease, background-color 0.2s ease;
  padding: 0.6rem; /* Make clickable area larger */
  border-radius: 50%;
}

.close-btn:hover {
  color: #ffffff;
  transform: rotate(90deg) scale(1.1); /* More pronounced rotation and slight scale */
  background-color: rgba(
    255,
    255,
    255,
    0.15
  ); /* More visible background on hover */
}

/* Modal Body - Scrollable Content */
.log-modal-body {
  padding: 1.8rem 2.2rem; /* More generous padding */
  overflow-y: auto;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem; /* More space between sections */
  color: var(--gray-800, #1f2937); /* Default text color */
}

.log-modal-body p {
  margin-bottom: 0.5rem;
  color: inherit; /* Inherit color from parent for consistency */
  font-size: 1rem; /* Standard font size */
  line-height: 1.6;
}

.log-modal-body p strong {
  color: var(--gray-900, #111827);
  font-weight: 700;
}

/* Section Styling */
.modal-section {
  margin-bottom: 1.8rem; /* More space between sections */
  border-bottom: 1px solid var(--gray-200, #e5e7eb); /* Consistent border color */
  padding-bottom: 1.5rem; /* More padding below section */
}

.modal-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.modal-section h3 {
  font-size: 1.3rem; /* Larger heading */
  font-weight: 700; /* Bolder heading */
  color: var(--gray-800, #1f2937); /* Darker heading */
  margin-bottom: 0.8rem; /* More spacing */
  user-select: none;
  display: flex;
  align-items: center;
  gap: 0.6rem;
}

.modal-section h3 mat-icon {
  font-size: 1.4rem;
  color: #22c55e; /* Green icon for headings */
}

/* Log Level Badge in Modal */
.modal-level-badge {
  display: inline-block;
  padding: 0.3rem 0.8rem; /* Adjusted padding */
  border-radius: 9999px;
  font-weight: 700;
  font-size: 0.85rem; /* Slightly smaller for a badge */
  text-transform: uppercase;
  user-select: none;
  letter-spacing: 0.05em;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08); /* Subtle shadow for badge */
}

.modal-level-badge.info {
  background-color: rgba(34, 197, 94, 0.15); /* Light green with transparency */
  color: #16a34a;
}

.modal-level-badge.warning {
  background-color: rgba(
    245,
    158,
    11,
    0.15
  ); /* Light amber with transparency */
  color: #d97706;
}

.modal-level-badge.error {
  background-color: rgba(239, 68, 68, 0.15); /* Light red with transparency */
  color: #dc2626;
}

.modal-level-badge.debug {
  background-color: rgba(
    16,
    185,
    129,
    0.15
  ); /* Light emerald with transparency */
  color: #0f766e;
}

/* Message formatting (Preformatted text for summary/message) */
.log-modal-body pre {
  background-color: var(--gray-100, #f3f4f6);
  padding: 1.2rem 1.5rem; /* More padding */
  border-radius: 10px; /* More rounded corners */
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier,
    monospace;
  font-size: 0.95rem; /* Slightly larger font */
  color: var(--gray-900, #111827);
  white-space: pre-wrap;
  word-break: break-word;
  overflow-x: auto;
  border: 1px solid var(--gray-200, #e5e7eb);
  box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.06); /* Stronger inner shadow */
  max-height: 300px; /* Increased max height */
}

/* Payload container */
.modal-payload {
  display: grid;
  grid-template-columns: 1fr 2.5fr; /* Key column narrower, value wider */
  gap: 0.8rem 1.5rem; /* More spacing */
  background-color: var(
    --gray-50,
    #f9fafb
  ); /* Lighter background for payload */
  border-radius: 10px; /* More rounded */
  padding: 1.2rem 1.5rem; /* More padding */
  max-height: 350px; /* Increased max height */
  overflow-y: auto;
  font-size: 0.95rem;
  color: var(--gray-800, #1f2937);
  border: 1px solid var(--gray-200, #e5e7eb); /* Subtle border */
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.03);
}

.payload-item {
  display: contents; /* Keeps grid structure */
}

.payload-key {
  font-weight: 600;
  color: var(--gray-900, #1f2937); /* Darker key color */
  user-select: text;
}

.payload-value {
  white-space: pre-wrap;
  user-select: text;
  word-break: break-word;
}

/* Specific styling for Topic (if it's a direct paragraph) */
.log-modal-body > div[ngIf="selectedLog.topic"] p {
  background-color: var(--blue-50, #eff6ff); /* Light blue for topic */
  color: var(--blue-800, #1e40af);
  padding: 0.8rem 1.2rem; /* More padding */
  border-radius: 10px; /* More rounded */
  border: 1px solid var(--blue-200, #bfdbfe);
  display: inline-block;
  font-weight: 500;
  margin-top: 1.5rem; /* More margin top */
  margin-bottom: 0.5rem; /* Small margin bottom */
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); /* Subtle shadow */
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px); /* Smaller translateY for general fade-in */
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(30px); /* Larger translateY for modal entry */
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 992px) {
  .logs-container {
    padding: 20px;
  }
  .filter-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.2rem;
  }
}

@media (max-width: 768px) {
  .logs-container {
    padding: 15px;
    width: 100%; /* Full width on smaller screens */
    margin: 20px auto;
  }

  .page-title {
    font-size: 2.2rem;
  }

  .page-subtitle {
    font-size: 1rem;
  }

  .filter-card {
    padding: 1.5rem;
  }

  .filter-grid {
    grid-template-columns: 1fr; /* Single column on mobile */
    gap: 1rem;
  }

  .filter-actions {
    flex-direction: column;
    gap: 0.8rem;
  }

  .btn {
    width: 100%; /* Full width buttons */
    justify-content: center;
    padding: 0.75rem 1rem;
  }

  .log-grid {
    grid-template-columns: 1fr; /* Single column for log cards */
    gap: 1.2rem;
  }

  .log-card {
    height: auto; /* Auto height for content */
    padding: 1.2rem 1.4rem;
  }

  .log-modal {
    width: 95%;
    max-width: 95%;
    padding: 1rem 1.2rem;
    max-height: 95vh;
  }

  .log-modal-header {
    padding: 1.2rem 1.5rem;
  }

  .log-modal-header h2 {
    font-size: 1.5rem;
  }

  .close-btn {
    font-size: 1.8rem;
  }

  .log-modal-body {
    padding: 1.2rem 1.5rem;
    gap: 1.2rem;
  }

  .modal-section h3 {
    font-size: 1.15rem;
  }

  .modal-payload {
    grid-template-columns: 1fr; /* Stack payload items on small screens */
    gap: 0.5rem;
  }

  .payload-item {
    display: block; /* Change display for stacked items */
  }

  .payload-key {
    margin-bottom: 0.2rem; /* Small margin below key */
  }

  .log-modal-body pre,
  .modal-payload {
    padding: 0.8rem 1rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.8rem;
  }
}
