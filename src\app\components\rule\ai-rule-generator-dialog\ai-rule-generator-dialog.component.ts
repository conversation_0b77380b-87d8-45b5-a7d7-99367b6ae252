import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCardModule } from '@angular/material/card';
import { MatExpansionModule } from '@angular/material/expansion';
import { RulesApiService } from '@app/core/services/administrative/rules.service';
import { RuleDto } from '@app/shared/models/RuleDto';
import { RuleWithAiRes } from '@app/shared/models/rule/RuleWithAiRes';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';
import { NgToastModule, NgToastService, TOAST_POSITIONS } from 'ng-angular-popup';
import { MatSelectModule } from '@angular/material/select';


@Component({
  selector: 'app-ai-rule-generator-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    MatIconModule,
    NgToastModule,
    MatProgressSpinnerModule,
    MatCardModule,
    MatExpansionModule,
    MatSelectModule,
    NgxUiLoaderModule
  ],
  templateUrl: './ai-rule-generator-dialog.component.html',
  styleUrls: ['./ai-rule-generator-dialog.component.css']
})
export class AiRuleGeneratorDialogComponent {
  TOAST_POSITIONS = TOAST_POSITIONS;

  ruleDescription: string = '';
  isGenerating: boolean = false;
  isSaving: boolean = false; // Added new state for saving operation
  // generationError: string = ''; // Removed as toasts will handle errors
  generatedRule: RuleDto | null = null;
  generatedSummary: string = '';
  showPreview: boolean = false;

  constructor(
    public dialogRef: MatDialogRef<AiRuleGeneratorDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private rulesService: RulesApiService,
    private ngxLoader: NgxUiLoaderService,
    private toast: NgToastService // Inject NgToastService
  ) {}

  onCancel(): void {
    this.dialogRef.close();
  }

  onGenerate(): void {
    if (!this.ruleDescription.trim()) {
      // this.generationError = 'Veuillez décrire la règle à générer.'; // Replaced with toast
      this.toast.info('Veuillez décrire la règle à générer.', 'Info', 3000, false);
      return;
    }

    this.ngxLoader.start();
    this.isGenerating = true;
    // this.generationError = ''; // No longer needed
    this.generatedRule = null;
    this.generatedSummary = '';
    this.showPreview = false;

    this.rulesService.createRuleWithAi(this.ruleDescription.trim()).subscribe({
      next: (response: RuleWithAiRes) => {
        try {
          this.generatedSummary = response.Summary;
          this.generatedRule = JSON.parse(response.RawData) as RuleDto;
          this.showPreview = true;
          this.toast.success('Règle générée par IA avec succès!', 'Succès', 3000, false); // Success toast
        } catch (error) {
          console.error('Error parsing generated rule:', error);
          // this.generationError = 'Erreur lors de la génération de la règle. Le format reçu est invalide.'; // Replaced with toast
          this.toast.warning('Erreur lors de la génération de la règle. Le format reçu est invalide.', 'Erreur', 4000, false);
        }
        this.isGenerating = false;
        this.ngxLoader.stop();
      },
      error: (error) => {
        console.error('Error generating rule:', error);
        let errorMessage = 'Erreur lors de la génération de la règle. Veuillez réessayer.';
        if (error?.error?.message) {
          errorMessage += ` Détails: ${error.error.message}`;
        }
        // this.generationError = errorMessage; // Replaced with toast
        this.toast.warning(errorMessage, 'Erreur', 5000, false);
        this.isGenerating = false;
        this.ngxLoader.stop();
      }
    });
  }

  onConfirm(): void {
    if (this.generatedRule) {
      this.ngxLoader.start();
      this.isSaving = true; // Set saving state to true
      const ruleToSave = {
        RawData: JSON.stringify(this.generatedRule),
        Summary: this.generatedSummary
      };

      this.rulesService.create(ruleToSave).subscribe({
        next: () => {
          this.dialogRef.close({
            action: 'confirm',
            rule: this.generatedRule,
            summary: this.generatedSummary
          });
          this.ngxLoader.stop();
          this.isSaving = false; // Reset saving state
          this.toast.success('Règle sauvegardée avec succès!', 'Succès', 3000, false); // Success toast for saving
        },
        error: (error) => {
          console.error('Error saving rule:', error);
          // this.generationError = 'Erreur lors de la sauvegarde de la règle. Veuillez réessayer.'; // Replaced with toast
          this.toast.warning('Erreur lors de la sauvegarde de la règle. Veuillez réessayer.', 'Erreur', 5000, false);
          this.ngxLoader.stop();
          this.isSaving = false; // Reset saving state
        }
      });
    }
  }

  onEdit(): void {
    this.showPreview = false;
  }

  formatRuleForDisplay(rule: RuleDto): string {
    try {
      return JSON.stringify(rule, null, 2);
    } catch (error) {
      console.error('Error formatting rule:', error);
      return 'Impossible d\'afficher la règle générée';
    }
  }
}
