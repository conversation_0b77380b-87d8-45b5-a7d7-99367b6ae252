import { Injectable } from '@angular/core';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class RoleService {
  constructor(private authService: AuthService) {}

  /**
   * Check if current user has any of the specified roles
   */
  hasAnyRole(roles: string[]): boolean {
    return this.authService.hasAnyRole(roles);
  }

  /**
   * Check if current user has specific role
   */
  hasRole(role: string): boolean {
    return this.authService.getRoles().includes(role);
  }

  /**
   * Check if current user is admin (ADMIN or SUPERADMIN)
   */
  isAdmin(): boolean {
    return this.hasRole('ADMINISTRATEUR') || this.hasRole('ADMIN');
  }

  /**
   * Check if current user is super admin
   */
  isSuperAdmin(): boolean {
    return this.hasRole('SUPERADMIN');
  }

  /**
   * Check if current user is client
   */
  isClient(): boolean {
    return this.hasRole('CLIENT');
  }

  /**
   * Check if current user is integrateur
   */
  isIntegrateur(): boolean {
    return this.hasRole('INTEGRATEUR');
  }

  /**
   * Get current user roles
   */
  getCurrentUserRoles(): string[] {
    return this.authService.getRoles();
  }

  /**
   * Check if user can access admin features
   */
  canAccessAdminFeatures(): boolean {
    return this.isAdmin();
  }

  /**
   * Check if user can manage organizations
   */
  canManageOrganizations(): boolean {
    return this.isAdmin();
  }

  /**
   * Check if user can manage users
   */
  canManageUsers(): boolean {
    return this.isAdmin();
  }

  /**
   * Check if user can view billing/invoices
   */
  canViewBilling(): boolean {
    return this.isAdmin();
  }

  /**
   * Check if user can manage subscriptions
   */
  canManageSubscriptions(): boolean {
    return this.isAdmin();
  }

  /**
   * Check if user can control devices
   */
  canControlDevices(): boolean {
    return this.isAdmin();
  }

  /**
   * Check if user can manage AI rules
   */
  canManageAIRules(): boolean {
    return this.isAdmin();
  }

  /**
   * Check if user can view organization details
   * (All users can view, but CLIENT users only see their own)
   */
  canViewOrganizationDetails(): boolean {
    return true; // All authenticated users can view
  }

  /**
   * Check if user can edit organization details
   */
  canEditOrganizationDetails(): boolean {
    return this.isAdmin();
  }

  /**
   * Check if user can view sensor data
   */
  canViewSensorData(): boolean {
    return true; // All authenticated users can view
  }

  /**
   * Check if user can generate reports
   */
  canGenerateReports(): boolean {
    return true; // All authenticated users can generate reports
  }

  /**
   * Get role display name
   */
  getRoleDisplayName(role: string): string {
    const roleMap: { [key: string]: string } = {
      'ADMINISTRATEUR': 'Administrateur',
      'SUPERADMIN': 'Super Administrateur',
      'CLIENT': 'Client',
      'INTEGRATEUR': 'Intégrateur'
    };
    return roleMap[role] || role;
  }

  /**
   * Get current user role display names
   */
  getCurrentUserRoleDisplayNames(): string[] {
    return this.getCurrentUserRoles().map(role => this.getRoleDisplayName(role));
  }
}
