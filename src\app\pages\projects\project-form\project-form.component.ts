// Frontend: src/app/pages/projects/project-form/project-form.component.ts
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { EnterpriseService, EnterpriseUser } from '../../../core/services/enterprise.service';
import { AuthService } from '../../../core/services/auth.service';

@Component({
  selector: 'app-project-form',
  templateUrl: './project-form.component.html',
  styleUrls: ['./project-form.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule
  ]
})
export class ProjectFormComponent implements OnInit {
  projectForm!: FormGroup;
  enterpriseUsers: EnterpriseUser[] = [];
  loading = false;
  projectId: string | null = null;
  isUpdateMode = false;
  isSuperAdmin = false;

  constructor(
    private fb: FormBuilder,
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private enterpriseService: EnterpriseService,
    private authService: AuthService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.projectForm = this.fb.group({
      name: ['', Validators.required],
      description: ['', Validators.required],
      label: ['', Validators.required],
      dueDate: ['', Validators.required],
      sensorCount: [0, [Validators.required, Validators.min(0)]],
      progress: [0, [Validators.required, Validators.min(0), Validators.max(100)]],
      members: [[]]
    });

    this.projectId = this.route.snapshot.paramMap.get('id');
    this.isUpdateMode = !!this.projectId;

    const roles = this.authService.getRoles();
    this.isSuperAdmin = roles.includes('SuperAdmin');

    if (this.isUpdateMode) {
      this.loadProject();
    }

    this.loadEnterpriseUsers();
  }

  loadEnterpriseUsers(): void {
    this.loading = true;
    this.enterpriseService.getEnterpriseUsers().subscribe({
      next: (data) => {
        const userId = this.authService.getUserId();
        // Filter enterprise users: SuperAdmin sees all, Admin sees only their own
        this.enterpriseUsers = this.isSuperAdmin
          ? data
          : data.filter(user => user.createdByAdminId === userId);
        this.loading = false;
      },
      error: () => {
        this.snackBar.open('Error loading enterprise users', 'Close', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  loadProject(): void {
    if (!this.projectId) return;
    this.http.get(`http://localhost:5256/api/projects/${this.projectId}`).subscribe({
      next: (project: any) => {
        this.projectForm.patchValue({
          ...project,
          dueDate: new Date(project.dueDate)
        });
      },
      error: () => {
        this.snackBar.open('Error loading project', 'Close', { duration: 3000 });
      }
    });
  }

  onSubmit(): void {
    if (this.projectForm.invalid) {
      this.snackBar.open('Please fill all required fields.', 'Close', { duration: 3000 });
      return;
    }

    this.loading = true;
    const request = this.isUpdateMode
      ? this.http.put(`http://localhost:5256/api/projects/${this.projectId}`, this.projectForm.value)
      : this.http.post('http://localhost:5256/api/projects', this.projectForm.value);

    request.subscribe({
      next: () => {
        const message = this.isUpdateMode ? 'Project updated successfully!' : 'Project created successfully!';
        this.snackBar.open(message, 'Close', { duration: 3000 });
        this.router.navigate(['/projects']);
        this.loading = false;
      },
      error: () => {
        this.snackBar.open('Error saving project', 'Close', { duration: 3000 });
        this.loading = false;
      }
    });
  }

  cancel(): void {
    this.router.navigate(['/projects']);
  }
}