import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '@app/environments/environment';

export interface ControllerServerViewData {
  Id: string;
  Name: string;
  MaxControllers?: number;
  MaxSensors?: number;
  GeographicZone?: string;
  CommercialCondition?: string;
  TriggerType?: string;
  ActionType?: string;
  EventType?: string;
  Status?: string;
  CreatedAt?: Date;
  LastUpdatedAt?: Date;
  // Client fields
  ClientId: string;
  ClientName: string;
  // Licence fields
  LicenceId: string;
  LicenceName: string;
  LicenceDescription?: string;
  // Subscription fields
  SubscriptionId: string;
  SubscriptionStartDate?: Date;
  SubscriptionEndDate?: Date;
  SubscriptionStatus: string;
}

export interface TransformedControllerServer {
  Id: string;
  Name: string;
  MaxControllers?: number;
  MaxSensors?: number;
  GeographicZone?: string;
  CommercialCondition?: string;
  TriggerType?: string;
  ActionType?: string;
  EventType?: string;
  Status?: string;
  CreatedAt?: Date;
  LastUpdatedAt?: Date;
  Licence: {
    Id: string;
    id: string; // for backward compatibility
    Name: string;
    Description?: string;
  };
  Subscription: {
    Id: string;
    DateDebut?: Date;
    DateFin?: Date;
    Status: string;
    IsActive?: boolean;
  };
  Client: {
    Id: string;
    Name: string;
  };
}

export interface LicenceData {
  Id: string;
  id: string; // for backward compatibility
  Name: string;
  Description?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ControllerServerViewService {
  private readonly baseUrl = environment.host;

  constructor(private http: HttpClient) {}

  /**
   * Get all controller servers for a specific client using the simple endpoint
   * @param clientId Client ID (string or GUID)
   * @returns Observable with all controller servers for the client
   */
  getControllerServersByClient(clientId: string): Observable<TransformedControllerServer[]> {
    console.log('Fetching controller servers for client:', clientId);
    
    if (!clientId || !clientId.trim()) {
      console.error('Client ID is required');
      return throwError(() => new Error('Client ID is required'));
    }

    const url = `${this.baseUrl}/api/ControllerServer/client/${clientId.trim()}`;
    
    return this.http.get<ControllerServerViewData[]>(url).pipe(
      map(response => {
        console.log('Raw response from backend:', response);
        
        if (!response || !Array.isArray(response)) {
          console.warn('Invalid response format, expected array');
          return [];
        }

        const transformedData = this.transformViewDataToControllerServers(response);
        console.log(`Successfully transformed ${transformedData.length} controller servers for client ${clientId}`);
        return transformedData;
      }),
      catchError(error => {
        console.error('Error fetching controller servers:', error);
        
        // Handle different error types
        if (error.status === 400) {
          return throwError(() => new Error('Invalid client ID provided'));
        } else if (error.status === 404) {
          return throwError(() => new Error('Client not found'));
        } else if (error.status === 500) {
          return throwError(() => new Error('Server error occurred'));
        }
        
        return throwError(() => error);
      })
    );
  }

  /**
   * Get available licences for a specific client
   * @param clientId Client ID
   * @returns Observable with available licences for the client
   */
  getAvailableLicences(clientId: string): Observable<LicenceData[]> {
    console.log('Extracting available licences for client:', clientId);
    
    return this.getControllerServersByClient(clientId).pipe(
      map(controllerServers => {
        // Extract unique licences from controller servers
        const licenceMap = new Map<string, LicenceData>();
        
        controllerServers.forEach(cs => {
          if (cs.Licence && !licenceMap.has(cs.Licence.Id)) {
            licenceMap.set(cs.Licence.Id, {
              Id: cs.Licence.Id,
              id: cs.Licence.Id,
              Name: cs.Licence.Name,
              Description: cs.Licence.Description,
            });
          }
        });
        
        const uniqueLicences = Array.from(licenceMap.values())
          .sort((a, b) => a.Name.localeCompare(b.Name));
        
        console.log(`Found ${uniqueLicences.length} unique licences for client ${clientId}`);
        return uniqueLicences;
      }),
      catchError(error => {
        console.error('Error extracting licences:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Search controller servers for a specific client (client-side filtering)
   * @param clientId Client ID
   * @param searchTerm Search term
   * @returns Observable with filtered controller servers
   */
  searchControllerServers(clientId: string, searchTerm: string): Observable<TransformedControllerServer[]> {
    console.log('Searching controller servers for client:', clientId, 'with term:', searchTerm);
    
    return this.getControllerServersByClient(clientId).pipe(
      map(controllerServers => {
        if (!searchTerm || !searchTerm.trim()) {
          return controllerServers;
        }
        
        const keyword = searchTerm.trim().toLowerCase();
        const filteredResults = controllerServers.filter(cs => 
          cs.Name?.toLowerCase().includes(keyword) ||
          cs.Licence?.Name?.toLowerCase().includes(keyword) ||
          cs.GeographicZone?.toLowerCase().includes(keyword) ||
          cs.CommercialCondition?.toLowerCase().includes(keyword)
        );
        
        console.log(`Search found ${filteredResults.length} results for term "${searchTerm}"`);
        return filteredResults;
      })
    );
  }

  /**
   * Transform raw view data to the expected component structure
   * @param viewData Raw data from the database view
   * @returns Transformed data compatible with existing component
   */
  private transformViewDataToControllerServers(viewData: ControllerServerViewData[]): TransformedControllerServer[] {
    return viewData.map(data => ({
      Id: data.Id,
      Name: data.Name,
      MaxControllers: data.MaxControllers,
      MaxSensors: data.MaxSensors,
      GeographicZone: data.GeographicZone,
      CommercialCondition: data.CommercialCondition,
      TriggerType: data.TriggerType,
      ActionType: data.ActionType,
      EventType: data.EventType,
      Status: data.Status,
      CreatedAt: data.CreatedAt,
      LastUpdatedAt: data.LastUpdatedAt,
      Licence: {
        Id: data.LicenceId,
        id: data.LicenceId, // for backward compatibility
        Name: data.LicenceName,
        Description: data.LicenceDescription,
      },
      Subscription: {
        Id: data.SubscriptionId,
        DateDebut: data.SubscriptionStartDate,
        DateFin: data.SubscriptionEndDate,
        Status: data.SubscriptionStatus,
        IsActive: this.isSubscriptionActive(data.SubscriptionStatus, data.SubscriptionStartDate, data.SubscriptionEndDate),
      },
      Client: {
        Id: data.ClientId,
        Name: data.ClientName,
      },
    }));
  }

  /**
   * Check if subscription is active based on status and dates
   * @param status Subscription status
   * @param startDate Subscription start date
   * @param endDate Subscription end date
   * @returns true if subscription is active
   */
  private isSubscriptionActive(status: string, startDate?: Date, endDate?: Date): boolean {
    if (status !== 'Active') {
      return false;
    }
    
    const now = new Date();
    const start = startDate ? new Date(startDate) : null;
    const end = endDate ? new Date(endDate) : null;
    
    const isAfterStart = !start || now >= start;
    const isBeforeEnd = !end || now <= end;
    
    return isAfterStart && isBeforeEnd;
  }

  /**
   * Validate if a string is a valid GUID format
   * @param value String to validate
   * @returns true if valid GUID format
   */
  isValidGuid(value: string): boolean {
    const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return guidRegex.test(value);
  }
}