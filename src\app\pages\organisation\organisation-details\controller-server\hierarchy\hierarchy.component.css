.hierarchy-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Header Styles */
.hierarchy-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 100%;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.header-title mat-icon {
  font-size: 2rem;
  width: 2rem;
  height: 2rem;
}

.header-controls {
  display: flex;
  gap: 12px;
}

.header-controls button {
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.header-controls button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.header-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Loading State */
.loading-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(45deg, #f9fafb 25%, transparent 25%), 
              linear-gradient(-45deg, #f9fafb 25%, transparent 25%), 
              linear-gradient(45deg, transparent 75%, #f9fafb 75%), 
              linear-gradient(-45deg, transparent 75%, #f9fafb 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

.loading-content {
  text-align: center;
  padding: 40px;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.loading-content h4 {
  margin: 0 0 10px 0;
  color: #374151;
  font-size: 1.25rem;
}

.loading-content p {
  margin: 0;
  color: #6b7280;
}

/* Error State */
.error-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.error-content {
  text-align: center;
  padding: 40px;
}

.error-icon {
  font-size: 4rem !important;
  width: 4rem !important;
  height: 4rem !important;
  color: #ef4444;
  margin-bottom: 20px;
}

.error-content h4 {
  margin: 0 0 10px 0;
  color: #dc2626;
  font-size: 1.25rem;
}

.error-content p {
  margin: 0 0 20px 0;
  color: #6b7280;
}

/* Statistics Panel */
.stats-panel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  padding: 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 15px;
  position: relative;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.stat-icon {
  font-size: 2rem !important;
  width: 2rem !important;
  height: 2rem !important;
  color: #667eea;
  margin-left: auto;
  opacity: 0.7;
}

/* Visualization Container */
.visualization-container {
  flex: 1;
  position: relative;
  background: linear-gradient(45deg, #f9fafb 25%, transparent 25%), 
              linear-gradient(-45deg, #f9fafb 25%, transparent 25%), 
              linear-gradient(45deg, transparent 75%, #f9fafb 75%), 
              linear-gradient(-45deg, transparent 75%, #f9fafb 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  overflow: hidden;
}

.hierarchy-svg {
  width: 100%;
  height: 100%;
  cursor: grab;
  display: block;
}

.hierarchy-svg:active {
  cursor: grabbing;
}

/* Tooltip */
.tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 12px;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 1000;
  max-width: 250px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.tooltip-content {
  line-height: 1.4;
}

.tooltip-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 6px;
  color: #ffffff;
}

.tooltip-type {
  font-size: 11px;
  color: #cbd5e1;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tooltip-details {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 8px;
}

.tooltip-details div {
  margin-bottom: 4px;
}

.tooltip-details div:last-child {
  margin-bottom: 0;
}

/* Empty State */
.empty-state {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.empty-content {
  text-align: center;
  padding: 40px;
}

.empty-icon {
  font-size: 4rem !important;
  width: 4rem !important;
  height: 4rem !important;
  color: #d1d5db;
  margin-bottom: 20px;
}

.empty-content h4 {
  margin: 0 0 10px 0;
  color: #374151;
  font-size: 1.25rem;
}

.empty-content .suggestion {
  font-size: 0.9rem;
  color: #4b5563;
  margin-top: 10px;
  font-style: italic;
}

/* Legend */
.legend {
  background: #f8fafc;
  padding: 15px 20px;
  border-top: 1px solid #e2e8f0;
}

.legend-title {
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  font-size: 14px;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #6b7280;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.legend-color.server-node {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.legend-color.controller-node.active {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.legend-color.controller-node.inactive {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
}

.legend-color.connection-line {
  background: #94a3b8;
  border-radius: 2px;
  height: 3px;
  width: 20px;
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card {
  animation: fadeIn 0.6s ease-out;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .header-controls {
    justify-content: center;
    flex-wrap: wrap;
  }

  .stats-panel {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    padding: 15px;
  }

  .stat-card {
    padding: 15px;
  }

  .stat-value {
    font-size: 1.5rem;
  }
}

@media (max-width: 768px) {
  .hierarchy-header {
    padding: 15px;
  }

  .header-title h3 {
    font-size: 1.25rem;
  }

  .header-controls {
    width: 100%;
  }

  .header-controls button {
    flex: 1;
    min-width: 0;
  }

  .stats-panel {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    padding: 10px;
  }

  .stat-card {
    padding: 12px;
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .stat-icon {
    margin: 0;
  }

  .legend-items {
    flex-direction: column;
    gap: 10px;
  }

  .tooltip {
    font-size: 11px;
    padding: 8px 12px;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .stats-panel {
    grid-template-columns: 1fr;
  }

  .header-controls button {
    font-size: 12px;
    padding: 8px 12px;
  }

  .loading-content,
  .error-content,
  .empty-content {
    padding: 20px;
  }
}

/* SVG Node Styles (applied via D3) */
:host ::ng-deep .node circle {
  transition: all 0.3s ease;
}

:host ::ng-deep .node:hover circle {
  filter: brightness(1.1) drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

:host ::ng-deep .link {
  transition: all 0.3s ease;
}

:host ::ng-deep .node-label {
  user-select: none;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}