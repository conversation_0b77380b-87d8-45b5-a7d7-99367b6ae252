// applied-rules-list.component.ts - Updated to handle edit mode properly
import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatChipsModule } from '@angular/material/chips';
import { RuleTransactionApiService } from '@app/core/services/administrative/ruletransaction.service';
import { TransactionApiService } from '@app/core/services/administrative/transaction.service';
import { ControllerApiService } from '@app/core/services/administrative/controller.service';
import { CapteurApiService } from '@app/core/services/administrative/capteur.service';
import { RuleTransaction } from '@app/core/models/ruleTransaction';
import { Transaction } from '@app/core/models/transaction';
import { Controller } from '@app/core/models/controller';
import { Capteur } from '@app/core/models/capteur';
import { RuleDto } from '@app/shared/models/RuleDto';
import { LocalRuleComponent } from '@app/pages/site-details/local-rule/local-rule.component';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';

interface AppliedRule {
  ruleId: string;
  ruleName: string;
  ruleData: RuleDto;
  totalApplications: number;
  controllers: ControllerApplication[];
  isExpanded: boolean;
}

interface ControllerApplication {
  controller: Controller;
  capteurs: Capteur[];
  transactions: Transaction[];
  ruleTransactions: RuleTransaction[];
}

@Component({
  selector: 'app-applied-rules-list',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatExpansionModule,
    MatChipsModule, 
  ],
  templateUrl: './applied-rules-list.component.html',
  styleUrls: ['./applied-rules-list.component.css']
})
export class AppliedRulesListComponent implements OnInit {
  @Input() localId!: string;

  appliedRules: AppliedRule[] = [];
  isLoading = true;

  constructor(
    private ruleTransactionService: RuleTransactionApiService,
    private transactionService: TransactionApiService,
    private controllerService: ControllerApiService,
    private capteurService: CapteurApiService, 
    private dialog: MatDialog,
  ) {}

  ngOnInit(): void {
    if (this.localId) {
      this.loadAppliedRules();
    }
  }

  private async loadAppliedRules(): Promise<void> {
    try {
      this.isLoading = true;
      
      // Get all rule transactions
      const ruleTransactions = await this.ruleTransactionService.getAll().toPromise() || [];
      
      // Use the optimized endpoint to get transactions for this local only
      const localTransactions = await this.transactionService.getByLocalId(this.localId).toPromise() || [];
      
      // Get controllers and capteurs
      const [controllers, capteurs] = await Promise.all([
        this.controllerService.getAll().toPromise(),
        this.capteurService.getAll().toPromise()
      ]);

      // Group rule transactions by rule ID
      const ruleGroups = new Map<string, RuleTransaction[]>();
      ruleTransactions.forEach(rt => {
        if (!ruleGroups.has(rt.IdRule)) {
          ruleGroups.set(rt.IdRule, []);
        }
        ruleGroups.get(rt.IdRule)!.push(rt);
      });

      // Process each rule group
      this.appliedRules = [];
      for (const [ruleId, ruleTransactionsList] of ruleGroups.entries()) {
        // Find relevant rule transactions for this local
        const relevantRuleTransactions = ruleTransactionsList.filter(rt =>
          localTransactions.some(lt => lt.Id === rt.IdTransaction)
        );

        if (relevantRuleTransactions.length === 0) continue;

        // Parse rule data from the first rule transaction
        let ruleData: RuleDto;
        try {
          ruleData = JSON.parse(relevantRuleTransactions[0].RawData);
        } catch (error) {
          console.error('Error parsing rule data:', error);
          continue;
        }

        // Group by controller
        const controllerMap = new Map<string, ControllerApplication>();
        
        for (const rt of relevantRuleTransactions) {
          const transaction = localTransactions.find(t => t.Id === rt.IdTransaction);
          if (!transaction || !transaction.IdController) continue;

          const controller = controllers?.find(c => c.Id === transaction.IdController);
          if (!controller) continue;

          if (!controllerMap.has(controller.Id!)) {
            controllerMap.set(controller.Id!, {
              controller,
              capteurs: [],
              transactions: [],
              ruleTransactions: []
            });
          }

          const controllerApp = controllerMap.get(controller.Id!)!;
          controllerApp.transactions.push(transaction);
          controllerApp.ruleTransactions.push(rt);

          // Add capteur if exists
          if (transaction.IdCapteur) {
            const capteur = capteurs?.find(c => c.Id === transaction.IdCapteur);
            if (capteur && !controllerApp.capteurs.find(c => c.Id === capteur.Id)) {
              controllerApp.capteurs.push(capteur);
            }
          }
        }

        // Create applied rule
        const appliedRule: AppliedRule = {
          ruleId,
          ruleName: ruleData.rule_name || 'Règle sans nom',
          ruleData,
          totalApplications: relevantRuleTransactions.length,
          controllers: Array.from(controllerMap.values()),
          isExpanded: false
        };

        this.appliedRules.push(appliedRule);
      }

      // Sort by rule name
      this.appliedRules.sort((a, b) => a.ruleName.localeCompare(b.ruleName));

    } catch (error) {
      console.error('Error loading applied rules:', error);
    } finally {
      this.isLoading = false;
    }
  }

  openRuleFormDialog(ruleTransaction?: RuleTransaction): void {
    this.dialog.open(LocalRuleComponent, {
      width: '95vw',
      maxWidth: '800px',
      panelClass: 'custom-local-rule-dialog',
      disableClose: true,
      data: {
        idLocal: this.localId,
        editMode: !!ruleTransaction,
        ruleTransaction
      }
    }).afterClosed().subscribe(result => {
      if (result?.action === 'saved' || result?.action === 'updated') {
        this.refreshAppliedRules();
      }
    });
  }

  private refreshAppliedRules(): void {
    this.loadAppliedRules();
  }

  // Event handlers for the applied rules component
  onAddNewRuleFromList(): void {
    this.openRuleFormDialog();
  }

  // UI Event Handlers
  onRuleExpand(rule: AppliedRule, expanded: boolean): void {
    rule.isExpanded = expanded;
  }

  onViewRuleDetails(rule: AppliedRule): void {
    console.log('View rule details:', rule);
    // Could implement a read-only dialog showing rule details
  }

  onEditRule(rule: AppliedRule): void {
    const firstRuleTransaction = rule.controllers
      .flatMap(ca => ca.ruleTransactions)[0];

    if (!firstRuleTransaction) {
      console.warn('No RuleTransaction found for editing.');
      return;
    }

    this.dialog.open(LocalRuleComponent, {
      width: '95vw',
      maxWidth: '800px',
      panelClass: 'custom-local-rule-dialog',
      disableClose: true,
      data: {
        idLocal: this.localId,
        editMode: true,
        ruleTransaction: firstRuleTransaction
      }
    }).afterClosed().subscribe(result => {
      if (result?.action === 'updated' || result?.action === 'saved') {
        console.log('Rule updated successfully, refreshing list...');
        this.refreshAppliedRules();
      }
    });
  }

  onToggleRule(rule: AppliedRule): void {
    // Get the first rule transaction to toggle
    const firstRuleTransaction = rule.controllers
      .flatMap(ca => ca.ruleTransactions)[0];

    if (!firstRuleTransaction) {
      console.warn('No RuleTransaction found for toggling.');
      return;
    }

    try {
      // Parse and update the rule data
      const ruleData: RuleDto = JSON.parse(firstRuleTransaction.RawData);
      ruleData.enabled = !ruleData.enabled;
      
      // Update all rule transactions for this rule
      const allRuleTransactions = rule.controllers
        .flatMap(ca => ca.ruleTransactions);

      const updatePromises = allRuleTransactions.map(rt => {
        const updatedRuleTransaction = {
          ...rt,
          RawData: JSON.stringify(ruleData)
        };
        return this.ruleTransactionService.update(updatedRuleTransaction).toPromise();
      });

      Promise.all(updatePromises).then(() => {
        console.log('Rule status toggled successfully');
        this.refreshAppliedRules();
      }).catch(error => {
        console.error('Error toggling rule status:', error);
        this.showErrorDialog('Erreur lors du changement du statut de la règle.');
      });

    } catch (error) {
      console.error('Error parsing rule data for toggle:', error);
      this.showErrorDialog('Erreur lors de l\'analyse des données de la règle.');
    }
  }

  onDeleteRule(rule: AppliedRule): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Supprimer la règle',
        message: `Êtes-vous sûr de vouloir supprimer la règle "${rule.ruleName}" ? Cette action est irréversible.`,
        icon: 'warning',
        confirmText: 'Supprimer',
        cancelText: 'Annuler'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.performDeleteRule(rule);
      }
    });
  }

  private performDeleteRule(rule: AppliedRule): void {
    // Get all rule transactions for this rule
    const allRuleTransactions = rule.controllers
      .flatMap(ca => ca.ruleTransactions);

    if (allRuleTransactions.length === 0) {
      console.warn('No RuleTransactions found for deletion.');
      return;
    }

    // Delete all rule transactions
    const deletePromises = allRuleTransactions.map(rt => 
      this.ruleTransactionService.delete(rt.Id!).toPromise()
    );

    Promise.all(deletePromises).then(() => {
      console.log('Rule deleted successfully');
      this.refreshAppliedRules();
    }).catch(error => {
      console.error('Error deleting rule:', error);
      this.showErrorDialog('Erreur lors de la suppression de la règle.');
    });
  }

  // Helper Methods
  trackByRuleId(index: number, rule: AppliedRule): string {
    return rule.ruleId;
  }

  trackByControllerId(index: number, controllerApp: ControllerApplication): string {
    return controllerApp.controller.Id || index.toString();
  }

  trackByCapteurId(index: number, capteur: Capteur): string {
    return capteur.Id || index.toString();
  }

  getRuleIcon(ruleData: RuleDto): string {
    if (!ruleData.enabled) return 'pause_circle_outline';
    if (ruleData.schedule_config?.enabled) return 'schedule';
    return 'rule';
  }

  getTotalCapteurs(rule: AppliedRule): number {
    return rule.controllers.reduce((total, controller) => total + controller.capteurs.length, 0);
  }

  getConditionsCount(ruleData: RuleDto): number {
    return ruleData.conditions.groups.reduce((total, group) => total + group.conditions.length, 0);
  }

  isSensor(capteur: Capteur): boolean {
    return capteur.TypeCapteur?.DeviceType?.toLowerCase() === 'sensor';
  }

  getCapteurIcon(capteur: Capteur): string {
    return this.isSensor(capteur) ? 'sensors' : 'settings_remote';
  }

  getSensorCount(capteurs: Capteur[]): number {
    return capteurs.filter(c => this.isSensor(c)).length;
  }

  getActuatorCount(capteurs: Capteur[]): number {
    return capteurs.filter(c => !this.isSensor(c)).length;
  }

  private showErrorDialog(message: string): void {
    this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Erreur',
        message: message,
        icon: 'error',
        confirmText: 'OK'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    });
  }
}