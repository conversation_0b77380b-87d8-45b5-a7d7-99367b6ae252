import { CommonModule } from '@angular/common';
import { Component, HostListener, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { lastValueFrom } from 'rxjs';

import { Client } from '@app/core/models/client';
import { Licence } from '@app/core/models/licence';
import { Subscription } from '@app/core/models/subscription'; // Assuming 'Subscription' has an 'Id' property

import { ClientApiService } from '@app/core/services/administrative/client.service';
import { LicenceApiService } from '@app/core/services/administrative/licence.service';
import { SubscriptionApiService } from '@app/core/services/administrative/subscription.service';
import { DetailComponent } from '../detail/detail.component';
import { ActivatedRoute } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import {
  NgToastComponent,
  NgToastService,
  TOAST_POSITIONS,
} from 'ng-angular-popup';

@Component({
  selector: 'app-list',
  standalone: true,
  imports: [CommonModule, FormsModule, DetailComponent, MatIconModule, NgToastComponent],
  templateUrl: './list.component.html',
  styleUrls: ['./list.component.css'],
})
export class ListComponent implements OnInit {
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    // Close dropdown if click is outside, but not if it's within any modal
    if (
      !target.closest('.dropdown-container') &&
      !target.closest('.modal-container')
    ) {
      if (this.showDropdown) {
        console.log('Click outside dropdown - closing');
      }
      this.showDropdown = false;
    }
  }

  subscriptions: Subscription[] = [];
  clients: Client[] = [];
  licences: Licence[] = [];
  isLoading: boolean = false;
  clientId: string | null = null;

    TOAST_POSITIONS = TOAST_POSITIONS;

  renewType: 'monthly' | 'yearly' = 'monthly';

  selectedSubscription: Subscription | null = null;
  // Use subscriptionId for isRenewing to track individual subscription's loading state
  isRenewing: { [subscriptionId: string]: boolean } = {};

  showDropdown: boolean = false;
  showDetailModal: boolean = false;
  showFormModal: boolean = false;
  showRenewModal: boolean = false;

  constructor(
    private subscriptionApiService: SubscriptionApiService,
    private clientApiService: ClientApiService,
    private licenceApiService: LicenceApiService,
    private route: ActivatedRoute,
    private toast: NgToastService,
  ) {}

  ngOnInit(): void {
    const hash = window.location.hash;
    const match = hash.match(/organisation-details\/([a-f0-9\-]+)/i);
    if (match && match[1]) {
      this.clientId = match[1];
    }
    console.log('Extracted clientId:', this.clientId);
    this.loadAllData();
  }

  async loadAllData(): Promise<void> {
    this.isLoading = true;
    try {
      const [clients, licences, subscriptions] = await Promise.all([
        lastValueFrom(this.clientApiService.getAll()),
        lastValueFrom(this.licenceApiService.getAll()),
        lastValueFrom(this.subscriptionApiService.getAll()),
      ]);
      this.clients = clients ?? [];
      this.licences = licences ?? [];
      this.subscriptions =
        subscriptions?.filter((sub) => sub.ClientId === this.clientId) ?? [];

      console.log('Extracted clientId:', this.clientId);
      console.log('All subscriptions (filtered):', this.subscriptions);

      this.selectedSubscription =
        this.subscriptions.length > 0 ? this.subscriptions[0] : null;
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      this.isLoading = false;
    }
  }

  selectSubscription(sub: Subscription) {
    // Toggle: hide if same subscription is clicked again
    this.selectedSubscription = this.selectedSubscription === sub ? null : sub;
  }

  getClientName(clientId?: string): string {
    const client = this.clients.find((c) => c.Id === clientId);
    return client ? client.Name : 'Unknown Client';
  }

  getLicenceName(licenceId?: string): string {
    const licence = this.licences.find((l) => l.Id === licenceId);
    return licence ? licence.Name : 'Unknown Licence';
  }

  onFormSubmit(updatedSubscription: Subscription): void {
    console.log('Form submitted:', updatedSubscription);
    this.selectedSubscription = { ...updatedSubscription };

    const index = this.subscriptions.findIndex(
      (sub) =>
        sub.ClientId === updatedSubscription.ClientId &&
        sub.LicenceId === updatedSubscription.LicenceId
    );
    if (index !== -1) {
      this.subscriptions[index] = { ...updatedSubscription };
    } else {
      this.subscriptions.push(updatedSubscription);
    }
  }

  openRenewModal(sub: Subscription): void {
    this.selectedSubscription = sub;
    this.renewType =
      sub.PaymentFrequency?.toLowerCase() === 'monthly' ? 'monthly' : 'yearly';
    this.showRenewModal = true;
    document.body.classList.add('modal-open');
    event?.stopPropagation();
  }

  closeRenewModal(): void {
    this.showRenewModal = false;
    this.selectedSubscription = null;
    document.body.classList.remove('modal-open');
  }

  async confirmAndRenewSubscription(): Promise<void> {
    if (!this.selectedSubscription || !this.selectedSubscription.Id) {
      alert('No subscription selected for renewal or ID is missing.');
      return;
    }

    const subToRenew = this.selectedSubscription;
    this.isRenewing[subToRenew.Id] = true;
    console.log(this.renewType," renewType");
    

    try {
      const response = await lastValueFrom(
        this.subscriptionApiService.renewSubscription(
          subToRenew.Id.toUpperCase(),
          this.renewType
        )
      );

      const index = this.subscriptions.findIndex(
        (s) => s.Id === response.subscription.Id
      );
      if (index > -1) {
        this.subscriptions[index] = response.subscription;
      } else {
        this.subscriptions.push(response.subscription);
      }

      const newEndDateDisplay = response.subscription.DateFin
        ? new Date(response.subscription.DateFin).toLocaleDateString()
        : 'Date non disponible';

      this.showSuccess(
        `Abonnement renouvelé avec succès pour ${this.getClientName(
          subToRenew.ClientId
        )} - ${this.getLicenceName(
          subToRenew.LicenceId
        )}! Nouvelle date de fin: ${newEndDateDisplay}. Montant de la facture: ${
          response.facture.Total
        }€`
      , "Succés");
      this.closeRenewModal();
    } catch (err: any) {
      console.error("Erreur lors du renouvellement de l'abonnement:", err);
      this.showError(
        `Échec du renouvellement de l'abonnement. Erreur: ${
          err.message || 'Erreur inconnue'
        }`,
        "Erreur"
      );
    } finally {
      this.isRenewing[subToRenew.Id] = false;
    }
  }

  toggleDropdown(sub: Subscription): void {
    console.log('Toggle dropdown called for', sub);
    if (sub) {
      // If clicking the same subscription, toggle dropdown visibility
      if (this.selectedSubscription === sub) {
        this.showDropdown = !this.showDropdown;
      } else {
        // Different subscription clicked: set selected and open dropdown
        this.selectedSubscription = sub;
        this.showDropdown = true;
      }
      console.log('showDropdown:', this.showDropdown);
    }
  }

  // Modal methods (Existing)
  openDetailModal(): void {
    if (this.selectedSubscription) {
      this.showDetailModal = true;
      this.showDropdown = false;
      document.body.classList.add('modal-open');
    }
  }

  openFormModal(): void {
    if (this.selectedSubscription) {
      this.showFormModal = true;
      this.showDropdown = false;
      document.body.classList.add('modal-open');
    }
  }

  closeDetailModal(): void {
    this.showDetailModal = false;
    document.body.classList.remove('modal-open');
  }

  closeFormModal(): void {
    this.showFormModal = false;
    document.body.classList.remove('modal-open');
  }
  
  private showSuccess(message: string, title: string) {
    this.toast.success(message, title, 3000, false);
  }

  private showError(message: string, title: string) {
    this.toast.warning(message, title, 3000, false);
  }
}
