import { ControllerServerController } from "./controllerServerController";
import { ControllerServerRule } from "./controllerServerRule";
import { Licence } from "./licence";
import { AuditModel } from "./models-audit/audit-model";
import { Subscription } from "./subscription";

export interface ControllerServeur extends AuditModel {
    Name: string;
    MaxControllers: number;
    MaxSensors: number;
    GeographicZone: string;
    CommercialCondition: string;
    TriggerType: string;
    ActionType: string;
    EventType: string;
    Status: string;
    SubscriptionId: string;
    Subscription: Subscription;
    ControllerServerControllers: ControllerServerController[];
    ControllerServerRules: ControllerServerRule[];
}


