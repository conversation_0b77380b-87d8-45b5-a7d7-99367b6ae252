import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { Controller } from '@app/core/models/controller';
import { ClientLicenceControllerView } from '@app/shared/models/clientLicenceControllerView';

@Component({
  selector: 'app-c-p-details',
  imports: [CommonModule],
  templateUrl: './c-p-details.component.html',
  styleUrl: './c-p-details.component.css'
})
export class CPDetailsComponent {
  @Input() controller: Controller | null = null;
}
