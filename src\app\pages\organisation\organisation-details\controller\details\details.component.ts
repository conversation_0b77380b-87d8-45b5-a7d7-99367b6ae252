import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { ClientLicenceControllerView } from '@app/shared/models/clientLicenceControllerView';

@Component({
  selector: 'app-details',
  imports: [CommonModule],
  templateUrl: './details.component.html',
  styleUrl: './details.component.css'
})
export class DetailsComponent {
  @Input() controller: ClientLicenceControllerView | null = null;


}
