<div class="container">
  <ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>

  <div class="page-header">
    <div>
      <h1 class="page-title">Licences disponibles</h1>
      <p class="page-subtitle">G<PERSON>rez vos licences et leurs options</p>
    </div>
    <button
      class="add-licence-btn"
      (click)="openAddModal()"
      [disabled]="isLoading || isSubmitting"
    >
      <span *ngIf="isLoading || isSubmitting" class="loading-spinner"></span>
      <mat-icon *ngIf="!isLoading && !isSubmitting">description</mat-icon>
      {{ isLoading ? "Chargement..." : "Ajouter une licence" }}
    </button>
  </div>

  <div *ngIf="isLoading" class="loading-container">
    <div class="loading-content">
      <div class="loading-spinner-large"></div>
      <p class="loading-text">Chargement des licences...</p>
    </div>
  </div>

  <div
    class="licenses-grid"
    *ngIf="!isLoading && licences && licences.length > 0"
  >
    <div
      class="license-card"
      *ngFor="let licence of licences; trackBy: trackByLicence"
      [class.card-refreshing]="isRefreshing[licence.Id]"
    >
      <div class="license-header">
        <div class="license-info">
          <h3 class="license-name">{{ licence.Name }}</h3>
          <p class="license-description" *ngIf="licence.Description">
            {{ licence.Description }}
          </p>
        </div>
        <div class="license-actions">
          <button
            class="action-btn view"
            (click)="viewLicence(licence)"
            title="Voir les détails"
            [disabled]="isRefreshing[licence.Id]"
          >
            <mat-icon>visibility</mat-icon>
          </button>
          <button
            class="action-btn edit"
            (click)="editLicence(licence)"
            title="Modifier"
            [disabled]="isRefreshing[licence.Id]"
          >
            <mat-icon>edit</mat-icon>
          </button>
          <button
            class="action-btn refresh"
            (click)="refreshLicence(licence)"
            title="Actualiser"
            [disabled]="isRefreshing[licence.Id]"
          >
            <span
              *ngIf="isRefreshing[licence.Id]"
              class="loading-spinner"
            ></span>
            <mat-icon *ngIf="!isRefreshing[licence.Id]">refresh</mat-icon>
          </button>
          <button
            class="action-btn delete"
            (click)="deleteLicence(licence)"
            title="Supprimer"
            [disabled]="isRefreshing[licence.Id]"
          >
            <mat-icon>delete</mat-icon>
          </button>
        </div>
      </div>

      <div class="license-content">
        <div
          class="license-options"
          *ngIf="licence.Options && licence.Options.length > 0"
        >
          <div class="options-header">
            <span class="options-label">Options disponibles</span>
            <span class="options-count">{{ licence.Options.length }}</span>
          </div>
          <div class="options-list">
            <div
              class="option-tag"
              *ngFor="let option of licence.Options; trackBy: trackByOption"
            >
              <span class="option-name">
                {{ option.Name }} — {{ option.Price }}€
                <span
                  *ngIf="
                    option.Type === 'max-controllers' ||
                    option.Type === 'max-controllers-server'
                  "
                >
                  — Max: {{ option.Value }}
                </span>
              </span>
            </div>
          </div>
        </div>
        <div
          class="no-options"
          *ngIf="!licence.Options || licence.Options.length === 0"
        >
          <mat-icon class="no-options-icon">list_alt</mat-icon>
          Aucune option configurée
        </div>
      </div>

      <div class="card-loading-overlay" *ngIf="isRefreshing[licence.Id]">
        <div class="loading-spinner"></div>
      </div>
    </div>
  </div>

  <div
    class="empty-state"
    *ngIf="!isLoading && (!licences || licences.length === 0)"
  >
    <mat-icon class="empty-state-icon">description</mat-icon>
    <h3 class="empty-state-title">Aucune licence trouvée</h3>
    <p class="empty-state-text">
      Commencez par ajouter votre première licence pour gérer vos options et
      tarifs.
    </p>
    <button
      class="btn btn-primary empty-state-btn"
      (click)="openAddModal()"
      [disabled]="isLoading || isSubmitting"
    >
      <span *ngIf="isSubmitting" class="loading-spinner"></span>
      <mat-icon *ngIf="!isSubmitting">add</mat-icon>
      {{ isSubmitting ? "Création..." : "Créer ma première licence" }}
    </button>
  </div>

  <div *ngIf="isModalOpen" class="modal-overlay" (click)="closeModal()">
    <div class="modal-container" (click)="$event.stopPropagation()">
      <div class="modal-header">
        <h2 class="modal-title">{{ getModalTitle() }}</h2>
        <div *ngIf="isSubmitting || isUpdating" class="modal-loading">
          <span class="loading-spinner"></span>
          <span class="loading-text">
            {{
              modalMode === "add"
                ? "Création en cours..."
                : "Mise à jour en cours..."
            }}
          </span>
        </div>
      </div>

      <form (ngSubmit)="submitForm()">
        <div
          class="modal-body"
          [class.modal-body-loading]="isSubmitting || isUpdating"
        >
          <div class="form-group">
            <label class="form-label">Nom de la licence</label>
            <input
              type="text"
              class="form-input"
              [(ngModel)]="formData.Name"
              name="Name"
              [readonly]="isReadOnly() || isSubmitting || isUpdating"
              [class.readonly]="isReadOnly() || isSubmitting || isUpdating"
              placeholder="Ex: Licence Standard"
              required
            />
          </div>

          <div class="form-group">
            <label class="form-label">Description</label>
            <textarea
              class="form-textarea"
              [(ngModel)]="formData.Description"
              name="Description"
              [readonly]="isReadOnly() || isSubmitting || isUpdating"
              [class.readonly]="isReadOnly() || isSubmitting || isUpdating"
              placeholder="Décrivez les fonctionnalités de cette licence..."
              rows="3"
            ></textarea>
          </div>

          <div class="form-group">
            <div class="options-section-header">
              <label class="form-label">Options de la licence</label>
              <span
                class="options-counter"
                *ngIf="formData.Options && formData.Options.length > 0"
              >
                {{ formData.Options.length }} option(s)
              </span>
            </div>

            <div class="options-container">
              <div
                *ngFor="
                  let option of formData.Options;
                  let i = index;
                  trackBy: trackByOption
                "
                class="option-item"
                [class.option-disabled]="isSubmitting || isUpdating"
              >
                <div class="option-header">
                  <h4>Option {{ i + 1 }}</h4>
                  <button
                    *ngIf="!isReadOnly()"
                    type="button"
                    class="btn btn-remove"
                    (click)="removeOption(i)"
                    [disabled]="isSubmitting || isUpdating"
                  >
                    <mat-icon>close</mat-icon>
                  </button>
                </div>

                <div class="option-fields">
                  <div class="form-group">
                    <label class="form-label">Nom de l'option</label>
                    <input
                      type="text"
                      class="form-input"
                      [(ngModel)]="option.Name"
                      [name]="'option_Name_' + i"
                      [readonly]="isReadOnly() || isSubmitting || isUpdating"
                      [class.readonly]="
                        isReadOnly() || isSubmitting || isUpdating
                      "
                      placeholder="Ex: Option Premium"
                    />
                  </div>

                  <div class="form-group">
                    <label class="form-label">Prix (€)</label>
                    <input
                      type="number"
                      class="form-input"
                      [(ngModel)]="option.Price"
                      [name]="'option_Price_' + i"
                      [readonly]="isReadOnly() || isSubmitting || isUpdating"
                      [class.readonly]="
                        isReadOnly() || isSubmitting || isUpdating
                      "
                      placeholder="0.00"
                      step="0.01"
                      min="0"
                    />
                  </div>

                  <div class="form-group">
                    <label class="form-label">Type de l'option</label>
                    <select
                      class="form-input"
                      [(ngModel)]="option.Type"
                      [name]="'option_Type_' + i"
                      [disabled]="isReadOnly() || isSubmitting || isUpdating"
                    >
                      <option [ngValue]="undefined" disabled selected>
                        Choisir un type
                      </option>
                      <option value="standard">Standard</option>
                      <option value="max-controllers-server">
                        Max Contrôleurs Serveurs
                      </option>
                      <option value="max-controllers">Max Contrôleurs</option>
                    </select>
                  </div>

                  <div
                    class="form-group"
                    *ngIf="option.Type === 'max-controllers-server'"
                  >
                    <label class="form-label"
                      >Valeur Max de Contrôleurs Serveur
                    </label>
                    <input
                      type="number"
                      class="form-input"
                      [(ngModel)]="option.Value"
                      [name]="'option_Value_' + i"
                      placeholder="Ex: 5"
                      min="0"
                      [readonly]="isReadOnly() || isSubmitting || isUpdating"
                      [class.readonly]="
                        isReadOnly() || isSubmitting || isUpdating
                      "
                    />
                  </div>

                  <div
                    class="form-group"
                    *ngIf="option.Type === 'max-controllers'"
                  >
                    <label class="form-label">Valeur Max Contrôleurs</label>
                    <input
                      type="number"
                      class="form-input"
                      [(ngModel)]="option.Value"
                      [name]="'option_Value_' + i"
                      placeholder="Ex: 5"
                      min="0"
                      [readonly]="isReadOnly() || isSubmitting || isUpdating"
                      [class.readonly]="
                        isReadOnly() || isSubmitting || isUpdating
                      "
                    />
                  </div>
                </div>
              </div>

              <div class="add-option-section" *ngIf="!isReadOnly()">
                <button
                  type="button"
                  class="btn btn-secondary add-option-btn"
                  (click)="addOption()"
                  [disabled]="isSubmitting || isUpdating"
                >
                  <mat-icon>add</mat-icon> Ajouter une option
                </button>
              </div>

              <div
                class="no-options-placeholder"
                *ngIf="!formData.Options || formData.Options.length === 0"
              >
                <mat-icon class="placeholder-icon">list_alt</mat-icon>
                <span class="placeholder-text">Aucune option configurée</span>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-actions">
          <button
            type="button"
            class="btn btn-cancel"
            (click)="closeModal()"
            [disabled]="isSubmitting || isUpdating"
          >
            {{ modalMode === "view" ? "Fermer" : "Annuler" }}
          </button>
          <button
            *ngIf="modalMode !== 'view'"
            type="submit"
            class="btn btn-primary"
            [disabled]="isSubmitting || isUpdating"
          >
            <span
              *ngIf="isSubmitting || isUpdating"
              class="loading-spinner"
            ></span>
            <span *ngIf="!isSubmitting && !isUpdating">
              {{ getSubmitButtonText() }}
            </span>
            <span *ngIf="isSubmitting || isUpdating">
              {{ modalMode === "add" ? "Création..." : "Mise à jour..." }}
            </span>
          </button>
        </div>
      </form>
    </div>
  </div>

  <div
    *ngIf="isDeleteModalOpen"
    class="modal-overlay"
    (click)="closeDeleteModal()"
  >
    <div
      class="modal-container delete-modal"
      (click)="$event.stopPropagation()"
    >
      <div class="modal-header">
        <h2 class="modal-title text-red-600">
          <mat-icon class="delete-icon">warning</mat-icon>
          Confirmer la suppression
        </h2>
        <div *ngIf="isDeleting" class="modal-loading">
          <span class="loading-spinner"></span>
          <span class="loading-text">Suppression en cours...</span>
        </div>
      </div>

      <div class="delete-content" [class.content-loading]="isDeleting">
        <p class="delete-message">
          Êtes-vous sûr de vouloir supprimer cette licence ?
        </p>
        <div class="delete-details" *ngIf="itemToDelete">
          <p><strong>Nom:</strong> {{ itemToDelete.Name }}</p>
          <p>
            <strong>Description:</strong>
            {{ itemToDelete.Description || "Aucune description" }}
          </p>
          <p>
            <strong>Options:</strong>
            {{ getOptionsCount(itemToDelete.Options) }}
          </p>
        </div>
        <p class="delete-warning">
          Cette action est irréversible et ne peut pas être annulée.
        </p>
      </div>

      <div class="modal-actions">
        <button
          type="button"
          class="btn btn-cancel"
          (click)="closeDeleteModal()"
          [disabled]="isDeleting"
        >
          Annuler
        </button>
        <button
          type="button"
          class="btn btn-danger"
          (click)="confirmDelete()"
          [disabled]="isDeleting"
        >
          <span *ngIf="isDeleting" class="loading-spinner"></span>
          <span *ngIf="!isDeleting">Supprimer</span>
          <span *ngIf="isDeleting">Suppression...</span>
        </button>
      </div>
    </div>
  </div>
</div>
