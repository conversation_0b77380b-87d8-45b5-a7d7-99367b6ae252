import { Capteur } from '@app/core/models/capteur';
import { DeviceProperty } from '@app/shared/models/DeviceProperty';
import { TypeCapteur } from '@app/shared/models/typeCapteur';

export interface DeviceMapping {
  originalDevice: string;
  originalDeviceType?: string;
  originalDeviceTypeName?: string;
  selectedCapteurId: string;
  selectedCapteur?: Capteur;
  selectedTypeCapteur?: TypeCapteur;
  availableProperties?: DeviceProperty[];
}
