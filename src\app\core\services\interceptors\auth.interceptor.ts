import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { catchError, throwError } from 'rxjs';
import { AuthService } from '../auth.service';

export const authInterceptor: HttpInterceptorFn = (req, next) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  // Skip for auth requests or if no token
  if (req.url.includes('/auth/') || !authService.getToken()) {
    return next(req);
  }

  // Add auth header if token exists and is valid
  if (authService.isLoggedIn()) {
    const authReq = req.clone({
      setHeaders: {
        Authorization: `Bearer ${authService.getToken()}`
      }
    });
    return next(authReq).pipe(
      catchError((error) => {
        if (error.status === 401) {
          authService.logout();
          router.navigate(['/login'], {
            queryParams: { returnUrl: router.url }
          });
        }
        return throwError(() => error);
      })
    );
  }

  // If token is expired, logout and redirect
  authService.logout();
  router.navigate(['/login'], {
    queryParams: { returnUrl: router.url }
  });
  return throwError(() => new Error('Session expired'));
};