import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AppDeleteConfirmDialogComponent } from './app-delete-confirm-dialog.component';

describe('AppDeleteConfirmDialogComponent', () => {
  let component: AppDeleteConfirmDialogComponent;
  let fixture: ComponentFixture<AppDeleteConfirmDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AppDeleteConfirmDialogComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(AppDeleteConfirmDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
