import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';

import { AuthService, RegisterModel } from '@app/core/services/auth.service';

import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';
import { NgToastModule, NgToastService } from 'ng-angular-popup';

import { InputTextModule } from 'primeng/inputtext';
import { PasswordModule } from 'primeng/password';
import { ButtonModule } from 'primeng/button';
import { RippleModule } from 'primeng/ripple';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-register',
  standalone: true,
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.css'], // Reuse login styles
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    InputTextModule,
    PasswordModule,
    ButtonModule,
    RippleModule,
    MatIconModule,
    NgxUiLoaderModule,
    NgToastModule
  ]
})
export class RegisterComponent {
  registerForm: FormGroup;
  isSubmitting = false;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private loader: NgxUiLoaderService,
    private toastService: NgToastService
  ) {
    this.registerForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]],
      phoneNumber: [''],
      userRole: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });
  }

  passwordMatchValidator(form: FormGroup): null | object {
    const password = form.get('password')?.value;
    const confirm = form.get('confirmPassword')?.value;
    return password === confirm ? null : { mismatch: true };
  }

  onSubmit(): void {
    if (this.registerForm.invalid) {
      this.toastService.warning('Veuillez remplir tous les champs', 'Erreur', 3000);
      this.markFormAsTouched();
      return;
    }

    this.isSubmitting = true;
    this.loader.start();

    const formValues = this.registerForm.value;
    const registerModel: RegisterModel = {
      email: formValues.email,
      password: formValues.password,
      confirmPassword: formValues.confirmPassword,
      phoneNumber: formValues.phoneNumber,
      userRole: formValues.userRole
    };

    this.authService.register(registerModel).subscribe({
      next: () => {
        this.loader.stop();
        this.toastService.success('Compte créé avec succès', 'Succès', 3000);
        this.router.navigate(['/login']);
      },
      error: (err) => {
        this.loader.stop();
        this.toastService.danger(err?.error?.message || 'Échec de l\'inscription', 'Erreur', 3000);
      }
    });
  }

  private markFormAsTouched(): void {
    Object.values(this.registerForm.controls).forEach(control => control.markAsTouched());
  }
}
