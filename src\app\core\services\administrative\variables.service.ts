import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';import { ApiService } from '../api.service';
import { Variables } from '@app/core/models/variables';

@Injectable({ providedIn: 'root' })
export class VariablesApiService extends ApiService<Variables> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("variables");
  }
}

