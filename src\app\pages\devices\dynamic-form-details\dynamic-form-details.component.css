.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.03);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.confirmation-popup {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  max-width: 550px;
  width: 90%;
  box-shadow: 0 5px 5px rgba(0, 0, 0, 0.011);
  position: relative;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f1f5f9;
}

.popup-header h3 {
  font-size: 1.4rem;
  color: #374151;
  margin: 0;
}

.close-popup-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background: #f8fafc;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s;
}

.close-popup-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.popup-content {
  margin-bottom: 2rem;
}

.label {
  font-weight: 500;
  color: #374151;
  font-size: 1.2rem;
}

.input-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.improved-input {
  width: 100%;
  padding: 0.8rem;
  border-radius: 12px;
  border: 1.5px solid #c7c7c7;
  background: white;
  font-size: 1.08rem;
  color: #4CAF50;
  font-weight: 500;
  transition: border 0.2s, box-shadow 0.2s;
  outline: none;
}
.improved-input:focus-within {
  border-color: #4caf50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.15);
}

.input-clear-btn {
  position: absolute;
  right: 1rem;
  background: transparent;
  border: none;
  color: #a0aec0;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s;
}

.input-clear-btn:hover {
  color: #ef4444;
}

.popup-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #f1f5f9;
}

.cancel-btn,
.confirm-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 0.95rem;
  border: none;
}

.cancel-btn {
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.cancel-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.confirm-btn {
  background: #4CAF50;
  color: white;
}

.confirm-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 18px rgba(73, 179, 142, 0.3);
}

.confirm-btn:disabled {
  background: #e5e7eb !important;
  color: #a1a1aa !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
  opacity: 0.7;
}
.device-details {
  display: flex;
  flex-direction: row;
  gap: 20px;
  margin-bottom: 10px;
}

.device-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.05rem;
}

.bullet {
  color: #4CAF50;
  font-weight: bold;
  font-size: 1.1rem;
}

.device-info-label {
  font-weight: 600;
  color: #4CAF50;
}

.device-info-value {
  font-weight: 400;
  color: #374151;
}
