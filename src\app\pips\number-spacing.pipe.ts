// import { Pipe, PipeTransform } from '@angular/core';

// @Pipe({
//   name: 'numberSpacing'
// })
// export class NumberSpacingPipe implements PipeTransform {

//   transform(value: number | string): string {
//     if (value === null || value === undefined) return '';
    
//     const strValue = value.toString();
    
//     // Process only the numeric parts of the string, leaving alphabets intact
//     return strValue.replace(/\d+/g, (num) => {
//       // Add space every 3 digits from the right for each numeric sequence
//       return num.replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
//     });
//   }

// }
// import { Pipe, PipeTransform } from '@angular/core';

// @Pipe({
//   name: 'numberSpacing'
// })
// export class NumberSpacingPipe implements PipeTransform {

//   transform(value: number | string): string {
//     if (value === null || value === undefined) return '';

//     const strValue = value.toString().replace(/\s/g, ''); // Supprimer les espaces existants

//     // Ne formatter que si la valeur est composée uniquement de chiffres
//     if (/^\d+$/.test(strValue)) {
//       return strValue.replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
//     }

//     // Pour les chaînes alphanumériques, formater uniquement les séquences numériques
//     return strValue.replace(/\d+/g, (num) => {
//       return num.replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
//     });
//   }
// }
import { Pipe, PipeTransform } from '@angular/core';
 
@Pipe({
  name: 'numberSpacing'
})
export class NumberSpacingPipe implements PipeTransform {
 
  transform(value: number | string, type?: 'SIREN' | 'SIRET'): string {
    if (value === null || value === undefined) return '';
 
    // On retire d'abord tous les espaces
    const strValue = value.toString().replace(/\s/g, '');
 
    // Si ce n’est pas que des chiffres, on formate uniquement les parties numériques
    if (!/^\d+$/.test(strValue)) {
      return strValue.replace(/\d+/g, (num) => num.replace(/\B(?=(\d{3})+(?!\d))/g, ' '));
    }
 
    // Formatage progressif selon le type
    if (type === 'SIREN') {
      // SIREN = 9 chiffres max, format "123 456 789"
      const truncated = strValue.slice(0, 9);
      return truncated.replace(/(\d{3})(?=\d)/g, '$1 ');
    }
 
    if (type === 'SIRET') {
      // SIRET = 14 chiffres max, format "123 456 789 12345"
      const truncated = strValue.slice(0, 14);
      if (truncated.length <= 9) {
        return truncated.replace(/(\d{3})(?=\d)/g, '$1 ');
      } else {
        const firstPart = truncated.slice(0, 9).replace(/(\d{3})(?=\d)/g, '$1 ');
        const secondPart = truncated.slice(9);
        return `${firstPart} ${secondPart}`;
      }
    }
 
    // Par défaut, formate avec un espace tous les 3 chiffres (pour autres nombres)
    return strValue.replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
  }
}