<div class="table-section">
  <mat-card>
    <h3>Liste des Capteurs</h3>
    <!-- Sélecteur de table -->
    <div class="filters-container">
      <mat-form-field appearance="outline" class="filter-input">
        <mat-label>Table des Capteurs</mat-label>
        <mat-select [(value)]="selectedTable" (selectionChange)="loadSensorData()">
          <mat-option *ngFor="let table of tables" [value]="table">{{ table }}</mat-option>
        </mat-select>
      </mat-form-field>
      <!-- Champ de recherche -->
      <mat-form-field appearance="outline" class="filter-input">
        <mat-label>Rechercher</mat-label>
        <input matInput (keyup)="applyFilter($event)" placeholder="Filtrer les capteurs...">
      </mat-form-field>
    </div>
    <!-- Tableau des données -->
    <table mat-table [dataSource]="dataSource" matSort class="sensor-table mat-elevation-z8" *ngIf="sensorData.length > 0">
      <!-- SensorType Column -->
      <ng-container matColumnDef="sensorType">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Type</th>
        <td mat-cell *matCellDef="let element">{{ element.SensorType }}</td>
      </ng-container>
      <!-- Value Column -->
      <ng-container matColumnDef="value">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Valeur</th>
        <td mat-cell *matCellDef="let element">{{ element.NumericValue }}</td>
      </ng-container>
      <!-- Unit Column -->
      <ng-container matColumnDef="unit">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Unité</th>
        <td mat-cell *matCellDef="let element">{{ element.Unit }}</td>
      </ng-container>
      <!-- Battery Column -->
      <ng-container matColumnDef="battery">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Batterie (%)</th>
        <td mat-cell *matCellDef="let element">{{ element.Battery }}</td>
      </ng-container>
      <!-- Timestamp Column -->
      <ng-container matColumnDef="timestamp">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Timestamp</th>
        <td mat-cell *matCellDef="let element">{{ element.Timestamp | date:'short' }}</td>
      </ng-container>
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
    <!-- Pagination -->
    <mat-paginator [pageSizeOptions]="[5, 10, 20]" showFirstLastButtons></mat-paginator>
  </mat-card>
</div>