<div class="edit-form-container">
  <div class="edit-form-card">
    <h2 class="form-title">Modifier le Local</h2>

    <form [formGroup]="editLocalForm" (ngSubmit)="onSubmit()" (keydown.enter)="$event.preventDefault()">
      <div class="form-grid">

        <!-- Nom -->
        <div class="form-group full-width">
          <label for="nom">Nom du local</label>
          <input type="text" id="nom" formControlName="nom" class="form-control" placeholder="Nom du local">
        </div>

        <!-- Base Topic -->
        <div class="form-group full-width">
          <label for="baseTopic">Base Topic</label>
          <input type="text" id="baseTopic" formControlName="baseTopic" class="form-control" placeholder="Ex: site/etage/salle">
        </div>

        <!-- ID Site -->
        <div class="form-group full-width">
          <label for="idSite">ID du site</label>
          <input type="number" id="idSite" formControlName="idSite" class="form-control">
        </div>

        <!-- Type -->
        <div class="form-group">
          <label for="type">Type de local</label>
          <input id="type" list="localTypesList" formControlName="type" class="form-control" placeholder="Sélectionner ou saisir un type">
          <datalist id="localTypesList">
            <option *ngFor="let type of localTypes" [value]="type.value">
              {{ type.label }}
            </option>
          </datalist>
        </div>

        <!-- Étage -->
        <div class="form-group">
          <label for="etage">Étage</label>
          <input type="number" id="etage" formControlName="etage" class="form-control">
        </div>

        <!-- Surface -->
        <div class="form-group">
          <label for="surface">Surface (m²)</label>
          <input type="number" id="surface" formControlName="surface" class="form-control">
        </div>

        <!-- Hauteur sous plafond -->
        <div class="form-group">
          <label for="hauteurSousPlafond">Hauteur sous plafond (m)</label>
          <input type="number" id="hauteurSousPlafond" formControlName="hauteurSousPlafond" class="form-control">
        </div>

        <!-- Capacité -->
        <div class="form-group">
          <label for="capacitePersonnes">Capacité (personnes)</label>
          <input type="number" id="capacitePersonnes" formControlName="capacitePersonnes" class="form-control">
        </div>

        <!-- Température cible -->
        <div class="form-group">
          <label for="temperatureCible">Température cible (°C)</label>
          <input type="number" id="temperatureCible" formControlName="temperatureCible" class="form-control">
        </div>

        <!-- Consommation électrique -->
        <div class="form-group">
          <label for="consommationElectriqueMensuelle">Consommation mensuelle (kWh)</label>
          <input type="number" id="consommationElectriqueMensuelle" formControlName="consommationElectriqueMensuelle" class="form-control">
        </div>

        <!-- Date maintenance -->
        <div class="form-group">
          <label for="dateDerniereMaintenance">Dernière maintenance</label>
          <input type="date" id="dateDerniereMaintenance" formControlName="dateDerniereMaintenance" class="form-control">
        </div>

        <!-- Description -->
        <div class="form-group full-width">
          <label for="description">Description</label>
          <textarea id="description" formControlName="description" rows="3" class="form-control"></textarea>
        </div>

        <!-- Architecture 2D -->
        <div class="form-group">
          <label for="architecture2D">Plan d'architecture 2D</label>
          <div class="file-upload">
            <input 
              type="file" 
              id="architecture2D" 
              (change)="onArchitecture2DSelected($event)"
              accept="image/*"
              class="form-control">
            <!-- <div class="preview" *ngIf="architecture2DPreview || local.imageArchitecture2D">
              <img [src]="architecture2DPreview || local.imageArchitecture2D" [alt]="'Plan architectural du local ' + local.id">
            </div> -->
          </div>
        </div>

        <!-- Image du local -->
        <div class="form-group">
          <label for="localeImage">Photo du local</label>
          <div class="file-upload">
            <input 
              type="file" 
              id="localeImage" 
              (change)="onLocaleImageSelected($event)"
              accept="image/*"
              class="form-control">
            <!-- <div class="preview" *ngIf="localeImagePreview || local.imageLocal"> -->
              <!-- <img [src]="localeImagePreview || local.imageLocal" [alt]="'Vue du local ' + local.id"> -->
            <!-- </div> -->
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="form-actions">
        <button type="button" class="btn btn-secondary" (click)="onCancel()">Annuler</button>
        <button type="submit" class="btn btn-primary" [disabled]="!editLocalForm.valid">Enregistrer</button>
      </div>
    </form>
  </div>
</div>
