// Internal interface for the raw data structure as it comes from the backend's `rawData` field

export interface RawDataBackendStructure {
  rule_name: string;
  topic_pattern: string[];
  conditions: {
    groups: Array<{
      operator: string;
      conditions: Array<{
        type: string; // e.g., "sensor_data"
        device: string; // e.g., "sensor_cube"
        key: string; // e.g., "side"
        operator: string; // e.g., "=="
        value: string; // e.g., "2"
      }>;
    }>;
  };
  actions: Array<{
    type: string; // e.g., "publish"
    topic: string; // e.g., "zigbee2mqtt/roller_shade/set"
    payload: { [key: string]: any; }; // e.g., { "position": 25 }
  }>;
  schedule_config?: {
    enabled: boolean;
    time?: string;
    cron_expression?: string;
  };
  enabled: boolean;
  priority: number;
}
