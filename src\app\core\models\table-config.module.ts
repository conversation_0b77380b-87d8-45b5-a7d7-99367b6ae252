export interface Column {
  key: string;
  label: string;
  sortable?: boolean;
  type?: 'text' | 'number' | 'date' | 'boolean' | 'custom';
  format?: (value: any) => string;
  filterType?: 'text' | 'select' | 'date' | 'number';
  filterPlaceholder?: string;
  filterOptions?: { value: any; label: string }[];
}

export interface TableConfig {
  columns: Column[];
  actions?: {
    view?: boolean;
    edit?: boolean;
    delete?: boolean;
    custom?: Array<{
      icon: string;
      label: string;
      action: string;
    }>;
  };
  pageSize: number;
  pageSizeOptions: number[];
}