.org-card {
  position: relative;
  max-width: 350px;
  min-height: 280px;
  cursor: pointer;
  border-radius: 10px;
  transition: all 0.3s ease;
  border-top: 4px solid var(--primary);
  background-color: #fff;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.card-image-container {
  position: relative;
  overflow: hidden;
  height: 150px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f8f0;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: contain; /* Changed from cover to contain */
  transition: transform 0.5s ease;
  padding: 8px; /* Add padding to prevent image from touching edges */
  background-color: white; /* Optional: adds a white background behind the image */
}

.card-image-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f0f8f0;
  z-index: -1;
}

.org-card:hover .card-image {
  transform: scale(1.05);
}

/* .card-overlay {
  position: absolute;
  top: 0;
  right: 0;
  background-color: rgba(76, 175, 80, 0.8);
  color: white;
  padding: 8px;
  border-radius: 0 0 0 10px;
} */

.card-overlay .material-icons {
  font-size: 24px;
}

.card-header {
  padding: 16px 16px 8px;
  border-bottom: 1px solid #f0f0f0;
}

.org-title {
  color: #2E7D32;
  font-weight: 500;
  margin: 0 0 8px;
  font-size: 1.2rem;
}

.org-email {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #5c5c5c;
  font-size: 0.85rem;
  margin: 0;
}

.card-content {
  padding: 16px;
  flex-grow: 1;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
}

.full-width {
  width: 100%;
}

.icon-small {
  font-size: 18px;
  color: var(--primary);
}

.card-actions {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  margin-top: auto;
  background-color: #fafafa;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 8px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 0;
  width: 100%;
}

.btn .material-icons {
  font-size: 16px;
}

/* Update button colors and hover effects */
.btn-primary {
  background-color: var(--primary);
  color: white;
}

.btn-primary:hover {
  background-color: #3e8e41;
}

.btn-accent {
  background-color: #2196F3;
  color: white;
}

.btn-accent:hover {
  background-color: #0b7dda;
}

.btn-danger {
  background-color: #f44336;
  color: white;
  padding: 6px 8px;
}

.btn-danger:hover {
  background-color: #d32f2f;
}

.btn:hover .material-icons {
  transform: translateY(-2px);
}

/* Animation for hover */
@keyframes cardPulse {
  0% {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  }
  50% {
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
  }
  100% {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  }
}

.org-card:hover {
  animation: cardPulse 2s infinite;
}





.energy-indicators {
  display: flex;
  justify-content: space-around;
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.indicator .material-icons {
  font-size: 24px;
}

.indicator.positive .material-icons {
  color: var(--primary);
}

.indicator.negative .material-icons {
  color: #f44336;
}

.indicator .value {
  font-weight: 500;
  font-size: 1.1rem;
}

.indicator .label {
  font-size: 0.8rem;
  color: #666;
}

.indicator.positive .value {
  color: var(--primary);
}

.indicator.negative .value {
  color: #f44336;
}

/* Responsive styles */
@media (max-width: 360px) {
  .card-actions {
    grid-template-columns: 1fr;
  }

  .btn {
    padding: 10px;
    font-size: 0.9rem;
  }
}