import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { CategoryStatCardComponent } from '../../../components/category-stat-card/category-stat-card.component';
import { trigger, transition, style, animate } from '@angular/animations';
import { OrganisationType } from '@app/core/services/organisation.service';

// Interface for organisation stats
interface OrganisationStat {
  type: OrganisationType;
  count: number;
}

@Component({
  selector: 'app-categories-sites',
  standalone: true,
  imports: [CommonModule, CategoryStatCardComponent],
  templateUrl: './categories-sites.component.html',
  styleUrls: ['./categories-sites.component.css'],
  animations: [
    trigger('cardAnimation', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(20px)' }),
        animate('300ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ])
    ])
  ]
})
export class CategoriesSitesComponent {
  @Input() siteStats: OrganisationStat[] = []; // Changed to accept organisation stats
  @Output() typeSelected = new EventEmitter<string>(); // Emits organisation type
  @Output() viewDetailsClicked = new EventEmitter<string>(); // Emits the organisation type for details

  constructor(readonly router: Router) {}

  onTypeClick(type: string) {
    this.typeSelected.emit(type); // Emit the organisation type
  }

  onDetailsClick(type: string) {
    this.viewDetailsClicked.emit(type); // Emit the organisation type
  }
}