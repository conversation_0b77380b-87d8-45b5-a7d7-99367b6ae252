// Complete rule-form.component.ts - Improved UX with fewer dialogs
import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { DragDropModule, CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { forkJoin } from 'rxjs';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';

// Import the services
import { TypeCapteurApiService } from '@app/core/services/administrative/typecapteur.service';
import { VariablesApiService } from '@app/core/services/administrative/variables.service';
import { RulesApiService } from '@app/core/services/administrative/rules.service';

// Import the backend models
import { TypeCapteur } from '@app/shared/models/typeCapteur';
import { Variables } from '@app/core/models/variables';
import { PayloadOption } from '../../../shared/models/PayloadOption';
import { DeviceProperty } from '../../../shared/models/DeviceProperty';
import { DeviceAction } from '../../../shared/models/DeviceAction';
import { DeviceTypes } from '../../../shared/models/DeviceTypes';
import { SensorDataCondition } from '../../../shared/models/SensorDataCondition';
import { TimeCondition } from '../../../shared/models/TimeCondition';
import { ConditionGroup } from '../../../shared/models/ConditionGroup';
import { PublishAction } from '../../../shared/models/PublishAction';
import { RuleDto } from '../../../shared/models/RuleDto';

import { ConfirmationDialogComponent} from '../../confirmation-dialog/confirmation-dialog.component';

// Define operator interface for better type safety
interface OperatorOption {
  label: string;
  value: string;
  types: string[]; // Which variable types support this operator
}

@Component({
  selector: 'app-rule-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    DragDropModule,
    NgxUiLoaderModule
  ],
  templateUrl: './rule-form.component.html',
  styleUrls: ['./rule-form.component.css']
})
export class RuleFormComponent implements OnInit {
  rule: RuleDto = {
    rule_name: '',
    topic_pattern: [],
    conditions: {
      operator: 'AND',
      groups: [
        {
          operator: 'AND',
          conditions: []
        }
      ]
    },
    actions: [],
    schedule_config: { enabled: false },
    enabled: true,
    priority: 1
  };

  priorities = [1, 2, 3, 4, 5];

  // Operators filtered by variable type
  allOperators: OperatorOption[] = [
    { label: '=', value: '==', types: ['string', 'number', 'boolean'] },
    { label: '≠', value: '!=', types: ['string', 'number', 'boolean'] },
    { label: '>', value: '>', types: ['number'] },
    { label: '<', value: '<', types: ['number'] },
    { label: '≥', value: '>=', types: ['number'] },
    { label: '≤', value: '<=', types: ['number'] }
  ];

  operator = ['AND', 'OR'];
  selectedActionTypes: string[] = [];
  selectedDevices: string[] = [];

  // Backend data
  typeCapteurs: TypeCapteur[] = [];
  variables: Variables[] = [];
  
  // Processed device data structure
  deviceTypes: DeviceTypes = {
    sensors: {},
    actuators: {}
  };

  // Loading states
  isLoading = true;
  loadingError: string | null = null;
  isSaving = false;
  isGeneratingSummary = false;

  constructor(
    public dialogRef: MatDialogRef<RuleFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { rule: RuleDto | null },
    private typeCapteurService: TypeCapteurApiService,
    private variablesService: VariablesApiService,
    private rulesService: RulesApiService,
    private dialog: MatDialog,
    private ngxUiLoaderService: NgxUiLoaderService
  ) {}

  ngOnInit(): void {
    this.loadBackendData();
    if (!this.rule.conditions.operator) {
      this.rule.conditions.operator = 'AND';
    }
  }

  // === ENHANCED OPERATOR LOGIC BY VARIABLE TYPE ===

  /**
   * Get available operators for a specific condition based on the selected property type
   */
  getAvailableOperators(condition: SensorDataCondition): OperatorOption[] {
    const selectedProperty = this.getSelectedProperty(condition);
    
    if (!selectedProperty) {
      return this.allOperators;
    }

    const propertyType = selectedProperty.type.toLowerCase();
    
    switch (propertyType) {
      case 'boolean':
        return this.allOperators.filter(op => ['==', '!='].includes(op.value));
      case 'number':
      case 'float':
      case 'double':
        return this.allOperators;
      case 'string':
      default:
        return this.allOperators.filter(op => ['==', '!='].includes(op.value));
    }
  }

  /**
   * Get suggested values for a condition based on the Actions array from backend
   * Filters out placeholder values for clean UI
   */
  getSuggestedValues(condition: SensorDataCondition): string[] {
    const selectedProperty = this.getSelectedProperty(condition);
    
    if (!selectedProperty || !selectedProperty.values || selectedProperty.values.length === 0) {
      return [];
    }

    // Convert all values to strings and filter out placeholder values
    return selectedProperty.values
      .map(val => String(val))
      .filter(val => val && val.trim() !== '' && val !== 'placeholder' && val !== 'select' && val !== 'valeur');
  }

  /**
   * Get the first suggested value for initializing the dropdown
   */
  getFirstSuggestedValue(condition: SensorDataCondition): string {
    const suggestions = this.getSuggestedValues(condition);
    return suggestions.length > 0 ? suggestions[0] : '';
  }

  /**
   * Check if a condition has suggested values available
   */
  hasSuggestedValues(condition: SensorDataCondition): boolean {
    return this.getSuggestedValues(condition).length > 0;
  }

  /**
   * Get the display value for the select dropdown with auto-initialization
   */
  getSelectDisplayValue(condition: SensorDataCondition): string {
    if (condition.value) {
      return String(condition.value);
    }
    
    // Auto-initialize with first suggested value if available
    const firstSuggested = this.getFirstSuggestedValue(condition);
    if (firstSuggested) {
      condition.value = this.parseValueForCondition(condition, firstSuggested);
      return firstSuggested;
    }
    
    return '';
  }

  /**
   * Get placeholder text for value input based on property type
   */
  getValuePlaceholder(condition: SensorDataCondition): string {
    const selectedProperty = this.getSelectedProperty(condition);
    
    if (!selectedProperty) {
      return 'Sélectionner d\'abord une propriété';
    }

    const propertyType = selectedProperty.type.toLowerCase();
    
    switch (propertyType) {
      case 'boolean':
        return 'true ou false';
      case 'number':
      case 'integer':
      case 'float':
      case 'double':
        return 'Entrer une valeur numérique';
      case 'string':
      default:
        return 'Entrer une valeur texte';
    }
  }

  /**
   * Validate value input based on property type
   */
  isValidValue(condition: SensorDataCondition, value: any): boolean {
    const selectedProperty = this.getSelectedProperty(condition);
    
    if (!selectedProperty) {
      return false;
    }

    if (value === null || value === undefined || value === '') {
      return false;
    }

    const propertyType = selectedProperty.type.toLowerCase();
    
    switch (propertyType) {
      case 'boolean':
        return value === true || value === false || value === 'true' || value === 'false';
      case 'number':
      case 'integer':
      case 'float':
      case 'double':
        return !isNaN(Number(value)) && isFinite(Number(value));
      case 'string':
      default:
        return true;
    }
  }

  /**
   * Parse value from input to the correct type for the backend
   */
  parseValueForCondition(condition: SensorDataCondition, inputValue: string): any {
    const selectedProperty = this.getSelectedProperty(condition);
    
    if (!selectedProperty || !inputValue) {
      return inputValue;
    }

    const propertyType = selectedProperty.type.toLowerCase();

    switch (propertyType) {
      case 'boolean':
        if (inputValue === 'true') return true;
        if (inputValue === 'false') return false;
        return inputValue;
      case 'number':
      case 'integer':
        const intValue = parseInt(inputValue, 10);
        return isNaN(intValue) ? inputValue : intValue;
      case 'float':
      case 'double':
        const floatValue = parseFloat(inputValue);
        return isNaN(floatValue) ? inputValue : floatValue;
      case 'string':
      default:
        return String(inputValue);
    }
  }

  // === BACKEND DATA LOADING ===

  private loadBackendData(): void {
    this.isLoading = true;
    this.loadingError = null;

    forkJoin({
      typeCapteurs: this.typeCapteurService.getAll(),
      variables: this.variablesService.getAll()
    }).subscribe({
      next: (data) => {
        this.typeCapteurs = data.typeCapteurs;
        this.variables = data.variables;
        this.processBackendData();
        this.initializeRule();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading backend data:', error);
        this.loadingError = 'Erreur lors du chargement des données. Veuillez réessayer.';
        this.isLoading = false;
        this.initializeRule();
      }
    });
  }

  private processBackendData(): void {
    this.deviceTypes = { sensors: {}, actuators: {} };

    this.typeCapteurs.forEach(typeCapteur => {
      const relatedVariables = this.variables.filter(v => v.IdTypeCapteur === typeCapteur.Id);
      
      const deviceCategory: 'sensors' | 'actuators' = 
        typeCapteur.DeviceType?.toLowerCase() === 'actuator' ? 'actuators' : 'sensors';
      
      const deviceKey = this.normalizeDeviceKey(typeCapteur.Nom);
      
      if (deviceCategory === 'sensors') {
        this.deviceTypes.sensors[deviceKey] = {
          topic: typeCapteur.Topic,
          device_name: typeCapteur.Nom,
          DisplayName: typeCapteur.DisplayName || typeCapteur.Nom,
          properties: relatedVariables.map(variable => {
            const declaredType = this.mapVariableType(variable.Type);
            const actualType = this.detectActualType(variable.Actions, declaredType);
            return {
              key: variable.Key,
              type: actualType,
              values: this.parseVariableValues(variable.Actions)
            };
          })
        };
      } else {
        this.deviceTypes.actuators[deviceKey] = {
          topic: typeCapteur.Topic,
          device_name: typeCapteur.Nom,
          DisplayName: typeCapteur.DisplayName || typeCapteur.Nom,
          actions: relatedVariables.map(variable => ({
            type: variable.Key,
            payload: {},
            options: this.parseActionOptions(variable.Actions)
          }))
        };
      }
    });
  }

  private normalizeDeviceKey(name: string): string {
    return name.toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^a-z0-9_]/g, '')
      .replace(/^sensor_|^actuator_/, '');
  }

  private mapVariableType(backendType: string): string {
    const typeMapping: { [key: string]: string } = {
      'String': 'string',
      'Integer': 'number',
      'Float': 'number',
      'Double': 'number',
      'Boolean': 'boolean',
      'Bool': 'boolean',
      'Number': 'number'
    };
    return typeMapping[backendType] || 'string';
  }

  private parseVariableValues(actions: string[] | null): string[] {
    if (!actions || !Array.isArray(actions)) return [];
    return actions.map(item => String(item));
  }

  private detectActualType(actions: string[] | null, declaredType: string): string {
    if (!actions || actions.length === 0) {
      return declaredType;
    }

    const allBooleanLike = actions.every(action => 
      action === 'true' || action === 'false'
    );
    
    if (allBooleanLike) {
      return 'boolean';
    }

    const allNumeric = actions.every(action => {
      const num = Number(action);
      return !isNaN(num) && isFinite(num);
    });
    
    if (allNumeric) {
      return 'number';
    }

    return declaredType;
  }

  private parseActionOptions(actions: string[] | null): PayloadOption[] {
    if (!actions || !Array.isArray(actions)) return [];
    return actions.map((item: string) => ({
      display: item,
      value: item
    }));
  }

  private initializeRule(): void {
    if (this.data.rule) {
      if (typeof this.data.rule === 'object' && 'RawData' in this.data.rule) {
        try {
          this.rule = JSON.parse((this.data.rule as any).RawData);
        } catch (error) {
          console.error('Error parsing rule RawData:', error);
          this.rule = JSON.parse(JSON.stringify(this.data.rule));
        }
      } else {
        this.rule = JSON.parse(JSON.stringify(this.data.rule));
      }
    } else {
      if (this.rule.conditions.groups.length === 0) {
        this.addConditionGroup();
      }
      if (this.rule.conditions.groups[0].conditions.length === 0) {
        this.addCondition(0);
      }
    }
    if (!this.rule.conditions.operator) {
      this.rule.conditions.operator = 'AND';
    }
    this.updateTopicPattern();
  }

  // === CONDITION CHANGE HANDLERS ===

  onConditionDeviceChange(condition: SensorDataCondition, deviceType: 'sensors' | 'actuators', deviceName: string): void {
    condition.device = deviceName;
    condition.key = '';
    condition.operator = '==';
    condition.value = '';
    this.updateTopicPattern();
  }

  onConditionKeyChange(condition: SensorDataCondition): void {
    const oldOperator = condition.operator;
    condition.value = '';
    
    const availableOperators = this.getAvailableOperators(condition);
    if (availableOperators.length > 0 && !availableOperators.some(op => op.value === oldOperator)) {
      condition.operator = availableOperators[0].value;
    }
    
    // Auto-select first suggested value if available
    const firstSuggestedValue = this.getFirstSuggestedValue(condition);
    if (firstSuggestedValue) {
      condition.value = this.parseValueForCondition(condition, firstSuggestedValue);
    }
    
    this.updateTopicPattern();
  }

  onConditionOperatorChange(condition: SensorDataCondition): void {
    const availableOperators = this.getAvailableOperators(condition);
    if (!availableOperators.some(op => op.value === condition.operator)) {
      if (availableOperators.length > 0) {
        condition.operator = availableOperators[0].value;
      }
    }
    this.updateTopicPattern();
  }

  onConditionValueChange(condition: SensorDataCondition, newValue?: string): void {
    if (newValue !== undefined) {
      condition.value = this.parseValueForCondition(condition, newValue);
    }
    this.updateTopicPattern();
  }

  onTimeConditionChange(condition: TimeCondition): void {
    this.updateTopicPattern();
  }

  onPropertySelected(condition: SensorDataCondition): void {
    const firstSuggestedValue = this.getFirstSuggestedValue(condition);
    if (firstSuggestedValue && !condition.value) {
      condition.value = this.parseValueForCondition(condition, firstSuggestedValue);
      this.updateTopicPattern();
    }
  }

  updateConditionValue(condition: SensorDataCondition, event: Event): void {
    const target = event.target as HTMLInputElement;
    const rawValue = target.value;
    const parsedValue = this.parseValueForCondition(condition, rawValue);
    condition.value = parsedValue;
    this.updateTopicPattern();
  }

  updateConditionValueFromSelect(condition: SensorDataCondition, event: Event): void {
    const target = event.target as HTMLSelectElement;
    const selectedValue = target.value;
    condition.value = this.parseValueForCondition(condition, selectedValue);
    this.updateTopicPattern();
  }

  onConditionValueBlur(condition: SensorDataCondition, event: Event): void {
    const target = event.target as HTMLInputElement;
    const value = target.value;
    
    if (value && !this.isValidValue(condition, value)) {
      console.warn('Invalid value entered:', value);
    }
  }

  // === CONDITION MANAGEMENT (IMPROVED UX - FEWER DIALOGS) ===

  addConditionGroup(): void {
    this.rule.conditions.groups.push({
      operator: 'AND',
      conditions: [{ type: 'payload', device: '', key: '', operator: '==', value: '' } as SensorDataCondition]
    });
    this.updateTopicPattern();
  }

  addCondition(groupIndex: number): void {
    const newCondition: SensorDataCondition = {
      type: 'payload',
      device: '',
      key: '',
      operator: '==',
      value: ''
    };
    
    this.rule.conditions.groups[groupIndex].conditions.push(newCondition);
    this.updateTopicPattern();
  }

  addTimeCondition(groupIndex: number): void {
    this.rule.conditions.groups[groupIndex].conditions.push({
      type: 'time',
      start_time: '',
      end_time: ''
    } as TimeCondition);
    this.updateTopicPattern();
  }

  // ENHANCED: Only show dialog for groups with significant content
  removeConditionGroup(index: number): void {
    const group = this.rule.conditions.groups[index];
    
    // Only show confirmation for groups with meaningful content
    const hasMeaningfulContent = this.hasMeaningfulGroupContent(group);
    
    if (hasMeaningfulContent) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: 'Supprimer le groupe',
          message: `Ce groupe contient des conditions avec des données. Voulez-vous le supprimer ?`,
          icon: 'delete_forever'
        },
        disableClose: true,
        panelClass: 'confirmation-dialog-panel'
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.rule.conditions.groups.splice(index, 1);
          this.updateTopicPattern();
        }
      });
    } else {
      // Directly remove empty or meaningless groups
      this.rule.conditions.groups.splice(index, 1);
      this.updateTopicPattern();
    }
  }

  /**
   * Check if a condition group has meaningful content worth confirming deletion
   */
  private hasMeaningfulGroupContent(group: ConditionGroup): boolean {
    return group.conditions.some(condition => {
      if (condition.type === 'payload') {
        const sc = condition as SensorDataCondition;
        return sc.device && sc.key && sc.value;
      } else if (condition.type === 'time') {
        const tc = condition as TimeCondition;
        return tc.start_time && tc.end_time;
      }
      return false;
    });
  }

  // IMPROVED: No dialog for single condition removal - better UX
  removeCondition(groupIndex: number, conditionIndex: number): void {
    this.rule.conditions.groups[groupIndex].conditions.splice(conditionIndex, 1);
    this.updateTopicPattern();
  }

  // === DRAG & DROP METHODS ===

  onGroupDrop(event: CdkDragDrop<ConditionGroup[]>): void {
    if (event.previousContainer === event.container) {
      if (event.previousIndex !== event.currentIndex) {
        moveItemInArray(
          this.rule.conditions.groups,
          event.previousIndex,
          event.currentIndex
        );
        this.updateTopicPattern();
      }
    }
  }

  onConditionDrop(event: CdkDragDrop<Condition[]>, targetGroupIndex: number): void {
    if (event.previousContainer === event.container) {
      if (event.previousIndex !== event.currentIndex) {
        moveItemInArray(
          this.rule.conditions.groups[targetGroupIndex].conditions,
          event.previousIndex,
          event.currentIndex
        );
        this.updateTopicPattern();
      }
    } else {
      const sourceGroupIndex = this.findGroupIndexByContainer(event.previousContainer.data);
      if (sourceGroupIndex !== -1 && sourceGroupIndex !== targetGroupIndex) {
        transferArrayItem(
          this.rule.conditions.groups[sourceGroupIndex].conditions,
          this.rule.conditions.groups[targetGroupIndex].conditions,
          event.previousIndex,
          event.currentIndex
        );
        this.updateTopicPattern();
      }
    }
  }

  onActionDrop(event: CdkDragDrop<PublishAction[]>): void {
    if (event.previousContainer === event.container) {
      if (event.previousIndex !== event.currentIndex) {
        moveItemInArray(
          this.rule.actions,
          event.previousIndex,
          event.currentIndex
        );
        this.updateTopicPattern();
      }
    }
  }

  private findGroupIndexByContainer(containerData: Condition[]): number {
    for (let i = 0; i < this.rule.conditions.groups.length; i++) {
      if (this.rule.conditions.groups[i].conditions === containerData) {
        return i;
      }
    }
    return -1;
  }

  // === TRACKING METHODS ===

  trackByGroupIndex(index: number, group: ConditionGroup): string {
    const conditionCount = group.conditions.length;
    const hasTimeCondition = group.conditions.some(c => c.type === 'time');
    const hasSensorCondition = group.conditions.some(c => c.type === 'payload');
    
    return `group-${index}-${group.operator}-${conditionCount}-${hasTimeCondition ? 'time' : ''}-${hasSensorCondition ? 'sensor' : ''}`;
  }

  trackByConditionIndex(index: number, condition: Condition): string {
    if (condition.type === 'payload') {
      const sensorCondition = condition as SensorDataCondition;
      return `condition-${index}-${sensorCondition.type}-${sensorCondition.device || 'no-device'}-${sensorCondition.key || 'no-key'}`;
    } else if (condition.type === 'time') {
      return `condition-${index}-time`;
    }
    return `condition-${index}-unknown`;
  }

  trackByActionIndex(index: number, action: PublishAction): string {
    if (action.type === 'log') {
      return `action-${index}-log-${action.message || 'no-message'}`;
    } else {
      const payloadKeys = action.payload ? Object.keys(action.payload).join('-') : 'no-payload';
      return `action-${index}-${action.type || 'no-type'}-${action.topic || 'no-topic'}-${payloadKeys}`;
    }
  }

  trackBySuggestion(index: number, suggestion: string): string {
    return suggestion;
  }

  // === ACTION MANAGEMENT (IMPROVED UX) ===

  addAction(): void {
    this.rule.actions.push({
      type: '',
      payload: {},
      topic: ''
    } as PublishAction);
    this.updateTopicPattern();
  }

  addLogAction(): void {
    this.rule.actions.push({
      type: 'log',
      topic: '',
      payload: {},
      message: ''
    } as PublishAction);
    this.updateTopicPattern();
  }

  onActionDeviceChange(action: PublishAction, selectedTopic: string): void {
    let deviceNameKey: string | undefined;
    for (const key in this.deviceTypes.actuators) {
      if (this.deviceTypes.actuators[key].topic === selectedTopic) {
        deviceNameKey = key;
        break;
      }
    }

    if (deviceNameKey && selectedTopic) {
      const selectedDevice = this.deviceTypes.actuators[deviceNameKey];
      if (selectedDevice) {
        action.topic = selectedDevice.topic;
        action.type = '';
        action.payload = {};
        this.updateTopicPattern();
      }
    } else {
      action.topic = '';
      action.type = '';
      action.payload = {};
      this.updateTopicPattern();
    }
  }

  onActionTypeChange(action: PublishAction, selectedActionType: string): void {
    action.type = selectedActionType;
    
    if (selectedActionType && selectedActionType !== 'log') {
      action.payload = action.payload || {};
      if (!(selectedActionType in action.payload)) {
        action.payload[selectedActionType] = '';
      }
    } else if (selectedActionType === 'log') {
      action.payload = {};
      if (!action.message) {
        action.message = '';
      }
    }
    
    this.updateTopicPattern();
  }

  onActionPayloadChange(action: PublishAction): void {
    this.updateTopicPattern();
  }

  onActionMessageChange(action: PublishAction): void {
    this.updateTopicPattern();
  }

  // IMPROVED: No dialog for action removal - better UX
  removeAction(index: number): void {
    this.rule.actions.splice(index, 1);
    this.updateTopicPattern();
  }

  // === DIALOG MANAGEMENT (IMPROVED UX) ===

  onCancel(): void {
    // Always show confirmation for forced cancel during saving (data protection)
    if (this.isSaving) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: 'Sauvegarde en cours',
          message: 'Une sauvegarde est en cours. Voulez-vous vraiment annuler ? Cela pourrait corrompre les données.',
          icon: 'warning'
        },
        disableClose: true,
        panelClass: 'confirmation-dialog-panel'
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.forceCancel();
        }
      });
      return;
    }

    // No dialog for AI generation cancellation - just cancel it
    if (this.isGeneratingSummary) {
      this.dialogRef.close();
      return;
    }

    // Only show confirmation for truly significant changes
    const hasSignificantChanges = this.hasSignificantUnsavedChanges();
    
    if (hasSignificantChanges) {
      const dialogConfig = this.getCancelConfirmationDialog();
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, dialogConfig);

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.dialogRef.close();
        }
      });
    } else {
      // Close directly for minor or no changes
      this.dialogRef.close();
    }
  }

  /**
   * Enhanced logic to determine when to show cancel confirmation
   */
  private shouldShowCancelConfirmation(): boolean {
    // For new rules, only show confirmation if there's meaningful content
    if (!this.data.rule) {
      return this.hasMeaningfulNewRuleContent();
    }
    
    // For existing rules, only show confirmation for significant changes
    return this.hasSignificantUnsavedChanges();
  }

  /**
   * Check if a new rule has meaningful content worth confirming
   */
  private hasMeaningfulNewRuleContent(): boolean {
    // Has a rule name
    const hasName = this.rule.rule_name.trim() !== '';
    
    // Has at least one meaningful condition
    const hasConditions = this.rule.conditions.groups.some(group => 
      group.conditions.some(condition => {
        if (condition.type === 'payload') {
          const sc = condition as SensorDataCondition;
          return sc.device && sc.key && sc.value;
        } else if (condition.type === 'time') {
          const tc = condition as TimeCondition;
          return tc.start_time && tc.end_time;
        }
        return false;
      })
    );
    
    // Has at least one meaningful action
    const hasActions = this.rule.actions.some(action => 
      (action.topic && action.type) || 
      (action.type === 'log' && action.message && action.message.trim() !== '')
    );
    
    // Only confirm if there's meaningful content (name + at least one condition or action)
    return hasName && (hasConditions || hasActions);
  }

  /**
   * Get appropriate cancel confirmation dialog configuration
   */
  private getCancelConfirmationDialog(): any {
    if (!this.data.rule) {
      return {
        data: {
          title: 'Annuler la création',
          message: 'Vous avez commencé à créer une règle. Êtes-vous sûr de vouloir annuler ?',
          icon: 'warning'
        },
        disableClose: true,
        panelClass: 'confirmation-dialog-panel'
      };
    }
    
    return {
      data: {
        title: 'Annuler les modifications',
        message: 'Vous avez des modifications importantes non sauvegardées. Êtes-vous sûr de vouloir fermer ?',
        icon: 'warning'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    };
  }

  private forceCancel(): void {
    this.isSaving = false;
    this.isGeneratingSummary = false;
    this.dialogRef.close({ action: 'cancelled', forced: true });
  }

  // === SAVE AND SUMMARY GENERATION (ENHANCED UX) ===

  onSave(): void {
    if (!this.rule.rule_name.trim() || this.isBusy) {
      return;
    }

    const cleanRule = this.generateCleanRule();
    const hasValidConditions = cleanRule.conditions.groups.some(group => group.conditions.length > 0);
    const hasValidActions = cleanRule.actions.length > 0;

    // Only show confirmation for problematic cases
    const shouldShowConfirmation = this.shouldShowSaveConfirmation(cleanRule, hasValidConditions, hasValidActions);
    
    if (shouldShowConfirmation) {
      const dialogConfig = this.getSaveConfirmationDialog(cleanRule, hasValidConditions, hasValidActions);
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, dialogConfig);

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.performSaveWithSummary();
        }
      });
    } else {
      // Direct save for most cases
      this.performSaveWithSummary();
    }
  }

  /**
   * Simplified logic to determine when to show save confirmation
   */
  private shouldShowSaveConfirmation(cleanRule: RuleDto, hasValidConditions: boolean, hasValidActions: boolean): boolean {
    // Only show confirmation for empty rules (no conditions AND no actions)
    if (!hasValidConditions && !hasValidActions) {
      return true;
    }
    
    // For new rules, only show confirmation if they're completely empty
    const isEdit = this.data.rule && this.data.rule.id != null;
    if (!isEdit) {
      return !hasValidConditions && !hasValidActions;
    }
    
    // For existing rules, only show confirmation for critical changes
    const hasCriticalChanges = this.hasCriticalChanges(cleanRule);
    return hasCriticalChanges;
  }

  /**
   * Check for critical changes that warrant confirmation
   */
  private hasCriticalChanges(cleanRule: RuleDto): boolean {
    if (!this.data.rule) {
      return false;
    }

    const originalRule = this.data.rule;
    
    // Only confirm for critical changes
    const criticalChanges = [
      // Rule name changes
      this.rule.rule_name !== originalRule.rule_name,
      
      // Priority changes
      this.rule.priority !== originalRule.priority,
      
      // Enabled/disabled status changes
      this.rule.enabled !== originalRule.enabled,
      
      // Major structural changes
      this.rule.conditions.operator !== originalRule.conditions.operator,
      
      // Significant condition changes (not just value tweaks)
      this.hasSignificantConditionChanges(originalRule),
      
      // Action type or target changes
      this.hasSignificantActionChanges(originalRule)
    ];
    
    return criticalChanges.some(change => change);
  }

  /**
   * Check for significant condition changes (not just value adjustments)
   */
  private hasSignificantConditionChanges(originalRule: any): boolean {
    const currentGroups = this.rule.conditions.groups;
    const originalGroups = originalRule.conditions.groups;
    
    // Different number of groups
    if (currentGroups.length !== originalGroups.length) {
      return true;
    }
    
    // Check each group for structural changes
    for (let i = 0; i < currentGroups.length; i++) {
      const currentGroup = currentGroups[i];
      const originalGroup = originalGroups[i];
      
      // Group operator changed
      if (currentGroup.operator !== originalGroup.operator) {
        return true;
      }
      
      // Different number of conditions in group
      if (currentGroup.conditions.length !== originalGroup.conditions.length) {
        return true;
      }
      
      // Check for device/property changes (not just values)
      for (let j = 0; j < currentGroup.conditions.length; j++) {
        const currentCondition = currentGroup.conditions[j];
        const originalCondition = originalGroup.conditions[j];
        
        if (currentCondition.type !== originalCondition.type) {
          return true;
        }
        
        if (currentCondition.type === 'payload') {
          // Device or property changed (structural change)
          if (currentCondition.device !== originalCondition.device || 
              currentCondition.key !== originalCondition.key) {
            return true;
          }
        }
      }
    }
    
    return false;
  }

  /**
   * Check for significant action changes
   */
  private hasSignificantActionChanges(originalRule: any): boolean {
    const currentActions = this.rule.actions;
    const originalActions = originalRule.actions;
    
    // Different number of actions
    if (currentActions.length !== originalActions.length) {
      return true;
    }
    
    // Check for action type or target changes
    for (let i = 0; i < currentActions.length; i++) {
      const currentAction = currentActions[i];
      const originalAction = originalActions[i];
      
      // Action type changed
      if (currentAction.type !== originalAction.type) {
        return true;
      }
      
      // Target device changed
      if (currentAction.topic !== originalAction.topic) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * Get appropriate confirmation dialog configuration
   */
  private getSaveConfirmationDialog(cleanRule: RuleDto, hasValidConditions: boolean, hasValidActions: boolean): any {
    const isEdit = this.data.rule && this.data.rule.id != null;
    
    if (!hasValidConditions && !hasValidActions) {
      return {
        data: {
          title: 'Règle vide',
          message: 'Cette règle est vide (aucune condition ni action). Voulez-vous la sauvegarder quand même ?',
          icon: 'warning'
        },
        disableClose: true,
        panelClass: 'confirmation-dialog-panel'
      };
    }
    
    // For critical changes to existing rules
    return {
      data: {
        title: 'Modifications importantes',
        message: `Vous avez apporté des modifications importantes à "${this.rule.rule_name}". Confirmer la mise à jour ?`,
        icon: 'edit'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    };
  }

  private performSaveWithSummary(): void {
    this.isSaving = true;
    this.isGeneratingSummary = false;

    const cleanRule = this.generateCleanRule();
    
    this.isGeneratingSummary = true;
    this.rulesService.summarizeRule(cleanRule).subscribe({
      next: (summaryResponse: any) => {
        this.isGeneratingSummary = false;
        
        let summaryText: string;
        if (summaryResponse && typeof summaryResponse === 'object' && summaryResponse.summary) {
          summaryText = summaryResponse.summary;
        } else {
          summaryText = String(summaryResponse);
        }
        
        this.saveRuleWithSummary(cleanRule, summaryText);
      },
      error: (error) => {
        this.isGeneratingSummary = false;
        console.error('Error generating summary:', error);
        this.saveRuleWithSummary(cleanRule, null);
      }
    });
  }

  private saveRuleWithSummary(cleanRule: RuleDto, summary: string | null): void {
    if (!this.isSaving) {
      console.warn('Save operation was cancelled or interrupted');
      return;
    }

    const ruleToSave: { RawData: string; summary?: string; id?: string; Enabled: boolean; Priority: number } = {
      RawData: JSON.stringify(cleanRule), 
      Enabled: cleanRule.enabled,
      Priority: cleanRule.priority,
    };

    if (summary) {
      ruleToSave.summary = summary;
    }

    // Check if this is an edit operation by looking for the rule ID
    const isEdit = this.data.rule && this.data.rule.id != null;
    
    if (isEdit && this.data.rule) {
      ruleToSave.id = this.data.rule.id;
      this.rulesService.update(ruleToSave).subscribe({
        next: (savedRule) => {
          this.handleSaveSuccess(savedRule, cleanRule, summary, 'updated');
        },
        error: (error) => {
          this.handleSaveError(error, 'Erreur lors de la mise à jour de la règle. Veuillez réessayer.');
        }
      });
    } else {
      this.rulesService.create(ruleToSave).subscribe({
        next: (savedRule) => {
          this.handleSaveSuccess(savedRule, cleanRule, summary, 'created');
        },
        error: (error) => {
          this.handleSaveError(error, 'Erreur lors de la création de la règle. Veuillez réessayer.');
        }
      });
    }
  }

  private handleSaveSuccess(savedRule: any, cleanRule: RuleDto, summary: string | null, action: 'created' | 'updated'): void {
    this.isSaving = false;
    
    const actionText = action === 'created' ? 'créée' : 'mise à jour';
    console.log(`Rule ${actionText} successfully:`, savedRule);
    
    this.dialogRef.close({ 
      action: 'saved', 
      rule: {
        ...savedRule,
        ...cleanRule,
        summary: summary
      },
      operation: action
    });
  }

  private handleSaveError(error: any, message: string): void {
    this.isSaving = false;
    this.isGeneratingSummary = false;
    
    console.error('Save error:', error);
    
    let errorMessage = message;
    if (error?.error?.message) {
      errorMessage += `\n\nDétails: ${error.error.message}`;
    } else if (error?.message) {
      errorMessage += `\n\nDétails: ${error.message}`;
    }
    
    this.showErrorDialog(errorMessage);
  }

  // IMPROVED: More intelligent detection of significant changes
  private hasSignificantUnsavedChanges(): boolean {
    if (!this.data.rule) {
      // For new rules, consider significant if there's a name AND at least one condition or action
      return this.rule.rule_name.trim() !== '' && (
        this.rule.conditions.groups.some(group => 
          group.conditions.some(condition => {
            if (condition.type === 'payload') {
              const sc = condition as SensorDataCondition;
              return sc.device && sc.key && sc.value;
            } else if (condition.type === 'time') {
              const tc = condition as TimeCondition;
              return tc.start_time && tc.end_time;
            }
            return false;
          })
        ) ||
        this.rule.actions.some(action => 
          (action.topic && action.type) || 
          (action.type === 'log' && action.message && action.message.trim() !== '')
        )
      );
    }
    
    // For existing rules, compare with original
    const originalRule = this.data.rule;
    return JSON.stringify(this.rule) !== JSON.stringify(originalRule);
  }

  private showErrorDialog(message: string, showRetry: boolean = false): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Erreur',
        message: message,
        icon: 'error',
        showCancel: showRetry,
        confirmText: showRetry ? 'Réessayer' : 'OK',
        cancelText: 'Fermer'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    });

    if (showRetry) {
      dialogRef.afterClosed().subscribe(result => {
        if (result && this.loadingError) {
          this.retryLoadData();
        }
      });
    }
  }

  // === UTILITY METHODS ===

  getSensorDeviceNames(): string[] {
    return Object.keys(this.deviceTypes.sensors);
  }

  getActuatorDeviceNames(): string[] {
    return Object.keys(this.deviceTypes.actuators);
  }

  getPropertiesForDevice(deviceType: 'sensors' | 'actuators', deviceName: string): DeviceProperty[] {
    if (!deviceName) return [];
    const device = this.deviceTypes[deviceType][deviceName];
    return device ? device.properties || [] : [];
  }

  getActionsForDevice(deviceType: 'sensors' | 'actuators', deviceName: string): DeviceAction[] {
    if (!deviceName) return [];
    const device = this.deviceTypes[deviceType][deviceName];
    return device ? device.actions || [] : [];
  }

  getDeviceNameKeyByTopic(topic: string, deviceType: 'sensors' | 'actuators'): string | undefined {
    if (!topic) return undefined;
    for (const key in this.deviceTypes[deviceType]) {
      if (this.deviceTypes[deviceType][key].topic === topic) {
        return key;
      }
    }
    return undefined;
  }

  getSelectedProperty(condition: SensorDataCondition): DeviceProperty | undefined {
    if (!condition.device || !condition.key) {
      return undefined;
    }
    const device = this.deviceTypes.sensors[condition.device];
    if (device && device.properties) {
      return device.properties.find(p => p.key === condition.key);
    }
    return undefined;
  }

  getSelectedActionType(action: PublishAction): DeviceAction | undefined {
    if (!action.topic || !action.type) {
      return undefined;
    }
    const deviceNameKey = this.getDeviceNameKeyByTopic(action.topic, 'actuators');
    if (deviceNameKey) {
      const selectedActuator = this.deviceTypes.actuators[deviceNameKey];
      if (selectedActuator && selectedActuator.actions) {
        return selectedActuator.actions.find(a => a.type === action.type);
      }
    }
    return undefined;
  }

  capitalizeFirst(str: string): string {
    if (!str) return str;
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  retryLoadData(): void {
    if (this.isBusy) {
      console.warn('Cannot retry while another operation is in progress');
      return;
    }
    
    this.loadingError = null;
    this.loadBackendData();
  }

  getDataListId(groupIndex: number, conditionIndex: number): string {
    return `suggestions-${groupIndex}-${conditionIndex}`;
  }

  // === JSON GENERATION AND DOWNLOAD ===

  generateJSON(): string {
    try {
      const ruleToExport = this.generateCleanRule(); 
      return JSON.stringify(ruleToExport, null, 2);
    } catch (error) {
      console.error('Error generating JSON:', error);
      return '{\n  "error": "Unable to generate JSON preview"\n}';
    }
  }

  downloadJSON(): void {
    if (this.isBusy) {
      this.showErrorDialog('Impossible de télécharger pendant une opération de sauvegarde. Veuillez patienter.');
      return;
    }
    
    try {
      const filename = `${this.rule.rule_name || 'rule'}.json`;
      const json = this.generateJSON();
      const blob = new Blob([json], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading JSON:', error);
      this.showErrorDialog('Erreur lors du téléchargement du fichier JSON.');
    }
  }

  private generateCleanRule(): RuleDto {
    const cleanRule = JSON.parse(JSON.stringify(this.rule));

    // Remove empty payload properties from actions
    cleanRule.actions.forEach((action: PublishAction) => {
      if (action.payload && typeof action.payload === 'object') {
        for (const key in action.payload) {
          if (action.payload.hasOwnProperty(key) && 
              (action.payload[key] === null || action.payload[key] === undefined || action.payload[key] === '')) {
            delete action.payload[key];
          }
        }
      }
    });

    // Remove actions that are completely empty
    cleanRule.actions = cleanRule.actions.filter((action: PublishAction) => {
      const hasMessage = action.type === 'log' && action.message && action.message.trim() !== '';
      const hasTopicAndType = action.type !== 'log' && action.topic && action.type;
      const hasPayloadData = action.payload && Object.keys(action.payload).length > 0;
      
      return hasMessage || (hasTopicAndType && hasPayloadData);
    });

    return cleanRule;
  }

  updateTopicPattern(): void {
    const topics: string[] = [];

    this.rule.conditions.groups.forEach(group => {
      group.conditions.forEach(condition => {
        if (condition.type === 'payload') {
          const sensorCondition = condition as SensorDataCondition;
          if (sensorCondition.device) {
            const device = this.deviceTypes.sensors[sensorCondition.device];
            if (device && device.topic && !topics.includes(device.topic)) {
              topics.push(device.topic);
            }
          }
        }
      });
    });

    this.rule.topic_pattern = topics;
  }

  // === GETTER METHODS FOR UI STATE ===

  get isBusy(): boolean {
    return this.isSaving || this.isGeneratingSummary;
  }

  get statusMessage(): string {
    if (this.isGeneratingSummary && this.isSaving) {
      return 'Sauvegarde terminée, génération du résumé IA...';
    }
    if (this.isGeneratingSummary) {
      return 'Génération du résumé IA en cours...';
    }
    if (this.isSaving) {
      return 'Sauvegarde de la règle en cours...';
    }
    return '';
  }

  get operationProgress(): number {
    if (this.isSaving && !this.isGeneratingSummary) {
      return 50;
    }
    if (this.isGeneratingSummary) {
      return 80;
    }
    if (!this.isSaving && !this.isGeneratingSummary) {
      return 100;
    }
    return 0;
  }
}

export type Condition = SensorDataCondition | TimeCondition;