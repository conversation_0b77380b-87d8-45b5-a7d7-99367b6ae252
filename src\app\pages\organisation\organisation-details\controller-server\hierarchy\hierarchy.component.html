<div class="hierarchy-container">
  <!-- Debug Info (remove in production) -->
  <!-- <div class="debug-info" style="background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 4px;">
    <h4>Debug Information:</h4>
    <p>Hierarchy Data Length: {{ hierarchyData?.length || 0 }}</p>
    <p>Is Loading: {{ isLoading }}</p>
    <p>Error: {{ error || 'None' }}</p>
    <p>Server Count: {{ serverCount }}</p>
    <p>Controller Count: {{ controllerCount }}</p>
    <p>Connection Count: {{ connectionCount }}</p>
    <p>Active Controllers: {{ activeControllerCount }}</p>
  </div> -->

  <!-- Header with Controls -->
  <!-- <div class="hierarchy-header">
    <div class="header-content">
      <div class="header-title">
        <mat-icon>account_tree</mat-icon>
        <h3>Hiérarchie des Contrôleurs Serveurs</h3>
      </div>
      <div class="header-controls">
        <button mat-stroked-button (click)="resetView()" [disabled]="isLoading">
          <mat-icon>home</mat-icon>
          Réinitialiser
        </button>
        <button mat-stroked-button (click)="toggleLayout()" [disabled]="isLoading">
          <mat-icon>{{ layoutType === 'force' ? 'account_tree' : 'scatter_plot' }}</mat-icon>
          {{ layoutType === 'force' ? 'Vue Arbre' : 'Vue Force' }}
        </button>
        <button mat-stroked-button (click)="exportSVG()" [disabled]="isLoading">
          <mat-icon>download</mat-icon>
          Exporter
        </button>
        <button mat-stroked-button (click)="loadHierarchyData()" [disabled]="isLoading">
          <mat-icon>refresh</mat-icon>
          Recharger
        </button>
        <button mat-stroked-button (click)="forceReload()" [disabled]="isLoading">
          <mat-icon>build</mat-icon>
          Force Reload
        </button>
      </div>
    </div>
  </div> -->

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="loading-content">
      <div class="spinner"></div>
      <h4>Chargement de la hiérarchie...</h4>
      <p>Analyse des relations entre contrôleurs serveurs et contrôleurs</p>
    </div>
  </div>

  <!-- Error State -->
  <div class="error-container" *ngIf="error && !isLoading">
    <div class="error-content">
      <mat-icon class="error-icon">error_outline</mat-icon>
      <h4>Erreur de chargement</h4>
      <p>{{ error }}</p>
      <button mat-raised-button color="primary" (click)="loadHierarchyData()">
        <mat-icon>refresh</mat-icon>
        Réessayer
      </button>
    </div>
  </div>

  <!-- Statistics Panel -->
  <div
    class="stats-panel"
    *ngIf="!isLoading && !error && hierarchyData.length > 0"
  >
    <div class="stat-card">
      <div class="stat-value">{{ serverCount }}</div>
      <div class="stat-label">Contrôleurs Serveurs</div>
      <mat-icon class="stat-icon">dns</mat-icon>
    </div>
    <div class="stat-card">
      <div class="stat-value">{{ controllerCount }}</div>
      <div class="stat-label">Contrôleurs</div>
      <mat-icon class="stat-icon">memory</mat-icon>
    </div>
    <div class="stat-card">
      <div class="stat-value">{{ connectionCount }}</div>
      <div class="stat-label">Connexions</div>
      <mat-icon class="stat-icon">link</mat-icon>
    </div>
    <div class="stat-card">
      <div class="stat-value">{{ activeControllerCount }}</div>
      <div class="stat-label">Actifs</div>
      <mat-icon class="stat-icon">check_circle</mat-icon>
    </div>
  </div>

  <!-- Visualization Container -->
  <div
    class="visualization-container"
    *ngIf="!isLoading && !error"
  >
    <div class="svg-container">
      <svg #svg class="hierarchy-svg"></svg>
    </div>

    <!-- Tooltip -->
    <div class="tooltip" #tooltip>
      <div class="tooltip-content">
        <div class="tooltip-title"></div>
        <div class="tooltip-details"></div>
      </div>
    </div>
  </div>

  <!-- Empty State for No Controllers -->
  <div
    class="empty-state"
    *ngIf="
      !isLoading && !error && hierarchyData.length > 0 && connectionCount === 0
    "
  >
    <div class="empty-content">
      <mat-icon class="empty-icon">account_tree</mat-icon>
      <h4>Serveurs sans contrôleurs</h4>
      <p>
        {{ serverCount }} contrôleur(s) serveur(s) trouvé(s), mais aucun
        contrôleur n'y est associé.
      </p>
      <p class="suggestion">
        Les serveurs sont affichés ci-dessus. Ajoutez des contrôleurs pour voir
        les relations.
      </p>
    </div>
  </div>

  <!-- Empty State for No Data -->
  <div
    class="empty-state"
    *ngIf="!isLoading && !error && hierarchyData.length === 0"
  >
    <div class="empty-content">
      <mat-icon class="empty-icon">account_tree</mat-icon>
      <h4>Aucune donnée de hiérarchie</h4>
      <p>Aucun contrôleur serveur ou relation trouvé à afficher.</p>
      <button mat-raised-button color="primary" (click)="loadHierarchyData()">
        <mat-icon>refresh</mat-icon>
        Actualiser les données
      </button>
    </div>
  </div>

  <!-- Legend -->
  <div class="legend" *ngIf="!isLoading && !error && hierarchyData.length > 0">
    <div class="legend-title">Légende</div>
    <div class="legend-items">
      <div class="legend-item">
        <div class="legend-color server-node"></div>
        <span>Contrôleur Serveur</span>
      </div>
      <div class="legend-item">
        <div class="legend-color controller-node active"></div>
        <span>Contrôleur Actif</span>
      </div>
      <div class="legend-item">
        <div class="legend-color controller-node inactive"></div>
        <span>Contrôleur Inactif</span>
      </div>
      <div class="legend-item">
        <div class="legend-color connection-line"></div>
        <span>Connexion</span>
      </div>
    </div>
  </div>
</div>