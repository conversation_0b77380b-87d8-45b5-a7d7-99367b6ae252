import { Component, Input } from '@angular/core';
import { Client } from '@app/core/models/client';
import { Licence } from '@app/core/models/licence';

@Component({
  selector: 'app-list',
  imports: [],
  templateUrl: './list.component.html',
  styleUrl: './list.component.css'
})
export class ListComponent {
  @Input() client: Client | null = null;
  @Input() licences: Licence[] = [];

  licenceHeaders: string[] = [
    'Nom',
    'Description',
    'Date Debut',
    'Date Fin',
    'Statut',
  ];
  licenceKeys: string[] = ['Name', 'Description', 'DateDebut', 'DateFin', 'Status'];
}
