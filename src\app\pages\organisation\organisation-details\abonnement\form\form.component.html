<div class="site-management-container">
  <form
    #subscriptionForm="ngForm"
    class="create-form-card"
    (ngSubmit)="submit()"
    autocomplete="off"
  >
    <div class="form-grid">
      <!-- Date début -->
      <div class="form-group">
        <label for="dateDebut">Date début:</label>
        <input
          type="date"
          [(ngModel)]="subscription.DateDebut"
          name="dateDebut"
          id="dateDebut"
          required
        />
      </div>

      <!-- Date fin -->
      <div class="form-group">
        <label for="dateFin">Date fin:</label>
        <input
          type="date"
          [(ngModel)]="subscription.DateFin"
          name="dateFin"
          id="dateFin"
          required
        />
      </div>

      <!-- Status -->
      <div class="form-group">
        <label for="status">Status:</label>
        <input
          type="text"
          [(ngModel)]="subscription.Status"
          name="status"
          id="status"
          required
        />
      </div>

      <!-- Client ID -->
      <div class="form-group">
        <label for="clientId">Client:</label>
        <select
          [(ngModel)]="subscription.ClientId"
          name="clientId"
          id="clientId"
          required
        >
          <option value="" disabled selected>Select client</option>
          <option *ngFor="let client of clients" [value]="client.Id">
            {{ client.Name }}
          </option>
        </select>
      </div>

      <!-- Licence -->
      <div class="form-group">
        <label for="licenceId">Licence:</label>
        <select
          [(ngModel)]="subscription.LicenceId"
          name="licenceId"
          id="licenceId"
          required
        >
          <option value="" disabled selected>Select licence</option>
          <option
            *ngFor="let licence of licences"
            [value]="licence.Id"
          >
            {{ licence.Name || licence.Name }}
          </option>
        </select>
      </div>

      <!-- Price -->
      <div class="form-group">
        <label for="price">Price:</label>
        <input
          type="number"
          [(ngModel)]="subscription.Price"
          name="price"
          id="price"
          min="0"
          step="0.01"
          required
        />
      </div>

      <!-- Payment Frequency -->
      <div class="form-group">
        <label for="paymentFrequency">Payment Frequency:</label>
        <select
          [(ngModel)]="subscription.PaymentFrequency"
          name="paymentFrequency"
          id="paymentFrequency"
          required
        >
          <option value="" disabled selected>Select frequency</option>
          <option value="monthly">Monthly</option>
          <option value="quarterly">Quarterly</option>
          <option value="yearly">Yearly</option>
        </select>
      </div>
    </div>

    <div class="form-actions">
      <button type="submit" [disabled]="isSubmitting">Submit</button>
    </div>

    <!-- Inside app-detail/app-form -->
    <button (click)="close()">Close</button>
  </form>
</div>
