import { Component, ElementRef, ViewChild } from '@angular/core';
import { StatsComponent } from "./stats/stats.component";
import { ClientsComponent } from "./clients/clients.component";
import { Organisation } from '@app/core/models/organisation';
import { TopPartComponent } from './top-part/top-part.component';

@Component({
  selector: 'app-accueil',
  imports: [TopPartComponent, StatsComponent, ClientsComponent],
  templateUrl: './accueil.component.html',
  styleUrl: './accueil.component.css'
})
export class AccueilComponent {
  selectedOrganisationId: string | null = null;

  @ViewChild('clientsSection') clientsSection!: ElementRef;

  onOrganisationSelected(org: Organisation) {
    this.selectedOrganisationId = org.Id;
    // Scroll vers la section clients après un court délai pour s'assurer du rendu
    setTimeout(() => {
      if (this.clientsSection) {
        this.clientsSection.nativeElement.scrollIntoView({ behavior: 'smooth' });
      }
    }, 100);
  }
}
