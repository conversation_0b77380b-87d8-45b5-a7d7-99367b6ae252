import { Observable } from 'rxjs';

export interface IApiService<T> {

  getAll(endpoint?: string): Observable<T[]>;

  getOne(data: string): Observable<T>;

  getById(id: string): Observable<T>;

  post(endpoint: string, item: T): Observable<T>;

  put(endpoint: string, item: T): Observable<T>;

  create(item: T): Observable<T>;

  update(id: string, formData: FormData): Observable<T>;

  delete(id: string): Observable<{ success: boolean }>; 

  count(): Observable<number>;
}
