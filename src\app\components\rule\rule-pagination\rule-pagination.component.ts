import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-rule-pagination',
  templateUrl: `./rule-pagination.component.html`,
  styleUrls: [`./rule-pagination.component.css`],
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule
  ]
})
export class RulePaginationComponent {
  @Input() currentPage: number = 1;
  @Input() pageCount: number = 0;
  @Input() totalElements: number = 0;
  @Input() searchTerm: string = '';
  @Input() appliedFiltersCount: number = 0;
  @Input() hasPendingOperations: boolean = false;
  @Input() isLoading: boolean | null = false;

  @Output() previousPage = new EventEmitter<void>();
  @Output() nextPage = new EventEmitter<void>();
  @Output() goToPage = new EventEmitter<number>();

  getPages(): number[] {
    const pages: number[] = [];
    const maxPagesToShow = 5; // e.g., show 5 pages at a time
    const halfMax = Math.floor(maxPagesToShow / 2);

    let startPage = Math.max(1, this.currentPage - halfMax);
    let endPage = Math.min(this.pageCount, this.currentPage + halfMax);

    // Adjust start/end if near boundaries
    if (endPage - startPage + 1 < maxPagesToShow) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }
    if (endPage - startPage + 1 < maxPagesToShow) {
      endPage = Math.min(this.pageCount, startPage + maxPagesToShow - 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    return pages;
  }
}