<div class="login-container">
  <div *ngIf="error" class="alert alert-danger">{{ error }}</div>
  <div class="login-header"><h1>Connexion</h1></div>
  <form [formGroup]="form" (ngSubmit)="onSubmit()">
    <div class="form-group">
      <label for="username">Adresse email</label>
      <input
        type="email"
        id="username"
        formControlName="username"
        class="form-control"
        [ngClass]="{ 'is-invalid': submitted && f['username'].errors }"
      />
      <div *ngIf="submitted && f['username'].errors" class="invalid-feedback">
        <div *ngIf="f['username'].errors?.['required']">L'email est requis</div>
        <div *ngIf="f['username'].errors?.['email']">
          Format d'email invalide
        </div>
      </div>
    </div>
    <div class="form-group">
      <label for="password">Mot de passe</label>
      <input
        type="password"
        id="password"
        formControlName="password"
        class="form-control"
        [ngClass]="{ 'is-invalid': submitted && f['password'].errors }"
      />
      <div *ngIf="submitted && f['password'].errors" class="invalid-feedback">
        <div *ngIf="f['password'].errors?.['required']">
          Le mot de passe est requis
        </div>
      </div>
    </div>
    <button type="submit" class="login-button" [disabled]="loading">
      <span
        *ngIf="loading"
        class="spinner-border spinner-border-sm me-1"
      ></span>
      Se connecter
    </button>
    <a href="#" class="forgot-password">Mot de passe oublié ?</a>
  </form>
</div>
