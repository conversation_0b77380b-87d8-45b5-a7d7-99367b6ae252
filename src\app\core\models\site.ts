import { Local } from "./local";
import { AuditModel } from "./models-audit/audit-model";

export class Site extends AuditModel {
  ClientId!: string;
  Email!: string;
  Description!: string;
  Image?: string;
  Contact!: string;
  Manager!: string;
  Name!: string;
  PhoneNumber!: string;
  Status!: string;
  AddressComplement!: string;
  Address!: string;
  EmployeesCount!: number;
  Grade!: string;
  Latitude!: number;
  LocalsCount!: number;
  Longtitude!: number;
  Surface!: number;
  Local?: Local[];
  FloorPlan?: any;
}
