import { Injectable } from "@angular/core";
import { ApiService } from "../api.service";
import { HttpClient } from "@angular/common/http";
import { ControllerServerRule } from "@app/core/models/ControllerServerRule";

@Injectable({ providedIn: 'root' })
export class ControllerServerRuleApiService extends ApiService<ControllerServerRule> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("controller-serveur-rule");
  }
}