.local-management-container {
  width: 95%;
  margin: 30px auto;
  padding: 25px;
  background: linear-gradient(145deg, #ffffff, #f9f9f9);
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

/* Header Section */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  width: 95%;
}

.title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.title-icon {
  font-size: 26px;
  color: var(--primary);
}

.create-button {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.create-button:hover {
  background: linear-gradient(45deg, #81c784, var(--primary)) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.action-icon {
  font-size: 18px;
}

/* Search Bar */
.search-bar {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  align-items: center;
}

.search-bar input {
  flex: 1;
  max-width: 450px;
  padding: 12px 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #f8fafc;
  color: #4a5568;
}

.search-bar input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.search-button {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: white;
  padding: 7px 25px;
  border-radius: 8px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.3s ease;
}

.search-button:hover {
  background: linear-gradient(45deg, #81c784, var(--primary)) !important;
}

/* Table View */
.table-view {
  margin-top: 20px;
}

.pagination-container {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}

/* Beautiful Table Container */
.beautiful-table-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  margin-top: 20px;
  position: relative;
}

.table-header-gradient {
  height: 4px;
  background: linear-gradient(90deg, var(--primary) 0%, #81c784 50%, #4fc3f7 100%);
}

/* Table Header */
.table-header {
  display: grid;
  grid-template-columns: 2fr 3fr 1fr 1.5fr;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 2px solid #e2e8f0;
  padding: 0;
}

.header-cell {
  padding: 20px 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  font-size: 14px;
  color: #2d3748;
  font-family: "Montserrat", sans-serif;
  border-right: 1px solid #e2e8f0;
  position: relative;
}

.header-cell:last-child {
  border-right: none;
}

.header-icon {
  font-size: 20px;
  color: var(--primary);
}

/* Table Body */
.table-body {
  min-height: 200px;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 3fr 1fr 1.5fr;
  border-bottom: 1px solid #f1f5f9;
  transition: all 0.3s ease;
  position: relative;
}

.table-row:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.table-row.even-row {
  background: rgba(248, 250, 252, 0.5);
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 24px;
  display: flex;
  align-items: center;
  border-right: 1px solid #f1f5f9;
  min-height: 80px;
}

.table-cell:last-child {
  border-right: none;
}

/* Tag Name Cell */
.tag-name-cell {
  justify-content: flex-start;
}

.tag-name-content {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
}

.tag-icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--primary), #81c784);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.tag-icon {
  color: white;
  font-size: 24px;
}

.tag-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tag-name {
  font-weight: 600;
  font-size: 16px;
  color: #2d3748;
  font-family: "Montserrat", sans-serif;
}

.tag-id {
  font-size: 12px;
  color: #718096;
  font-family: "Lato", sans-serif;
}

/* Assignments Cell */
.assignments-cell {
  justify-content: center;
}

.assignments-grid {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: center;
  width: 100%;
}

.assignment-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-radius: 12px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  min-width: 80px;
}

.assignment-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.assignment-icon-wrapper {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.client-icon {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.site-icon {
  background: linear-gradient(135deg, #10b981, #34d399);
  color: white;
}

.local-icon {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
  color: white;
}

.assignment-details {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.assignment-label {
  font-size: 11px;
  font-weight: 500;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.assignment-count {
  font-size: 18px;
  font-weight: 700;
  color: #2d3748;
}

/* Total Cell */
.total-cell {
  justify-content: center;
}

.total-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.total-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  position: relative;
}

.total-circle::before {
  content: '';
  position: absolute;
  inset: 2px;
  border-radius: 50%;
  background: white;
  z-index: 1;
}

.total-number {
  font-size: 20px;
  font-weight: 800;
  color: #667eea;
  z-index: 2;
  position: relative;
}

.total-label {
  font-size: 11px;
  font-weight: 500;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
}

/* Actions Cell */
.actions-cell {
  justify-content: center;
}

.actions-wrapper {
  display: flex;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.edit-btn {
  background: linear-gradient(135deg, #ff9800, #ffb74d);
  color: white;
  box-shadow: 0 3px 10px rgba(255, 152, 0, 0.3);
}

.edit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4);
}

.delete-btn {
  background: linear-gradient(135deg, #f44336, #ef5350);
  color: white;
  box-shadow: 0 3px 10px rgba(244, 67, 54, 0.3);
}

.delete-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
}

.action-btn mat-icon {
  font-size: 16px;
}

/* No Data Message */
.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 40px;
  text-align: center;
  color: #718096;
}

.no-data-icon {
  font-size: 64px;
  color: #cbd5e0;
  margin-bottom: 16px;
}

.no-data-message h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #4a5568;
}

.no-data-message p {
  margin: 0;
  font-size: 14px;
  color: #718096;
}

/* Responsive Design for Beautiful Table */
@media (max-width: 1200px) {
  .assignments-grid {
    flex-direction: column;
    gap: 8px;
  }

  .assignment-item {
    min-width: 60px;
  }
}

@media (max-width: 1024px) {
  .table-header,
  .table-row {
    grid-template-columns: 1.5fr 2fr 1fr 1fr;
  }

  .header-cell,
  .table-cell {
    padding: 16px;
  }

  .tag-name-content {
    gap: 12px;
  }

  .tag-icon-wrapper {
    width: 40px;
    height: 40px;
  }

  .tag-icon {
    font-size: 20px;
  }

  .assignments-grid {
    gap: 8px;
  }

  .assignment-item {
    padding: 8px;
    min-width: 60px;
  }

  .assignment-icon-wrapper {
    width: 28px;
    height: 28px;
  }

  .total-circle {
    width: 50px;
    height: 50px;
  }

  .total-number {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .beautiful-table-container {
    border-radius: 12px;
    margin: 10px;
  }

  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .table-row {
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    margin-bottom: 16px;
    padding: 0;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .table-cell {
    border-right: none;
    border-bottom: 1px solid #f1f5f9;
    padding: 16px 20px;
    min-height: auto;
  }

  .table-cell:last-child {
    border-bottom: none;
  }

  .header-cell {
    display: none;
  }

  .table-cell::before {
    content: attr(data-label);
    font-weight: 600;
    color: #4a5568;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-bottom: 8px;
  }

  .assignments-grid {
    justify-content: flex-start;
  }

  .actions-wrapper {
    justify-content: flex-start;
  }
}

/* Loading Spinner */
.loading-spinner {
  text-align: center;
  padding: 40px;
  font-size: 16px;
  color: #666;
}

/* Popup Form Styles */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.popup-form {
  background: white;
  border-radius: 12px;
  padding: 30px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e2e8f0;
}

.popup-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
  display: flex;
  align-items: center;
  gap: 10px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #718096;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f7fafc;
  color: #2d3748;
}

/* Form Styles */
.site-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #2d3748;
  font-size: 14px;
}

.required {
  color: #e53e3e;
}

.form-group input {
  padding: 12px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #f8fafc;
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
  background: white;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.btn-cancel {
  padding: 12px 24px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #4a5568;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-cancel:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.btn-submit {
  padding: 12px 24px;
  background: linear-gradient(45deg, var(--primary), #81c784);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-submit:hover:not(:disabled) {
  background: linear-gradient(45deg, #81c784, var(--primary));
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.btn-submit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Validation Errors */
.validation-errors {
  background: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.validation-errors-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #c53030;
  margin-bottom: 10px;
}

.validation-errors-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.validation-errors-list li {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #c53030;
  font-size: 14px;
  margin-bottom: 5px;
}

.validation-errors-list li:last-child {
  margin-bottom: 0;
}



/* Responsive Design */
@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .popup-form {
    width: 95%;
    padding: 20px;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn-cancel,
  .btn-submit {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 24px;
  }

  .search-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-bar input {
    max-width: none;
  }
}