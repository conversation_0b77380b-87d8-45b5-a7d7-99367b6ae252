<div class="container">
  <!-- Header with title and icon -->
  <div class="header">
    <h1 class="page-title">
      <mat-icon class="title-icon">devices</mat-icon>
      Gestion des Capteurs IoT
    </h1>
  </div>

  <!-- Search field -->
  <mat-form-field appearance="outline" style="width: 100%; margin-bottom: 1rem">
    <mat-label>Rechercher</mat-label>
    <input
      matInput
      (keyup)="applyFilter($event)"
      placeholder="Filtrer les capteurs"
    />
  </mat-form-field>

  <!-- Table container with green header bar -->
  <div class="table-container">
    <div class="table-header-bar"></div>

    <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">
      <!-- Sensor Type Column -->
      <ng-container matColumnDef="sensorType">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Nom</th>
        <td mat-cell *matCellDef="let element" class="sensor-name-cell">
          {{ element.sensorType }}
        </td>
      </ng-container>

      <!-- Value Column -->
      <ng-container matColumnDef="value">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Valeur</th>
        <td mat-cell *matCellDef="let element" class="value-cell">
          {{ element.value !== null ? element.value : "—" }}
        </td>
      </ng-container>

      <!-- Unit Column -->
      <ng-container matColumnDef="unit">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Unité</th>
        <td mat-cell *matCellDef="let element" class="unit-cell">
          {{ element.unit || "—" }}
        </td>
      </ng-container>

      <!-- Battery Column -->
      <ng-container matColumnDef="battery">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Batterie (%)</th>
        <td
          mat-cell
          *matCellDef="let element"
          class="battery-cell"
          [ngClass]="getBatteryClass(element.battery)"
        >
          {{ element.battery !== null ? element.battery + "%" : "—" }}
        </td>
      </ng-container>

      <!-- Timestamp Column -->
      <ng-container matColumnDef="timestamp">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Horodatage</th>
        <td mat-cell *matCellDef="let element" class="timestamp-cell">
          {{ element.timestamp ? (element.timestamp | date : "d/M/y") : "—" }}
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    </table>

    <!-- Paginator -->
    <mat-paginator
      [pageSize]="5"
      [pageSizeOptions]="[5, 10, 25]"
      showFirstLastButtons
    ></mat-paginator>
  </div>
</div>
