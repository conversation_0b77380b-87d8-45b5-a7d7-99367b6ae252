// rule-generation.component.ts (CLEAN VERSION WITH NEW EXECUTION METHODS)
import { Compo<PERSON>, OnInit, OnDestroy, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NgToastService, TOAST_POSITIONS } from 'ng-angular-popup';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';
import { Subject, forkJoin, map } from 'rxjs';

// Import components
import { RuleHeaderComponent } from '../../components/rule/rule-header/rule-header.component';
import { RuleSearchCreateComponent } from '../../components/rule/rule-search-create/rule-search-create.component';
import { RuleFilterSortComponent } from '../../components/rule/rule-filter-sort/rule-filter-sort.component';
import { RuleListComponent } from '../../components/rule/rule-list/rule-list.component';
import { RulePaginationComponent } from '../../components/rule/rule-pagination/rule-pagination.component';

import { RuleFormComponent } from '../../components/rule/rule-form/rule-form.component';
import { ConfirmationDialogComponent } from '../../components/confirmation-dialog/confirmation-dialog.component';
import { AiRuleGeneratorDialogComponent } from '@app/components/rule/ai-rule-generator-dialog/ai-rule-generator-dialog.component';

// Import models
import { RuleContent } from "@app/shared/models/rule/RuleContent";
import { RuleComprehensive } from "@app/shared/models/rule/RuleComprehensive";
import { Lister } from "@app/shared/models/rule/Lister";
import { WhereParams } from "@app/shared/models/rule/WhereParams";
import { Controller } from "@app/core/models/controller";
import { ControllerForRule } from "@app/shared/models/rule/ControllerForRule";
import { RawDataBackendStructure } from "@app/shared/models/rule/RawDataBackendStructure";
import { Action } from "@app/shared/models/rule/Action";
import { Condition } from "@app/shared/models/rule/Condition";

import { RulesApiService, RuleExecutionChartData, RuleExecutionSimple } from '../../core/services/administrative/rules.service';

// NEW: Interface definitions for display data
interface ExecutionChartDisplayData {
  date: string;
  fullDate: string;
  total: number;
  successful: number;
  failed: number;
  successRate: number;
}

interface ExecutionDisplayData {
  timestamp: string;
  formattedTimestamp: string;
  isSuccess: boolean;
  controllerId: string;
  statusText: string;
  statusIcon: string;
}

interface ControllerExecutionSummary {
  total: number;
  successful: number;
  failed: number;
  successRate: number;
  lastExecution: string;
}
import { ControllerApiService } from '@app/core/services/administrative/controller.service';
import { NgToastModule } from 'ng-angular-popup';
import { ClientForRule } from '@app/shared/models/rule/ClientForRule';
import { RuleClientHierarchy } from '@app/shared/models/rule/RuleClientHierarchy';
import { RuleTransactionDetail } from '@app/shared/models/rule/RuleTransactionDetail';
import { PagedResponse } from '@app/shared/models/rule/PagedResponse';

@Component({
  selector: 'app-rule-generation',
  templateUrl: './rule-generation.component.html',
  styleUrls: ['./rule-generation.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    RuleHeaderComponent,
    RuleSearchCreateComponent,
    RuleFilterSortComponent,
    RuleListComponent,
    RulePaginationComponent,
    NgxUiLoaderModule,
    NgToastModule,
  ]
})
export class RuleGenerationComponent implements OnInit, OnDestroy {
  // Core data properties
  rules: RuleContent[] = [];
  filteredRules: RuleContent[] = [];

  // Search and pagination
  searchTerm: string = '';
  currentPage: number = 1;
  pageSize: number = 10;
  totalElements: number = 0;
  pageCount: number = 0;

  // UI state
  activeTab: 'hierarchy' | 'rawdata' | 'summary' = 'summary';
  expandedRuleId: string | null = null;
  selectedControllerId: string | null = null;

  // Loading states
  isDeleting = new Set<string>();
  private isLoading = false;
  isSearching: boolean = false;
  isSearchMode: boolean = false;

  // Search functionality
  private destroy$ = new Subject<void>();

  // Data caches
  private rawDataCache = new Map<string, string>();
  private summaryCache = new Map<string, string>();
  private controllersCache = new Map<string, Controller>();
  
  // NEW: Execution chart data cache
  private executionChartCache = new Map<string, RuleExecutionChartData[]>();
  private executionSimpleCache = new Map<string, PagedResponse<RuleExecutionSimple[]>>();

  // === SORTING AND FILTERING PROPERTIES ===
  showAdvancedFilters: boolean = false;
  currentSortColumn: string = 'Priority';
  currentSortDirection: string = 'ASC';
  appliedFilters: WhereParams[] = [];

  // Available sort options
  sortOptions = [
    { value: 'Priority', label: 'Priorité' },
    { value: 'RuleCreatedAt', label: 'Date de création' },
    { value: 'RuleLastUpdatedAt', label: 'Dernière modification' },
    { value: 'Status', label: 'Statut' },
    { value: 'TotalApplications', label: 'Nombre d\'applications' },
    { value: 'LastTriggered', label: 'Dernier déclenchement' }
  ];

  // Available filter options
  filterOptions: { column: string; label: string; type: 'text' | 'number' | 'date' | 'select'; options?: { value: string; label: string; }[] }[] = [
    { column: 'Status', label: 'Statut', type: 'select', options: [
      { value: 'active', label: 'Actif' },
      { value: 'inactive', label: 'Inactif' }
    ]},
    { column: 'Priority', label: 'Priorité', type: 'number' },
    { column: 'RuleCreatedAt', label: 'Date de création', type: 'date' },
    { column: 'TotalApplications', label: 'Nombre d\'applications', type: 'number' },
    { column: 'RawData', label: 'Contenu de la règle', type: 'text' },
    { column: 'TagsString', label: 'Tags', type: 'text' }
  ];

  TOAST_POSITIONS = TOAST_POSITIONS;

  @ViewChild(RuleListComponent) ruleListComponent!: RuleListComponent;

  constructor(
    private dialog: MatDialog,
    private rulesApiService: RulesApiService,
    private controllerApiService: ControllerApiService,
    private toast: NgToastService,
    private loader: NgxUiLoaderService,
  ) {}

  ngOnInit(): void {
    this.currentPage = 1;
    this.loadRules();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  hasPendingOperations(): boolean {
    return this.isDeleting.size > 0 || this.isLoading || this.isSearching;
  }

  getLoadingState(): boolean {
    return this.isLoading;
  }

  // === SEARCH FUNCTIONALITY ===
  onSearchSubmit(): void {
    console.log('🚀 Main component: onSearchSubmit called with searchTerm:', this.searchTerm);
    this.currentPage = 1;
    this.performSearch(this.searchTerm);
  }

  onSearchClear(): void {
    console.log('🧹 Main component: onSearchClear called');
    this.searchTerm = '';
    this.currentPage = 1;
    this.performSearch('');
  }

  private performSearch(searchTerm: string): void {
    console.log('🔎 performSearch called with searchTerm:', searchTerm);
    
    if (!searchTerm.trim()) {
      console.log('🔎 Empty search term, loading all rules');
      this.currentPage = 1;
      this.filteredRules = [...this.rules];
      this.isSearching = false;
      this.isSearchMode = false;
      this.loadRules();
      return;
    }

    console.log('🔎 Starting search with term:', searchTerm.trim());
    this.isSearching = true;
    this.isSearchMode = true;

    const lister: Lister<RuleComprehensive> = {
      Pagination: {
        CurrentPage: this.currentPage,
        PageSize: this.pageSize,
        PageCount: 0,
        IsLast: false,
        IsFirst: this.currentPage === 1,
        StartIndex: (this.currentPage - 1) * this.pageSize,
        TotalElement: 0
      },
      SortParams: [{ 
        Column: this.currentSortColumn, 
        Sort: this.currentSortDirection 
      }],
      FilterParams: [...this.appliedFilters]
    };

    const hasComplexFilters = this.appliedFilters.length > 3;
    const request$ = hasComplexFilters
      ? this.rulesApiService.searchRulesAdvanced({
          searchTerm: searchTerm.trim(),
          includeInactive: false,
          page: this.currentPage,
          pageSize: this.pageSize,
          sortParams: lister.SortParams,
          filterParams: this.appliedFilters
        })
      : this.rulesApiService.searchRules(searchTerm.trim(), lister);

    request$.pipe(
      map(response => {
        this.totalElements = response.Lister.Pagination.TotalElement || 0;
        this.pageCount = response.Lister.Pagination.PageCount || Math.ceil(this.totalElements / this.pageSize);

        if (this.currentPage > this.pageCount && this.pageCount > 0) {
          this.currentPage = this.pageCount;
        }

        return response.Content.map(dto => {
          if (dto.RawData) {
            this.rawDataCache.set(dto.RuleId, dto.RawData);
          }
          if (dto.RuleSummary) {
            this.summaryCache.set(dto.RuleId, dto.RuleSummary);
          }
          return this.mapDtoToRuleContent(dto);
        });
      })
    ).subscribe({
      next: (searchedRules) => {
        console.log('✅ Search successful, found rules:', searchedRules.length);
        this.filteredRules = searchedRules;
        this.loadHierarchyForSearchResults(searchedRules);
        this.isSearching = false;
      },
      error: (error) => {
        console.error('❌ Error performing search:', error);
        this.isSearching = false;
        this.performLocalSearch(searchTerm);
        this.toast.info('Erreur lors de la recherche, filtrage local utilisé', 'Info', 3000, false);
      }
    });
  }

  private loadHierarchyForSearchResults(searchResults: RuleContent[]): void {
    const rulesToLoadHierarchy = searchResults.filter(searchRule => {
      const existingRule = this.rules.find(r => r.id === searchRule.id);
      return !existingRule || existingRule.clients.length === 0;
    });

    if (rulesToLoadHierarchy.length > 0) {
      const hierarchyObservables = rulesToLoadHierarchy.map(rule =>
        forkJoin({
          clientHierarchy: this.rulesApiService.getRuleClientHierarchy(rule.id),
          transactionDetails: this.rulesApiService.getRuleTransactionDetails(rule.id),
          executionChart: this.rulesApiService.getRuleExecutionChart(rule.id, 30),
          recentExecutions: this.rulesApiService.getRecentRuleExecutions(rule.id, 1)
        }).pipe(
          map(data => ({
            ruleId: rule.id,
            clientHierarchy: data.clientHierarchy,
            transactionDetails: data.transactionDetails,
            executionChart: data.executionChart,
            recentExecutions: data.recentExecutions
          }))
        )
      );

      forkJoin(hierarchyObservables).subscribe({
        next: (results) => {
          results.forEach(result => {
            const searchRule = this.filteredRules.find(r => r.id === result.ruleId);
            if (searchRule) {
              searchRule.clients = this.aggregateClientHierarchyWithControllers(
                result.clientHierarchy,
                result.transactionDetails
              );
              
              // Cache execution data
              this.executionChartCache.set(result.ruleId, result.executionChart);
              this.executionSimpleCache.set(result.ruleId, result.recentExecutions);
              
              // Update total executions
              if (result.executionChart.length > 0) {
                const totalExecutions = result.executionChart.reduce(
                  (sum, day) => sum + day.TotalExecutions, 0
                );
                searchRule.totalApplications = totalExecutions;
              }
            }

            const mainRule = this.rules.find(r => r.id === result.ruleId);
            if (mainRule) {
              mainRule.clients = this.aggregateClientHierarchyWithControllers(
                result.clientHierarchy,
                result.transactionDetails
              );
              
              if (result.executionChart.length > 0) {
                const totalExecutions = result.executionChart.reduce(
                  (sum, day) => sum + day.TotalExecutions, 0
                );
                mainRule.totalApplications = totalExecutions;
              }
            }
          });
        },
        error: (error) => {
          console.error('Error loading hierarchy for search results:', error);
        }
      });
    }
  }

  private performLocalSearch(searchTerm: string): void {
    console.log('🔍 Performing local search with term:', searchTerm);
    const term = searchTerm.toLowerCase().trim();
    this.filteredRules = this.rules.filter(rule => {
      return rule.name.toLowerCase().includes(term) ||
             rule.tags.some(tag => tag.toLowerCase().includes(term)) ||
             rule.actions.some(action =>
               action.type.toLowerCase().includes(term) ||
               action.action.toLowerCase().includes(term) ||
               action.value.toLowerCase().includes(term) ||
               action.target.toLowerCase().includes(term)
             ) ||
             rule.conditions.some(condition =>
               condition.type.toLowerCase().includes(term) ||
               condition.operator.toLowerCase().includes(term) ||
               condition.value.toLowerCase().includes(term)
             ) ||
             this.rawDataCache.get(rule.id)?.toLowerCase().includes(term) ||
             this.summaryCache.get(rule.id)?.toLowerCase().includes(term);
    });
  }

  // === CORE DATA LOADING ===
  private loadRules(): void {
    this.loader.start();
    this.isLoading = true;
    this.isSearching = false;

    if (this.currentPage < 1) {
      this.currentPage = 1;
    }

    const lister: Lister<RuleComprehensive> = {
      Pagination: {
        CurrentPage: this.currentPage,
        PageSize: this.pageSize,
        PageCount: 0,
        IsLast: false,
        IsFirst: this.currentPage === 1,
        StartIndex: (this.currentPage - 1) * this.pageSize,
        TotalElement: 0
      },
      SortParams: [{ 
        Column: this.currentSortColumn, 
        Sort: this.currentSortDirection 
      }],
      FilterParams: [...this.appliedFilters]
    };

    const hasComplexFilters = this.appliedFilters.length > 3;

    const request$ = hasComplexFilters 
      ? this.rulesApiService.getRulesComprehensiveAdvanced({
          page: this.currentPage,
          pageSize: this.pageSize,
          sortParams: lister.SortParams,
          filterParams: this.appliedFilters
        })
      : this.rulesApiService.getRulesComprehensive(lister);

    request$.pipe(
      map(response => {
        this.totalElements = response.Lister.Pagination.TotalElement || 0;
        this.pageCount = response.Lister.Pagination.PageCount || Math.ceil(this.totalElements / this.pageSize);

        if (this.currentPage > this.pageCount && this.pageCount > 0) {
          this.currentPage = this.pageCount;
          this.loadRules();
          return [];
        }

        return response.Content.map(dto => {
          if (dto.RawData) {
            this.rawDataCache.set(dto.RuleId, dto.RawData);
          }
          if (dto.RuleSummary) {
            this.summaryCache.set(dto.RuleId, dto.RuleSummary);
          }
          return this.mapDtoToRuleContent(dto);
        });
      })
    ).subscribe({
      next: (mappedRules) => {
        this.rules = mappedRules;
        this.filteredRules = [...this.rules];
        this.loadHierarchyForAllRules();
        this.isLoading = false;
        this.loader.stop();
      },
      error: (error) => {
        console.error('Error loading rules:', error);
        this.isLoading = false;
        this.loader.stop();
        this.showErrorDialog('Erreur lors du chargement des règles. Veuillez vérifier votre connexion et réessayer.');
      }
    });
  }

  // === NEW EXECUTION DATA METHODS ===
  private loadExecutionDataForRule(rule: RuleContent): void {
    if (!rule.id) return;

    forkJoin({
      chartData: this.rulesApiService.getRuleExecutionChart(rule.id, 30),
      recentExecutions: this.rulesApiService.getRecentRuleExecutions(rule.id, 1)
    }).subscribe({
      next: (data) => {
        // Cache the data
        this.executionChartCache.set(rule.id, data.chartData);
        this.executionSimpleCache.set(rule.id, data.recentExecutions);

        // Update rule with total execution counts from chart data
        if (data.chartData.length > 0) {
          const totalStats = data.chartData.reduce((acc, day) => ({
            total: acc.total + day.TotalExecutions,
            successful: acc.successful + day.SuccessfulExecutions,
            failed: acc.failed + day.FailedExecutions
          }), { total: 0, successful: 0, failed: 0 });

          rule.totalApplications = totalStats.total;
        }

        // Distribute execution data to controllers
        this.distributeExecutionDataToControllers(rule, data.recentExecutions, data.chartData);
      },
      error: (error) => {
        console.error('Error loading execution data for rule:', rule.id, error);
      }
    });
  }

  private distributeExecutionDataToControllers(
    rule: RuleContent, 
    recentExecutions: PagedResponse<RuleExecutionSimple[]>, 
    chartData: RuleExecutionChartData[]
  ): void {
    rule.clients.forEach(client => {
      client.sites.forEach(site => {
        site.locations.forEach(location => {
          location.controllers.forEach(controller => {
            // Filter executions for this specific controller
            const executionsArray = recentExecutions.Content || [];
            const controllerExecutions = executionsArray.filter(
              exec => exec.ControllerId === controller.id
            );
  
            // Store the filtered execution data
            controller.executions = controllerExecutions;
            
            // All controllers in the rule share the same chart data
            // (since chart data is per rule, not per controller)
            controller.executionChartData = chartData;
            if (this.ruleListComponent) {
              this.ruleListComponent.updateControllerChartData(controller.id, controller.executionChartData || []);
            }
  
            console.log(`Controller ${controller.id} has ${controllerExecutions.length} executions`);
          });
        });
      });
    });
  }

  getRuleExecutionChartData(ruleId: string): RuleExecutionChartData[] {
    return this.executionChartCache.get(ruleId) || [];
  }

  getRecentExecutionsForRule(ruleId: string): RuleExecutionSimple[] {
    const paged = this.executionSimpleCache.get(ruleId);
    return paged ? paged.Content || [] : [];
  }

  // === NEW EXECUTION DATA DISPLAY METHODS ===
  
  // Get execution data for chart display (works directly with new data)
  getExecutionChartForDisplay(ruleId: string): ExecutionChartDisplayData[] {
    const chartData = this.executionChartCache.get(ruleId) || [];
    return chartData.map(day => ({
      date: new Date(day.ExecutionDate).toLocaleDateString('fr-FR', { 
        day: '2-digit', 
        month: '2-digit' 
      }),
      fullDate: day.ExecutionDate,
      total: day.TotalExecutions,
      successful: day.SuccessfulExecutions,
      failed: day.FailedExecutions,
      successRate: day.TotalExecutions > 0 ? (day.SuccessfulExecutions / day.TotalExecutions) * 100 : 0
    }));
  }

  // Get recent executions for display
  getRecentExecutionsForDisplay(ruleId: string, controllerId?: string): ExecutionDisplayData[] {
    const paged = this.executionSimpleCache.get(ruleId);
    const executions = paged ? paged.Content || [] : [];
    const filtered = controllerId 
      ? executions.filter(exec => exec.ControllerId === controllerId)
      : executions;
    
    return filtered.map(exec => ({
      timestamp: exec.ExecutionTimestamp,
      formattedTimestamp: this.formatTimestamp(exec.ExecutionTimestamp),
      isSuccess: exec.IsSuccess,
      controllerId: exec.ControllerId,
      statusText: exec.IsSuccess ? 'Succès' : 'Échec',
      statusIcon: exec.IsSuccess ? 'check_circle' : 'cancel'
    }));
  }

  // Get controller execution summary
  getControllerExecutionSummary(ruleId: string, controllerId: string): ControllerExecutionSummary {
    const executions = this.getRecentExecutionsForDisplay(ruleId, controllerId);
    const total = executions.length;
    const successful = executions.filter(exec => exec.isSuccess).length;
    const failed = total - successful;
    
    return {
      total,
      successful,
      failed,
      successRate: total > 0 ? (successful / total) * 100 : 0,
      lastExecution: executions.length > 0 ? executions[0].formattedTimestamp : 'Aucune'
    };
  }

  getExecutionStatsForRule(ruleId: string): { total: number; successful: number; failed: number; successRate: number } {
    const chartData = this.executionChartCache.get(ruleId) || [];
    
    const stats = chartData.reduce((acc, day) => ({
      total: acc.total + day.TotalExecutions,
      successful: acc.successful + day.SuccessfulExecutions,
      failed: acc.failed + day.FailedExecutions
    }), { total: 0, successful: 0, failed: 0 });

    return {
      ...stats,
      successRate: stats.total > 0 ? (stats.successful / stats.total) * 100 : 0
    };
  }

  loadHourlyExecutionData(ruleId: string, date: Date): void {
    this.rulesApiService.getRuleExecutionHourlyChart(ruleId, date).subscribe({
      next: (hourlyData) => {
        console.log('Hourly execution data for', date, ':', hourlyData);
        // You can integrate this with a detailed chart modal
      },
      error: (error) => {
        console.error('Error loading hourly execution data:', error);
      }
    });
  }

  refreshExecutionDataForRule(ruleId: string): void {
    this.executionChartCache.delete(ruleId);
    this.executionSimpleCache.delete(ruleId);
    
    const rule = this.rules.find(r => r.id === ruleId);
    if (rule) {
      this.loadExecutionDataForRule(rule);
    }
  }

  // === HIERARCHY LOADING WITH EXECUTION DATA ===
  private loadHierarchyForAllRules(): void {
    const hierarchyObservables = this.rules.map(rule =>
      forkJoin({
        clientHierarchy: this.rulesApiService.getRuleClientHierarchy(rule.id),
        transactionDetails: this.rulesApiService.getRuleTransactionDetails(rule.id),
        executionChart: this.rulesApiService.getRuleExecutionChart(rule.id, 30),
        recentExecutions: this.rulesApiService.getRecentRuleExecutions(rule.id, 1) // Increased limit
      }).pipe(
        map(data => ({
          ruleId: rule.id,
          clientHierarchy: data.clientHierarchy,
          transactionDetails: data.transactionDetails,
          executionChart: data.executionChart,
          recentExecutions: data.recentExecutions
        }))
      )
    );
  
    if (hierarchyObservables.length > 0) {
      forkJoin(hierarchyObservables).subscribe({
        next: (results) => {
          results.forEach(result => {
            const rule = this.rules.find(r => r.id === result.ruleId);
            if (rule) {
              // Load hierarchy as before
              rule.clients = this.aggregateClientHierarchyWithControllers(
                result.clientHierarchy,
                result.transactionDetails
              );
  
              // Cache execution data
              this.executionChartCache.set(rule.id, result.executionChart);
              this.executionSimpleCache.set(rule.id, result.recentExecutions);
  
              // NEW: Distribute execution data to controllers
              this.distributeExecutionDataToControllers(
                rule, 
                result.recentExecutions, 
                result.executionChart
              );
  
              // Update total applications from execution data
              if (result.executionChart.length > 0) {
                const totalExecutions = result.executionChart.reduce(
                  (sum, day) => sum + day.TotalExecutions, 0
                );
                rule.totalApplications = totalExecutions;
              }
  
              // Update filtered rules as well
              const filteredRule = this.filteredRules.find(r => r.id === result.ruleId);
              if (filteredRule) {
                filteredRule.clients = rule.clients;
                filteredRule.totalApplications = rule.totalApplications;
              }
            }
          });
  
          this.loadControllerDetails();
        },
        error: (error) => {
          console.error('Error loading hierarchy with execution data:', error);
        }
      });
    }
  }

  refreshData(): void {
    if (this.hasPendingOperations()) {
      this.toast.info('Veuillez attendre la fin de l\'opération en cours', 'Info', 3000, false);
      return;
    }
    this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Confirmation',
        message: 'Êtes-vous sûr de vouloir actualiser les données ? Cela réinitialisera les filtres et le tri.',
        confirmText: 'Actualiser',
        cancelText: 'Annuler',
        icon: 'refresh'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    }).afterClosed().subscribe(result => {
      if (result) {
        this.performRefresh();
      }
    });
  }

  private performRefresh(): void {
    this.currentPage = 1;
    this.searchTerm = '';
    this.isSearchMode = false;
    this.expandedRuleId = null;
    this.selectedControllerId = null;
    this.controllersCache.clear();
    this.executionChartCache.clear();
    this.executionSimpleCache.clear();
    this.loadRules();
  }

  // === RULE MANAGEMENT ===
  openRuleFormDialog(ruleId?: string): void {
    if (this.isSearching) {
      this.toast.info('Veuillez attendre la fin de la recherche en cours','Info',3000,false);
      return;
    }

    let ruleData: { rule: any | null } = { rule: null };
    
    if (ruleId) {
      const rawData = this.rawDataCache.get(ruleId);
      if (rawData) {
        try {
          const parsedRule = JSON.parse(rawData);
          ruleData = {
            rule: {
              id: ruleId,
              isEdit: true,
              RawData: rawData,
              ...parsedRule
            }
          };
        } catch (error) {
          console.error('Error parsing raw data for edit:', error);
          this.showErrorDialog('Erreur lors de l\'analyse des données de la règle. Les données pourraient être corrompues.');
          return;
        }
      } else {
        this.rulesApiService.getRuleComprehensiveById(ruleId).subscribe({
          next: (ruleDto) => {
            if (ruleDto && ruleDto.RawData) {
              this.rawDataCache.set(ruleId, ruleDto.RawData);
              if (ruleDto.RuleSummary) {
                this.summaryCache.set(ruleId, ruleDto.RuleSummary);
              }
              this.openRuleFormDialog(ruleId);
            } else {
              this.showErrorDialog('Impossible de charger les données de la règle pour la modification.');
            }
          },
          error: (error) => {
            console.error('Error fetching rule for edit:', error);
            this.showErrorDialog('Erreur lors du chargement de la règle. Veuillez réessayer.');
          }
        });
        return;
      }
    }

    const dialogRef = this.dialog.open(RuleFormComponent, {
      width: '95vw',
      maxWidth: '800px',
      panelClass: 'custom-rule-form-dialog',
      data: ruleData,
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.action === 'saved') {
        this.toast.success(ruleId ? 'Règle mise à jour avec succès' : 'Règle sauvegardée avec succès!','Succès',3000,false);
        this.loadRules();
      }
    });
  }

  deleteRule(ruleId: string): void {
    if (this.isDeleting.has(ruleId)) {
      return;
    }

    const rule = this.rules.find(r => r.id === ruleId);
    const ruleName = rule?.name || 'cette règle';

    let confirmationMessage = `Voulez-vous vraiment supprimer la règle "${ruleName}" ?`;

    if (rule) {
      const details = [];
      if (rule.totalApplications > 0) {
        details.push(`${rule.totalApplications} exécution(s) enregistrée(s)`);
      }
      if (rule.clients.length > 0) {
        const totalControllers = this.getTotalControllersForRule(rule);
        details.push(`${rule.clients.length} client(s) et ${totalControllers} contrôleur(s) associé(s)`);
      }
      if (rule.actions.length > 0) {
        details.push(`${rule.actions.length} action(s) configurée(s)`);
      }

      if (details.length > 0) {
        confirmationMessage += `\n\nCette règle contient :\n• ${details.join('\n• ')}`;
      }

      confirmationMessage += '\n\nCette action est irréversible et supprimera définitivement toutes les données associées.';
    }

    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Supprimer la règle',
        message: confirmationMessage,
        icon: 'delete_forever'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.performDeleteRule(ruleId);
      }
    });
  }

  private performDeleteRule(ruleId: string): void {
    this.isDeleting.add(ruleId);

    this.rulesApiService.delete(ruleId).subscribe({
      next: (success) => {
        this.isDeleting.delete(ruleId);

        if (success) {
          this.toast.success('Règle supprimée avec succès','Succès',3000,false);

          this.rawDataCache.delete(ruleId);
          this.summaryCache.delete(ruleId);
          this.executionChartCache.delete(ruleId);
          this.executionSimpleCache.delete(ruleId);

          if (this.expandedRuleId === ruleId) {
            this.expandedRuleId = null;
            this.selectedControllerId = null;
          }

          this.loadRules();
        } else {
          this.showErrorDialog('Échec de la suppression de la règle. Veuillez réessayer.');
        }
      },
      error: (error) => {
        this.isDeleting.delete(ruleId);
        console.error('Error deleting rule:', error);
        this.showErrorDialog('Erreur lors de la suppression de la règle. Veuillez vérifier votre connexion et réessayer.');
      }
    });
  }

  toggleRuleStatus(rule: RuleContent): void {
    console.log('Toggle rule status:', rule.id);
  }

  duplicateRule(ruleId: string): void {
    console.log('Duplicate rule:', ruleId);
  }

  openTransactionHistory(ruleId: string): void {
    console.log('Open transaction history:', ruleId);
  }

  openAiRuleGeneratorDialog(): void {
    if (this.isSearching) {
      this.toast.info('Veuillez attendre la fin de la recherche en cours','Info',3000,false);
      return;
    }

    const dialogRef = this.dialog.open(AiRuleGeneratorDialogComponent, {
      width: '600px',
      panelClass: 'ai-rule-generator-dialog-panel',
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.action === 'confirm') {
        this.toast.success('Règle générée par IA et sauvegardée avec succès!','Succès',3000,false);
        this.loadRules();
      }
    });
  }

  // === UI STATE MANAGEMENT ===
  toggleExpandRule(ruleId: string): void {
    this.expandedRuleId = this.expandedRuleId === ruleId ? null : ruleId;
    this.selectedControllerId = null;
    this.activeTab = 'summary';

    if (this.expandedRuleId) {
      const rule = this.rules.find(r => r.id === this.expandedRuleId);
      if (rule) {
        this.loadExecutionDataForRule(rule);
      }
    }
  }

  setActiveTab(tab: 'hierarchy' | 'rawdata' | 'summary'): void {
    this.activeTab = tab;
  }

  selectController(controllerId: string): void {
    this.selectedControllerId = this.selectedControllerId === controllerId ? null : controllerId;
  }

  viewControllerDetails(controllerId: string): void {
    console.log('View controller details:', controllerId);
  }

  // === PAGINATION ===
  goToPage(page: number): void {
    if (this.hasPendingOperations()) {
      this.toast.info('Veuillez attendre la fin de l\'opération en cours', 'Info', 3000, false);
      return;
    }
    this.currentPage = page;
    this.applySortAndFilters();
  }

  nextPage(): void {
    if (this.currentPage < this.pageCount && !this.hasPendingOperations()) {
      this.currentPage++;
      this.applySortAndFilters();
    }
  }

  previousPage(): void {
    if (this.currentPage > 1 && !this.hasPendingOperations()) {
      this.currentPage--;
      this.applySortAndFilters();
    }
  }

  // === SORTING AND FILTERING ===
  toggleAdvancedFilters(): void {
    this.showAdvancedFilters = !this.showAdvancedFilters;
  }

  updateSort(column: string): void {
    if (this.currentSortColumn === column) {
      this.currentSortDirection = this.currentSortDirection === 'ASC' ? 'DESC' : 'ASC';
    } else {
      this.currentSortColumn = column;
      this.currentSortDirection = 'ASC';
    }
    this.currentPage = 1;
    this.applySortAndFilters();
  }

  addFilter(column: string, operator: string, value: any): void {
    this.appliedFilters = this.appliedFilters.filter(f => f.Column !== column);

    if (value !== null && value !== undefined && value !== '') {
      if (this.validateFilterValue(column, value, operator)) {
        this.appliedFilters.push({
          Column: column,
          Value: value.toString(),
          Operand: operator
        });
      }
    }
    this.currentPage = 1;
    this.applySortAndFilters();
  }

  removeFilter(column: string): void {
    this.appliedFilters = this.appliedFilters.filter(f => f.Column !== column);
    this.currentPage = 1;
    this.applySortAndFilters();
  }

  clearAllFilters(): void {
    this.appliedFilters = [];
    this.currentPage = 1;
    this.applySortAndFilters();
  }

  private applySortAndFilters(): void {
    if (this.isSearchMode && this.searchTerm.trim()) {
      this.performSearch(this.searchTerm);
    } else {
      this.loadRules();
    }
  }

  createQuickFilter(column: string, value: any, operator: string = 'eq'): void {
    this.addFilter(column, operator, value);
  }

  applyPredefinedFilter(filterType: string): void {
    this.clearAllFilters();
    switch (filterType) {
      case 'active-rules':
        this.createQuickFilter('Status', 'active', 'eq');
        break;
      case 'recent-rules':
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        this.createQuickFilter('RuleCreatedAt', oneWeekAgo.toISOString().split('T')[0], 'gte');
        break;
      case 'high-priority':
        this.createQuickFilter('Priority', '5', 'lt');
        break;
      case 'frequently-used':
        this.createQuickFilter('TotalApplications', '10', 'gt');
        break;
      default:
        break;
    }
  }

  private validateFilterValue(column: string, value: any, operator: string): boolean {
    if (value === null || value === undefined || value === '') {
      return false;
    }
    const option = this.filterOptions.find(opt => opt.column === column);
    if (!option) return true;
    switch (option.type) {
      case 'number':
        return !isNaN(Number(value));
      case 'date':
        return !isNaN(Date.parse(value));
      default:
        return true;
    }
  }

  onFilterOperatorChange(eventData: { event: Event, column: string }): void {
    const target = eventData.event.target as HTMLSelectElement;
    if (target?.value) {
      this.addFilter(eventData.column, target.value, this.getFilterValue(eventData.column) || '');
    }
  }

  onFilterValueChange(eventData: { event: Event, column: string }): void {
    const target = eventData.event.target as HTMLInputElement;
    if (target) {
      this.addFilter(eventData.column, this.getFilterOperator(eventData.column), target.value || '');
    }
  }

  onFilterSelectChange(eventData: { event: Event, column: string }): void {
    const target = eventData.event.target as HTMLSelectElement;
    if (target) {
      this.addFilter(eventData.column, 'eq', target.value || '');
    }
  }

  resetToDefaults(): void {
    this.currentSortColumn = 'Priority';
    this.currentSortDirection = 'ASC';
    this.appliedFilters = [];
    this.searchTerm = '';
    this.showAdvancedFilters = false;
    this.isSearchMode = false;
    this.currentPage = 1;
    this.loadRules();
    this.toast.info('Tri et filtres réinitialisés', 'Info', 2000, false);
  }

  getFilterValue(column: string): any {
    const filter = this.appliedFilters.find(f => f.Column === column);
    return filter ? filter.Value : null;
  }

  getFilterOperator(column: string): string {
    const filter = this.appliedFilters.find(f => f.Column === column);
    return filter ? filter.Operand : 'eq';
  }

  getActiveFiltersCount(): number {
    return this.appliedFilters.length;
  }

  hasAdvancedFeaturesActive(): boolean {
    return this.appliedFilters.length > 0 || 
           this.currentSortColumn !== 'Priority' || 
           this.currentSortDirection !== 'ASC';
  }

  getSortIcon(column: string): string {
    if (this.currentSortColumn !== column) {
      return 'unfold_more';
    }
    return this.currentSortDirection === 'ASC' ? 'keyboard_arrow_up' : 'keyboard_arrow_down';
  }

  isSortedBy(column: string): boolean {
    return this.currentSortColumn === column;
  }

  getFilterDisplayText(filter: WhereParams): string {
    const option = this.filterOptions.find(opt => opt.column === filter.Column);
    const label = option?.label || filter.Column;
    
    let operatorText = '';
    switch (filter.Operand) {
      case 'eq': operatorText = '='; break;
      case 'neq': operatorText = '≠'; break;
      case 'lt': operatorText = '<'; break;
      case 'gt': operatorText = '>'; break;
      case 'lte': operatorText = '≤'; break;
      case 'gte': operatorText = '≥'; break;
      case 'contains': operatorText = 'contient'; break;
      case 'startswith': operatorText = 'commence par'; break;
      case 'endswith': operatorText = 'finit par'; break;
      default: operatorText = filter.Operand;
    }

    return `${label} ${operatorText} ${filter.Value}`;
  }

  // === UTILITY METHODS ===
  getTotalControllersForRule(rule: RuleContent): number {
    return rule.clients.reduce((total, client) => {
      return total + client.sites.reduce((siteAcc, site) => {
        return siteAcc + site.locations.reduce((locAcc, location) => {
          return locAcc + location.controllers.length;
        }, 0);
      }, 0);
    }, 0);
  }

  getPagesArray(): number[] {
    const pages: number[] = [];
    const maxPagesToShow = 5;
    const halfMax = Math.floor(maxPagesToShow / 2);

    let startPage = Math.max(1, this.currentPage - halfMax);
    let endPage = Math.min(this.pageCount, this.currentPage + halfMax);

    if (endPage - startPage + 1 < maxPagesToShow) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }
    if (endPage - startPage + 1 < maxPagesToShow) {
      endPage = Math.min(this.pageCount, startPage + maxPagesToShow - 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    return pages;
  }

  // === HELPER FUNCTIONS FOR RULE LIST COMPONENT ===
  getRuleSummary = (ruleId: string): string => {
    return this.summaryCache.get(ruleId) || 'Résumé non disponible';
  }

  hasRuleSummary = (ruleId: string): boolean => {
    const summary = this.summaryCache.get(ruleId);
    return summary != null && summary.trim().length > 0 && summary !== 'Résumé non disponible';
  }

  formatRawData = (ruleId: string): string => {
    const rawData = this.rawDataCache.get(ruleId);
    if (!rawData) {
      return '{\n  "error": "Données brutes non disponibles"\n}';
    }

    try {
      const parsed = JSON.parse(rawData);
      return JSON.stringify(parsed, null, 2);
    } catch (e) {
      return rawData;
    }
  }

  formatTimestamp = (timestamp: string): string => {
    if (!timestamp) return 'N/A';
    const date = new Date(timestamp);
    return date.toLocaleString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getControllerStatusIcon = (controller: ControllerForRule): string => {
    const cachedController = this.controllersCache.get(controller.id);
    const status = cachedController?.State ?? controller.status;

    switch (status) {
      case true: return 'check_circle';
      case false: return 'error';
      default: return 'help';
    }
  }

  getControllerStatusText = (controller: ControllerForRule): string => {
    const cachedController = this.controllersCache.get(controller.id);
    const status = cachedController?.State ?? controller.status;

    switch (status) {
      case true: return 'En ligne';
      case false: return 'Hors ligne';
      default: return 'Inconnu';
    }
  }

  getControllerStatusClass = (controller: ControllerForRule): string => {
    const cachedController = this.controllersCache.get(controller.id);
    const status = cachedController?.State ?? controller.status;

    switch (status) {
      case true: return 'online';
      case false: return 'offline';
      default: return 'unknown';
    }
  }

  copyRawData(ruleId: string): void {
    const rawData = this.formatRawData(ruleId);
    navigator.clipboard.writeText(rawData).then(() => {
      this.toast.success('Données copiées dans le presse-papiers','Succès',3000,false);
    }).catch(err => {
      console.error('Error copying to clipboard:', err);
      this.toast.warning('Erreur lors de la copie','Erreur',3000,false);
    });
  }

  downloadRawData(rule: RuleContent): void {
    const rawData = this.formatRawData(rule.id);
    const blob = new Blob([rawData], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `rule-${rule.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}-${rule.id.substring(0, 8)}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    this.toast.success('Téléchargement démarré','Succès',3000,false);
  }

  // === DATA AGGREGATION METHODS ===
  private aggregateClientHierarchyWithControllers(
    hierarchy: RuleClientHierarchy[],
    transactionDetails: RuleTransactionDetail[]
  ): ClientForRule[] {
    const clientsMap = new Map<string, ClientForRule>();

    // Build the basic client/site/local hierarchy
    hierarchy.forEach(item => {
      if (!clientsMap.has(item.ClientId)) {
        clientsMap.set(item.ClientId, {
          id: item.ClientId,
          name: item.ClientRC || item.ClientSIREN || 'Unknown Client',
          email: '',
          company: '',
          status: 'active',
          sites: []
        });
      }
      const client = clientsMap.get(item.ClientId)!;

      let site = client.sites.find(s => s.id === item.SiteId);
      if (!site) {
        site = {
          id: item.SiteId,
          name: item.SiteName,
          address: item.SiteAddress,
          clientId: item.ClientId,
          locations: []
        };
        client.sites.push(site);
      }

      let location = site.locations.find(l => l.id === item.LocalId);
      if (!location) {
        location = {
          id: item.LocalId,
          name: item.LocalName,
          type: 'room',
          controllers: []
        };
        site.locations.push(location);
      }
    });

    // Add controllers from transaction details
    transactionDetails.forEach(detail => {
      const actualControllerId = detail.ControllerIdController || detail.ControllerId;

      if (!actualControllerId) {
        return;
      }

      clientsMap.forEach(client => {
        client.sites.forEach(site => {
          site.locations.forEach(location => {
            if (location.id === detail.ControllerLocalId) {
              if (!location.controllers.find(c => c.id === actualControllerId)) {
                const controllerForRule: ControllerForRule = {
                  id: actualControllerId,
                  name: `Controller ${actualControllerId.substring(0, 8)}`,
                  model: 'Loading...',
                  lastSeen: detail.TransactionCreatedAt,
                  status: detail.ControllerInControl,
                  executions: [],
                  executionChartData: []
                };
                location.controllers.push(controllerForRule);
              }
            }
          });
        });
      });
    });

    return Array.from(clientsMap.values());
  }

  private loadControllerDetails(): void {
    const controllerIds = new Set<string>();

    this.rules.forEach(rule => {
      rule.clients.forEach(client => {
        client.sites.forEach(site => {
          site.locations.forEach(location => {
            location.controllers.forEach(controller => {
              controllerIds.add(controller.id);
            });
          });
        });
      });
    });

    const controllerObservables = Array.from(controllerIds).map(controllerId =>
      this.controllerApiService.getById(controllerId).pipe(
        map(controller => ({
          controllerId,
          controller
        }))
      )
    );

    if (controllerObservables.length > 0) {
      forkJoin(controllerObservables).subscribe({
        next: (results) => {
          results.forEach(result => {
            if (result.controller) {
              this.controllersCache.set(result.controllerId, result.controller);
            }
          });

          this.updateRulesWithControllerData();
        },
        error: (error) => {
          console.error('Error loading controller details:', error);
        }
      });
    }
  }

  private updateRulesWithControllerData(): void {
    [this.rules, this.filteredRules].forEach(rulesList => {
      rulesList.forEach(rule => {
        rule.clients.forEach(client => {
          client.sites.forEach(site => {
            site.locations.forEach(location => {
              location.controllers.forEach(controller => {
                const cachedController = this.controllersCache.get(controller.id);
                if (cachedController) {
                  controller.name = cachedController.HostName || `Controller ${controller.id.substring(0, 8)}`;
                  controller.model = cachedController.Model || 'Unknown Model';
                  controller.lastSeen = cachedController.LastConnection ?
                    cachedController.LastConnection.toString() : controller.lastSeen;
                  controller.status = cachedController.State ?? false;
                }
              });
            });
          });
        });
      });
    });
  }

  private showErrorDialog(message: string): void {
    this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Erreur',
        message: message,
        icon: 'error'
      },
      disableClose: true,
      panelClass: 'confirmation-dialog-panel'
    });
  }

  // === MAPPING FUNCTION ===
  private mapDtoToRuleContent(dto: RuleComprehensive): RuleContent {
    let parsedRawData: RawDataBackendStructure | null = null;
    try {
      if (dto.RawData) {
        parsedRawData = JSON.parse(dto.RawData) as RawDataBackendStructure;
      }
    } catch (e) {
      console.error('Error parsing RawData for rule:', dto.RuleId, e);
    }

    const priority: number = parsedRawData?.priority ?? dto.Priority;
    const status: string = dto.Enabled ? 'active' : 'inactive';

    const conditions: Condition[] = [];
    if (parsedRawData && parsedRawData.conditions?.groups?.length > 0) {
      parsedRawData.conditions.groups[0].conditions.forEach((cond, index) => {
        conditions.push({
          id: index + 1,
          type: cond.type,
          operator: cond.operator,
          value: cond.value,
          deviceId: cond.device,
          propertyName: cond.key
        });
      });
    }

    const actions: Action[] = [];
    if (parsedRawData && parsedRawData.actions?.length > 0) {
      parsedRawData.actions.forEach((act, index) => {
        let actionValue = '';
        let actionName = act.type;
        if (act.payload) {
          const payloadKeys = Object.keys(act.payload);
          if (payloadKeys.length > 0) {
            actionName = payloadKeys[0];
            actionValue = String(act.payload[payloadKeys[0]]);
          }
        }

        actions.push({
          id: index + 1,
          type: act.type,
          action: actionName,
          value: actionValue,
          target: act.topic
        });
      });
    }

    const name: string = parsedRawData?.rule_name || `Rule ${dto.RuleId.substring(0, 8)}`;
    const tagsArray: string[] = dto.TagsString ? dto.TagsString.split(', ').filter(t => t.length > 0) : [];
    const tagStatus: { [key: string]: string } = {};
    tagsArray.forEach(tag => tagStatus[tag] = 'active');

    return {
      id: dto.RuleId,
      name: name,
      priority: priority,
      status: status as 'active' | 'inactive',
      tags: tagsArray,
      conditions: conditions,
      actions: actions,
      tagStatus: tagStatus,
      totalApplications: dto.TotalApplications,
      lastTriggered: dto.LastTriggered || '',
      clients: []
    };
  }
}