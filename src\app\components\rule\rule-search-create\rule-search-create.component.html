<div class="search-create">
    <div class="search-container">
        <input type="text" placeholder="Rechercher une règle par nom, tag ou contenu..." class="input"
            [(ngModel)]="internalSearchTerm" (keyup.enter)="onSearchSubmit()" />
        <button mat-icon-button (click)="onSearchSubmit()" [disabled]="isSearching" matTooltip="Rechercher">
            <mat-icon>search</mat-icon>
        </button>
        <button mat-icon-button (click)="onClearSearch()" [disabled]="!internalSearchTerm || isSearching"
            matTooltip="Nettoyer la recherche">
            <mat-icon>clear</mat-icon>
        </button>
        <div *ngIf="isSearching" class="search-loading">
            <mat-icon class="loading-icon">hourglass_empty</mat-icon>
        </div>
    </div>

    <div class="create-buttons">
        <button mat-raised-button class="ai-create-button" (click)="onAiRuleGenerator()"
            [disabled]="hasPendingOperations" matTooltip="Générer une règle automatiquement avec l'IA">
            <mat-icon>auto_awesome</mat-icon>
            Générer avec IA
        </button>

        <button mat-raised-button class="create-button" (click)="onCreateRule()" [disabled]="hasPendingOperations">
            <mat-icon>add</mat-icon>
            Créer une nouvelle règle
        </button>
    </div>
</div>