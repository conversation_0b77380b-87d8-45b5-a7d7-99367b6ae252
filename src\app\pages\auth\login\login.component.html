<div class="login-container" [@fadeIn]>
    <ng-toast></ng-toast>
    <div class="login-card" [@slideInRight]>
        <div class="card-header">
            <div class="logo-container">
                <div class="logo-circle">
                    <img src="assets/images/logoIOT.png" alt="IOT Logo" class="logo-image">
                </div>
            </div>
            <p class="card-subtitle">Connectez-vous pour acceder à UBBEE</p>
        </div>
        <div *ngIf="loginError" class="error-message p-mb-3">
            <i class="pi pi-exclamation-triangle"></i> Nom d'utilisateur ou mot de passe invalide
        </div>

        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
        <div class="form-field p-float-label">
            <i class="pi pi-user input-icon"></i>
            <input
                id="username"
                type="text"
                pInputText
                placeholder="Username"
                formControlName="username" 
                class="custom-input"
                [ngClass]="{'input-invalid': loginForm.get('username')?.invalid && loginForm.get('username')?.touched}">
            </div>
            <small 
                *ngIf="loginForm.get('username')?.invalid && loginForm.get('username')?.touched" 
                class="error-message">
                Username is required
            </small>


        <div class="form-field p-float-label">

            <i class="pi pi-lock input-icon"></i>

            <input
                id="password"
                type="password"
                placeholder="Password"
                pPassword
                [feedback]="false"
                formControlName="password"
                class="custom-input"
                [ngClass]="{'input-invalid': loginForm.get('password')?.invalid && loginForm.get('password')?.touched}">
            </div>
            <small
                *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched"
                class="error-message">
                Password est requis
            </small>

            <div class="button-container">
                <button
                  pButton
                  pRipple
                  type="submit"
                  class="custom-button">
                  <span class="button-text">Se connecter</span>
                </button>
            </div>
            <div class="footer-link">
                <span>Vous n’avez pas de compte ?</span>
                <a routerLink="/register">Créer un compte</a>
              </div>
              
            </form>
        </div>
    </div>
