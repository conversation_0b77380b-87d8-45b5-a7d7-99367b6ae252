import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiService } from '../api.service';
import { Site } from '@app/core/models/site';
import { environment } from '../../../environments/environment';
import { Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class SiteApiService extends ApiService<Site> {
   override baseUrl: string = environment.host.endsWith('/')
      ? environment.host + 'api/'
      : environment.host + '/api/';
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("site");
  }

  uploadImage(siteId: string, imageData: string): Observable<any> {
    const requestBody = {
      id: siteId,
      imageData: imageData
    };

    return this.http.post(`${this.baseUrl}Images/upload/site`, requestBody, {
      headers: {
        'Content-Type': 'application/json-patch+json'
      }
    });
  }

  downloadImage(siteId: string): Observable<any> {
    return this.http.post(`${this.baseUrl}Images/download/site`, null, {
      params: {
        Id: siteId
      },
      headers: {
        'Content-Type': 'application/json-patch+json'
      }
    });
  }
}
