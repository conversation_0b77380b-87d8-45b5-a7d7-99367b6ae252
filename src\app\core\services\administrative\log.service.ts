import { Injectable } from '@angular/core';
import { ApiService } from '../api.service';
import { Log } from '@app/core/models/log';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '@app/environments/environment';

@Injectable({ providedIn: 'root' })
export class LogApiService extends ApiService<Log> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint('log');
  }

  searchLogs(filter: any): Observable<any> {
    return this.http.post(
      `${this.getFullUrl('/search')}`,
      filter,
      this.httpOptions
    );
  }
  summarizeLog(rule: any): Observable<String> {
    const summarizationUrl =
      environment.host + '/api/text-summarization/summarizeLogs';
    return this.http.post<String>(summarizationUrl, rule);
  }
}
