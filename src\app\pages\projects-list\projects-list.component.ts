import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatMenuModule } from '@angular/material/menu';
import { MatChipsModule } from '@angular/material/chips';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AuthService } from '../../core/services/auth.service';
import { EnterpriseService, EnterpriseUser } from '../../core/services/enterprise.service';
import { trigger, transition, style, animate, query, stagger } from '@angular/animations';

interface Member {
  id: string;
  userName: string;
}

interface Project {
  id: string;
  name: string;
  description: string;
  label: string;
  dueDate: string;
  sensorCount: number;
  progress: number;
  members: Member[];
  createdBy: string;
  createdByUser?: { userName: string };
}

@Component({
  selector: 'app-projects-list',
  templateUrl: './projects-list.component.html',
  styleUrls: ['./projects-list.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatTableModule,
    MatIconModule,
    MatButtonModule,
    MatProgressBarModule,
    MatTooltipModule,
    MatProgressSpinnerModule,
    MatMenuModule,
    MatChipsModule
  ],
  animations: [
    trigger('cardAnimation', [
      transition(':enter', [
        style({ 
          opacity: 0,
          transform: 'scale(0.95) translateY(20px)'
        }),
        animate('0.4s ease-out', 
          style({ 
            opacity: 1,
            transform: 'scale(1) translateY(0)'
          })
        )
      ], { delay: '{{ delay }}' })
    ]),
    trigger('listAnimation', [
      transition('* => *', [
        query(':enter', [
          style({ opacity: 0, transform: 'translateY(15px)' }),
          stagger(80, [
            animate('0.4s ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
          ])
        ], { optional: true })
      ])
    ])
  ]
})
export class ProjectsListComponent implements OnInit {
  displayedColumns: string[] = [
    'name',
    'label',
    'dueDate',
    'sensorCount',
    'progress',
    'members',
    'createdBy',
    'actions'
  ];
  projects: Project[] = [];
  isLoading = true;
  isAdminOrSuperAdmin = false;
  isSuperAdmin = false;
  userId: string | null = null;
  viewMode: 'cards' | 'list' = 'cards';
  enterpriseUsers: EnterpriseUser[] = [];

  constructor(
    private http: HttpClient,
    private router: Router,
    private snackBar: MatSnackBar,
    private authService: AuthService,
    private enterpriseService: EnterpriseService
  ) {}

  ngOnInit(): void {
    this.userId = this.authService.getUserId();
    const roles = this.authService.getRoles();
    this.isAdminOrSuperAdmin = roles.includes('Admin') || roles.includes('SuperAdmin');
    this.isSuperAdmin = roles.includes('SuperAdmin');

    if (this.isAdminOrSuperAdmin) {
      this.loadEnterpriseUsers();
    }
    this.loadProjects();
  }

  loadEnterpriseUsers(): void {
    this.enterpriseService.getEnterpriseUsers().subscribe({
      next: (users) => {
        this.enterpriseUsers = this.isSuperAdmin
          ? users
          : users.filter(user => user.createdByAdminId === this.userId);
      },
      error: () => {
        this.snackBar.open('Error loading enterprise users', 'Close', { duration: 3000 });
      }
    });
  }

  loadProjects(): void {
    this.isLoading = true;
    this.http.get<Project[]>('http://localhost:5256/api/projects').subscribe({
      next: (data) => {
        this.projects = data;
        this.isLoading = false;
      },
      error: () => {
        this.snackBar.open('Error loading projects', 'Close', { duration: 3000 });
        this.isLoading = false;
      }
    });
  }

  getMemberUserNames(members: Member[]): string {
    if (!members || members.length === 0) {
      return 'No members';
    }
    return members
      .map(member => member.userName || member.id)
      .filter(name => name)
      .join(', ');
  }
  
  getMemberExcessTooltip(members: Member[]): string {
    if (!members || members.length <= 3) {
      return '';
    }
    return members
      .slice(3)
      .map(member => member.userName || member.id)
      .filter(name => name)
      .join(', ');
  }

  getInitials(name: string): string {
    if (!name) return '?';
    return name.split(' ')
      .map(part => part.charAt(0).toUpperCase())
      .slice(0, 2)
      .join('');
  }

  getProgressClass(progress: number): string {
    if (progress < 30) return 'progress-low';
    if (progress < 70) return 'progress-medium';
    return 'progress-high';
  }

  toggleViewMode(): void {
    this.viewMode = this.viewMode === 'cards' ? 'list' : 'cards';
  }

  navigateToAddProject(): void {
    this.router.navigate(['/projects/add']);
  }

  navigateToUpdateProject(projectId: string): void {
    this.router.navigate(['/projects/update', projectId]);
  }

  navigateToDeleteProject(projectId: string): void {
    this.router.navigate(['/projects/delete', projectId]);
  }
}