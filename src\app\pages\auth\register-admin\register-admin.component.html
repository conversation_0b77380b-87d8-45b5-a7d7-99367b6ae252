<!-- src/app/pages/auth/register-admin/register-admin.component.html -->
<div class="register-container" [@fadeIn]>
    <mat-card class="register-card" [@cardAnimation]>
      <mat-card-header class="header">
        <mat-card-title class="title">
          <mat-icon class="title-icon">admin_panel_settings</mat-icon>
          Enregistrer nouvel Administrateur
        </mat-card-title>
        <mat-card-subtitle class="subtitle">Créer un nouvel utilisateur administrateur</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content class="content">
        <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
          <mat-form-field appearance="outline" class="full-width" floatLabel="always">
            <mat-label>Nom d'utilisateur </mat-label>
            <input matInput formControlName="userName" required>
            <mat-icon matSuffix class="input-icon">person</mat-icon>
            <mat-error *ngIf="registerForm.get('userName')?.hasError('required')">
              Nom d'utilisateur est requis
            </mat-error>
          </mat-form-field>
  
          <mat-form-field appearance="outline" class="full-width" floatLabel="always">
            <mat-label>Email</mat-label>
            <input matInput type="email" formControlName="email" required>
            <mat-icon matSuffix class="input-icon">email</mat-icon>
            <mat-error *ngIf="registerForm.get('email')?.hasError('required')">
              Email est requis
            </mat-error>
            <mat-error *ngIf="registerForm.get('email')?.hasError('email')">
              Format d'email invalide
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width" floatLabel="always">
            <mat-label>Nom complet</mat-label>
            <input matInput formControlName="fullName" required>
            <mat-icon matSuffix class="input-icon">badge</mat-icon>
            <mat-error *ngIf="registerForm.get('fullName')?.hasError('required')">
              Nom complet est requis
            </mat-error>
          </mat-form-field>
  
          <mat-form-field appearance="outline" class="full-width" floatLabel="always">
  <mat-label>Mot de passe</mat-label>
  <input
    matInput
    [type]="hidePassword ? 'password' : 'text'"
    formControlName="password"
    required
  />
  <button
    mat-icon-button
    matSuffix
    type="button"
    (click)="hidePassword = !hidePassword"
    tabindex="-1"
  >
    <mat-icon class="input-icon">{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
  </button>

  <mat-error *ngIf="registerForm.get('password')?.hasError('required')">
    Mot de passe est requis
  </mat-error>
  <mat-error *ngIf="registerForm.get('password')?.hasError('minlength')">
    Le mot de passe doit comporter au moins 8 caractères
  </mat-error>
</mat-form-field>

  
          <div class="button-container">
            <button
              mat-raised-button
              color="primary"
              type="submit"
              class="submit-button"
              [disabled]="registerForm.invalid"
              [@buttonAnimation]
            >
              <mat-icon>person_add</mat-icon>
              Enregistrer administrateur
            </button>
            <button
              mat-stroked-button
              color="warn"
              type="button"
              class="cancel-button"
              (click)="onCancel()"
              [@buttonAnimation]
            >
              <mat-icon>cancel</mat-icon>
              Annuler
            </button>
          </div>
        </form>
      </mat-card-content>
    </mat-card>
  </div>