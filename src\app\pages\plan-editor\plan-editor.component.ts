import { Component, AfterViewInit } from '@angular/core';
import * as fabric from 'fabric';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { InstalationService } from '@app/core/services/instalation.service';
import { SvgDefinitionsService } from '@app/core/services/svgdefinition.service';
import { MatIconModule } from '@angular/material/icon';

export interface SVGColors {
  fillColor: string;
  strokeColor: string;
  strokeWidth: number;
}
@Component({
  selector: 'app-plan-editor',
  imports: [CommonModule, FormsModule,MatIconModule],
  templateUrl: './plan-editor.component.html',
  styleUrls: ['./plan-editor.component.css'],
})


export class PlanEditorComponent implements AfterViewInit {
  canvas!: fabric.Canvas;

  canvasInitialized = false;
  selectedTool = '';
  planType = 'local'; // 'local' ou 'site'
  isGridVisible = true;

  // Palette de couleurs
  selectedColor = '#3B82F6';
  selectedStrokeColor = '#374151';
  selectedFillColor='#374151';
  selectedStrokeWidth = 2;
  constructor(private installService: InstalationService,private svgDefinitionsService: SvgDefinitionsService) {}
  ngOnInit() {
    this.installService.getClients().subscribe((data) => {
      this.clients = data;
      console.log(this.clients);
    });
  }
  colorPalette = [
    { name: 'Bleu', color: '#3B82F6' },
    { name: 'Vert', color: '#059669' },
    { name: 'Rouge', color: '#DC2626' },
    { name: 'Orange', color: '#D97706' },
    { name: 'Violet', color: '#7C3AED' },
    { name: 'Rose', color: '#DB2777' },
    { name: 'Gris', color: '#6B7280' },
    { name: 'Noir', color: '#111827' },
    { name: 'Marron', color: '#92400E' },
    { name: 'Cyan', color: '#0891B2' },
    { name: 'Lime', color: '#65A30D' },
    { name: 'Indigo', color: '#4338CA' },
  ];

  strokeColors = [
    { name: 'Gris foncé', color: '#374151' },
    { name: 'Noir', color: '#111827' },
    { name: 'Blanc', color: '#FFFFFF' },
    { name: 'Bleu', color: '#3B82F6' },
    { name: 'Rouge', color: '#DC2626' },
  ];

  // Éléments de structure
  elements = [
    {
      type: 'capteur',
      name: 'Capteur',
      icon: '📡',
      description: 'Capteur IoT',
      color: '#3B82F6',
      category: 'technique',
    },
    {
      type: 'mur',
      name: 'Mur',
      icon: '🧱',
      description: 'Mur / Cloison',
      color: '#6B7280',
      category: 'structure',
    },
    {
      type: 'porte',
      name: 'Porte',
      icon: '🚪',
      description: 'Porte / Ouverture',
      color: '#92400E',
      category: 'structure',
    },
    {
      type: 'fenetre',
      name: 'Fenêtre',
      icon: '🪟',
      description: 'Fenêtre',
      color: '#0891B2',
      category: 'structure',
    },
    {
      type: 'escalier',
      name: 'Escalier',
      icon: '🪜',
      description: 'Escalier',
      color: '#7C2D12',
      category: 'structure',
    },
    {
      type: 'zone',
      name: 'Zone',
      icon: '📦',
      description: 'Zone / Pièce',
      color: '#059669',
      category: 'structure',
    },
  ];

  // Éléments de mobilier avec SVG
  mobilierElements = [
    {
      type: 'bureau',
      name: 'Bureau',
      icon: '🖥️',
      description: 'Bureau de travail',
      color: '#8B4513',
      useSVG: true,
    },
    {
      type: 'table',
      name: 'Table',
      icon: '🪑',
      description: 'Table de réunion',
      color: '#CD853F',
      useSVG: true,
    },
    {
      type: 'chaise',
      name: 'Chaise',
      icon: '🪑',
      description: 'Chaise de bureau',
      color: '#2F4F4F',
      useSVG: true,
    },
    {
      type: 'armoire',
      name: 'Armoire',
      icon: '🗄️',
      description: 'Armoire / Rangement',
      color: '#556B2F',
      useSVG: true,
    },
    {
      type: 'canape',
      name: 'Canapé',
      icon: '🛋️',
      description: 'Canapé / Sofa',
      color: '#4682B4',
      useSVG: true,
    },
    {
      type: 'lit',
      name: 'Lit',
      icon: '🛏️',
      description: 'Lit',
      color: '#BC8F8F',
      useSVG: true,
    },
    {
      type: 'evier',
      name: 'Évier',
      icon: '🚿',
      description: 'Évier / Lavabo',
      color: '#20B2AA',
      useSVG: true,
    },
    {
      type: 'frigo',
      name: 'Réfrigérateur',
      icon: '🧊',
      description: 'Réfrigérateur',
      color: '#F0F8FF',
      useSVG: true,
    },
    {
      type: 'cuisiniere',
      name: 'Cuisinière',
      icon: '🔥',
      description: 'Cuisinière / Plaque',
      color: '#FF6347',
      useSVG: true,
    },
    {
      type: 'toilette',
      name: 'Toilette',
      icon: '🚽',
      description: 'WC / Toilette',
      color: '#E6E6FA',
      useSVG: true,
    },
    {
      type: 'douche',
      name: 'Douche',
      icon: '🚿',
      description: 'Cabine de douche',
      color: '#B0E0E6',
      useSVG: true,
    },
    {
      type: 'plante',
      name: 'Plante',
      icon: '🪴',
      description: 'Plante décorative',
      color: '#228B22',
      useSVG: true,
    }
     
  ];
// ============ ÉLECTROMÉNAGER ============
electromenager = [
  {
    type: 'ceiling-fan',
    name: 'Ventilateur plafond',
    icon: '🌀',
    description: 'Ventilateur de plafond',
    color: '#6B7280',
    category: 'electromenager',
    useSVG: true,
  },
  {
    type: 'refrigerator',
    name: 'Réfrigérateur',
    icon: '🧊',
    description: 'Réfrigérateur / Frigo',
    color: '#E5E7EB',
    category: 'electromenager',
    useSVG: true,
  },
  {
    type: 'dishwasher',
    name: 'Lave-vaisselle',
    icon: '🍽️',
    description: 'Lave-vaisselle',
    color: '#9CA3AF',
    category: 'electromenager',
    useSVG: true,
  },
  {
    type: 'oven',
    name: 'Four',
    icon: '🔥',
    description: 'Four / Cuisinière',
    color: '#374151',
    category: 'electromenager',
    useSVG: true,
  },
  {
    type: 'washing-machine',
    name: 'Lave-linge',
    icon: '👕',
    description: 'Machine à laver',
    color: '#F3F4F6',
    category: 'electromenager',
    useSVG: true,
  },
  {
    type: 'microwave',
    name: 'Micro-ondes',
    icon: '📻',
    description: 'Four micro-ondes',
    color: '#D1D5DB',
    category: 'electromenager',
    useSVG: true,
  }
];

// ============ SALLE DE BAIN ============
salleDeBain = [
  {
    type: 'sink-simple',
    name: 'Lavabo simple',
    icon: '🚿',
    description: 'Lavabo / Vasque',
    color: '#DBEAFE',
    category: 'salle-de-bain',
    useSVG: true,
  },
  {
    type: 'sink-double',
    name: 'Lavabo double',
    icon: '🛁',
    description: 'Double vasque',
    color: '#BFDBFE',
    category: 'salle-de-bain',
    useSVG: true,
  },
  {
    type: 'toilet',
    name: 'WC',
    icon: '🚽',
    description: 'Toilettes / WC',
    color: '#F8FAFC',
    category: 'salle-de-bain',
    useSVG: true,
  },
  {
    type: 'shower',
    name: 'Douche',
    icon: '🚿',
    description: 'Cabine de douche',
    color: '#E0F2FE',
    category: 'salle-de-bain',
    useSVG: true,
  },
  {
    type: 'bathtub',
    name: 'Baignoire',
    icon: '🛀',
    description: 'Baignoire',
    color: '#BAE6FD',
    category: 'salle-de-bain',
    useSVG: true,
  },
  {
    type: 'urinal',
    name: 'Urinoir',
    icon: '🚻',
    description: 'Urinoir',
    color: '#F1F5F9',
    category: 'salle-de-bain',
    useSVG: true,
  }
];

// ============ CHAMBRE ============
chambre = [
  {
    type: 'bed-single',
    name: 'Lit simple',
    icon: '🛏️',
    description: 'Lit une personne',
    color: '#FEF3C7',
    category: 'chambre',
    useSVG: true,
  },
  {
    type: 'bed-double',
    name: 'Lit double',
    icon: '🛌',
    description: 'Lit deux personnes',
    color: '#FDE68A',
    category: 'chambre',
    useSVG: true,
  },
  {
    type: 'nightstand',
    name: 'Table de chevet',
    icon: '🕯️',
    description: 'Table de nuit',
    color: '#D97706',
    category: 'chambre',
    useSVG: true,
  },
  {
    type: 'wardrobe',
    name: 'Armoire',
    icon: '👗',
    description: 'Armoire / Penderie',
    color: '#92400E',
    category: 'chambre',
    useSVG: true,
  },
  {
    type: 'dresser',
    name: 'Commode',
    icon: '📦',
    description: 'Commode / Tiroirs',
    color: '#A16207',
    category: 'chambre',
    useSVG: true,
  },
  {
    type: 'plant',
    name: 'Plante',
    icon: '🪴',
    description: 'Plante verte',
    color: '#16A34A',
    category: 'chambre',
    useSVG: true,
  }
];

// ============ STRUCTURE BÂTIMENT ============
structureBatiment = [
  {
    type: 'spiral-stair',
    name: 'Escalier colimaçon',
    icon: '🌀',
    description: 'Escalier en spirale',
    color: '#7C2D12',
    category: 'structure',
    useSVG: true,
  },
  {
    type: 'elevator',
    name: 'Ascenseur',
    icon: '🛗',
    description: 'Ascenseur',
    color: '#6B7280',
    category: 'structure',
    useSVG: true,
  },
  {
    type: 'structural-column',
    name: 'Colonne',
    icon: '🏛️',
    description: 'Colonne porteur',
    color: '#374151',
    category: 'structure',
    useSVG: true,
  },
  {
    type: 'beam',
    name: 'Poutre',
    icon: '🏗️',
    description: 'Poutre structurelle',
    color: '#4B5563',
    category: 'structure',
    useSVG: true,
  },
  {
    type: 'technical-shaft',
    name: 'Gaine technique',
    icon: '🔧',
    description: 'Gaine / Conduits',
    color: '#9CA3AF',
    category: 'structure',
    useSVG: true,
  },
  {
    type: 'ventilation-duct',
    name: 'Conduit ventilation',
    icon: '💨',
    description: 'Ventilation / VMC',
    color: '#D1D5DB',
    category: 'structure',
    useSVG: true,
  }
];

// ============ ARMOIRES ET BIBLIOTHÈQUES ============
armoiresBibliotheques = [
  {
    type: 'simple-cabinet',
    name: 'Armoire simple',
    icon: '🗄️',
    description: 'Placard / Armoire',
    color: '#92400E',
    category: 'mobilier',
    useSVG: true,
  },
  {
    type: 'corner-cabinet',
    name: 'Armoire angle',
    icon: '📐',
    description: 'Meuble d\'angle',
    color: '#A16207',
    category: 'mobilier',
    useSVG: true,
  },
  {
    type: 'bookshelf',
    name: 'Bibliothèque',
    icon: '📚',
    description: 'Étagères / Livres',
    color: '#78350F',
    category: 'mobilier',
    useSVG: true,
  },
  {
    type: 'tv-stand',
    name: 'Meuble TV',
    icon: '📺',
    description: 'Support télévision',
    color: '#1F2937',
    category: 'mobilier',
    useSVG: true,
  },
  {
    type: 'display-case',
    name: 'Vitrine',
    icon: '🏺',
    description: 'Vitrine / Exposition',
    color: '#DBEAFE',
    category: 'mobilier',
    useSVG: true,
  },
  {
    type: 'modular-storage',
    name: 'Rangement modulaire',
    icon: '🧩',
    description: 'Modules de rangement',
    color: '#D97706',
    category: 'mobilier',
    useSVG: true,
  }
];

// ============ TAPIS ET REVÊTEMENTS ============
tapisRevetements = [
  {
    type: 'round-carpet-rosette',
    name: 'Tapis rond',
    icon: '🟫',
    description: 'Tapis circulaire',
    color: '#A16207',
    category: 'revetement',
    useSVG: true,
  },
  {
    type: 'rectangular-carpet',
    name: 'Tapis rectangulaire',
    icon: '🟪',
    description: 'Tapis classique',
    color: '#7C2D12',
    category: 'revetement',
    useSVG: true,
  },
  {
    type: 'diamond-pattern-carpet',
    name: 'Tapis motifs',
    icon: '💎',
    description: 'Tapis à motifs',
    color: '#92400E',
    category: 'revetement',
    useSVG: true,
  },
  {
    type: 'floor-covering',
    name: 'Revêtement sol',
    icon: '🔲',
    description: 'Carrelage / Parquet',
    color: '#6B7280',
    category: 'revetement',
    useSVG: true,
  },
  {
    type: 'doormat',
    name: 'Paillasson',
    icon: '🪫',
    description: 'Tapis d\'entrée',
    color: '#8B4513',
    category: 'revetement',
    useSVG: true,
  }
];

// ============ JARDIN ============
jardin = [
  {
    type: 'swimming-pool',
    name: 'Piscine',
    icon: '🏊',
    description: 'Bassin / Piscine',
    color: '#0EA5E9',
    category: 'jardin',
    useSVG: true,
  },
  {
    type: 'fountain',
    name: 'Fontaine',
    icon: '⛲',
    description: 'Fontaine d\'eau',
    color: '#0284C7',
    category: 'jardin',
    useSVG: true,
  },
  {
    type: 'tree',
    name: 'Arbre',
    icon: '🌳',
    description: 'Arbre / Végétation',
    color: '#16A34A',
    category: 'jardin',
    useSVG: true,
  },
  {
    type: 'bush',
    name: 'Buisson',
    icon: '🌿',
    description: 'Arbuste / Buisson',
    color: '#15803D',
    category: 'jardin',
    useSVG: true,
  },
  {
    type: 'wooden-deck',
    name: 'Terrasse bois',
    icon: '🪵',
    description: 'Deck / Terrasse',
    color: '#A16207',
    category: 'jardin',
    useSVG: true,
  },
  {
    type: 'gazebo',
    name: 'Gazebo',
    icon: '🏛️',
    description: 'Pergola / Kiosque',
    color: '#92400E',
    category: 'jardin',
    useSVG: true,
  },
  {
    type: 'flower-bed',
    name: 'Parterre fleurs',
    icon: '🌸',
    description: 'Massif floral',
    color: '#EC4899',
    category: 'jardin',
    useSVG: true,
  },
  {
    type: 'garden-path',
    name: 'Allée jardin',
    icon: '🛤️',
    description: 'Chemin / Sentier',
    color: '#78716C',
    category: 'jardin',
    useSVG: true,
  },
  {
    type: 'garden-bench',
    name: 'Banc jardin',
    icon: '🪑',
    description: 'Banc extérieur',
    color: '#A16207',
    category: 'jardin',
    useSVG: true,
  },
  {
    type: 'garden-lighting',
    name: 'Éclairage jardin',
    icon: '💡',
    description: 'Lampadaire extérieur',
    color: '#FBBF24',
    category: 'jardin',
    useSVG: true,
  }
];

// ============ PORTES ET FENÊTRES ============
portesEtFenetres = [
  {
    type: 'simple-door',
    name: 'Porte simple',
    icon: '🚪',
    description: 'Porte standard',
    color: '#92400E',
    category: 'ouverture',
    useSVG: true,
  },
  {
    type: 'double-door',
    name: 'Porte double',
    icon: '🚪',
    description: 'Porte à deux battants',
    color: '#A16207',
    category: 'ouverture',
    useSVG: true,
  },
  {
    type: 'sliding-door',
    name: 'Porte coulissante',
    icon: '🔄',
    description: 'Porte à galandage',
    color: '#78350F',
    category: 'ouverture',
    useSVG: true,
  },
  {
    type: 'simple-window',
    name: 'Fenêtre simple',
    icon: '🪟',
    description: 'Fenêtre fixe',
    color: '#0891B2',
    category: 'ouverture',
    useSVG: true,
  },
  {
    type: 'hinged-window',
    name: 'Fenêtre battant',
    icon: '🪟',
    description: 'Fenêtre à battant',
    color: '#0284C7',
    category: 'ouverture',
    useSVG: true,
  },
  {
    type: 'round-window',
    name: 'Fenêtre ronde',
    icon: '🟡',
    description: 'Hublot / Œil-de-bœuf',
    color: '#0EA5E9',
    category: 'ouverture',
    useSVG: true,
  },
  {
    type: 'staircase',
    name: 'Escalier',
    icon: '🪜',
    description: 'Escalier droit',
    color: '#7C2D12',
    category: 'ouverture',
    useSVG: true,
  },
  {
    type: 'ramp',
    name: 'Rampe',
    icon: '♿',
    description: 'Rampe d\'accès',
    color: '#6B7280',
    category: 'ouverture',
    useSVG: true,
  }
];

// ============ MURS ET STRUCTURES ============
mursEtStructures = [
  {
    type: 'simple-wall',
    name: 'Mur simple',
    icon: '🧱',
    description: 'Mur / Cloison',
    color: '#6B7280',
    category: 'structure',
    useSVG: true,
  },
  {
    type: 'l-wall',
    name: 'Mur en L',
    icon: '📐',
    description: 'Mur d\'angle',
    color: '#4B5563',
    category: 'structure',
    useSVG: true,
  },
  {
    type: 't-wall',
    name: 'Mur en T',
    icon: '➕',
    description: 'Intersection de murs',
    color: '#374151',
    category: 'structure',
    useSVG: true,
  },
  {
    type: 'wall-openings',
    name: 'Mur avec ouvertures',
    icon: '🏠',
    description: 'Mur perforé',
    color: '#9CA3AF',
    category: 'structure',
    useSVG: true,
  },
  {
    type: 'column',
    name: 'Colonne',
    icon: '🏛️',
    description: 'Poteau / Pilier',
    color: '#374151',
    category: 'structure',
    useSVG: true,
  },
  {
    type: 'beam-horizontal',
    name: 'Poutre horizontale',
    icon: '➖',
    description: 'Poutre / Linteau',
    color: '#4B5563',
    category: 'structure',
    useSVG: true,
  }
];

// ============ ÉLÉMENTS SUPPLÉMENTAIRES ============
elementsSupplementaires = [
   {
    type: 'kitchen-cabinet-simple',
    name: 'Meuble bas',
    icon: '📦',
    description: 'Meuble de cuisine bas',
    color: '#92400E',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'kitchen-cabinet-corner-left',
    name: 'Meuble angle G',
    icon: '📐',
    description: 'Meuble d\'angle gauche',
    color: '#A16207',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'kitchen-cabinet-corner-right',
    name: 'Meuble angle D',
    icon: '📐',
    description: 'Meuble d\'angle droit',
    color: '#A16207',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'kitchen-counter-l-left',
    name: 'Plan travail L G',
    icon: '🔲',
    description: 'Plan de travail en L gauche',
    color: '#78350F',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'kitchen-counter-l-right',
    name: 'Plan travail L D',
    icon: '🔲',
    description: 'Plan de travail en L droit',
    color: '#78350F',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'kitchen-sink-round',
    name: 'Évier rond',
    icon: '⭕',
    description: 'Évier circulaire',
    color: '#94A3B8',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'kitchen-counter-straight',
    name: 'Plan travail droit',
    icon: '▬',
    description: 'Plan de travail linéaire',
    color: '#78350F',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'kitchen-island',
    name: 'Îlot central',
    icon: '🏝️',
    description: 'Îlot de cuisine',
    color: '#92400E',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'kitchen-peninsula',
    name: 'Péninsule',
    icon: '🔗',
    description: 'Plan de travail péninsule',
    color: '#A16207',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'dining-table-square',
    name: 'Table carrée',
    icon: '⬜',
    description: 'Table à manger carrée',
    color: '#8B4513',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'dining-table-round',
    name: 'Table ronde',
    icon: '⭕',
    description: 'Table à manger ronde',
    color: '#8B4513',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'dining-table-oval',
    name: 'Table ovale',
    icon: '⭕',
    description: 'Table à manger ovale',
    color: '#8B4513',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'dining-table-rectangular',
    name: 'Table rectangulaire',
    icon: '▬',
    description: 'Table à manger rectangulaire',
    color: '#8B4513',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'cooktop-2-burners',
    name: 'Plaque 2 feux',
    icon: '🔥',
    description: 'Table de cuisson 2 feux',
    color: '#1F2937',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'cooktop-4-burners',
    name: 'Plaque 4 feux',
    icon: '🔥',
    description: 'Table de cuisson 4 feux',
    color: '#1F2937',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'kitchen-hood',
    name: 'Hotte',
    icon: '💨',
    description: 'Hotte aspirante',
    color: '#6B7280',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'double-sink',
    name: 'Évier double',
    icon: '🚿',
    description: 'Évier à deux bacs',
    color: '#94A3B8',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'cooktop-6-burners',
    name: 'Plaque 6 feux',
    icon: '🔥',
    description: 'Table de cuisson 6 feux',
    color: '#1F2937',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'kitchen-appliance-panel',
    name: 'Panneau électro',
    icon: '⚡',
    description: 'Panneau électroménager',
    color: '#374151',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'kitchen-control-panel',
    name: 'Tableau commande',
    icon: '🎛️',
    description: 'Panneau de contrôle',
    color: '#4B5563',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'kitchen-equipment-rack',
    name: 'Rack équipement',
    icon: '🔧',
    description: 'Support d\'équipement',
    color: '#6B7280',
    category: 'cuisine',
    useSVG: true,
  },
  {
    type: 'arc',
    name: 'Arc',
    icon: '🌉',
    description: 'Arc / Voûte',
    color: '#6B7280',
    category: 'forme',
    useSVG: true,
  },
  {
    type: 'circle',
    name: 'Cercle',
    icon: '⭕',
    description: 'Forme circulaire',
    color: '#3B82F6',
    category: 'forme',
    useSVG: true,
  },
  {
    type: 'hatched-area',
    name: 'Zone hachurée',
    icon: '▦',
    description: 'Zone de remplissage',
    color: '#9CA3AF',
    category: 'forme',
    useSVG: true,
  },
  {
    type: 'reference-cross',
    name: 'Croix référence',
    icon: '✖️',
    description: 'Point de repère',
    color: '#EF4444',
    category: 'forme',
    useSVG: true,
  }
];
// ============ CUISINE ET SALLE À MANGER ============
cuisineEtSalleAManger = [
 
];
// Variables pour gérer l'ouverture/fermeture des sections
isStructureOpen = false;
isMobilierOpen = false;
isElectromenagerOpen = false;
isSalleDeBainOpen = false;
isChambreOpen = false;
isStructureBatimentOpen = false;
isArmoiresBibliothecuesOpen = false;
isTapisRevetementsOpen = false;
isJardinOpen = false;
isPortesEtFenetresOpen = false;
isMursEtStructuresOpen = false;
isElementsSupplementairesOpen = false;

// Importez vos tableaux d'éléments
electromenagerElements = this.electromenager;
salleDeBainElements = this.salleDeBain;
chambreElements = this.chambre;
structureBatimentElements = this.structureBatiment;
armoiresBibliothecuesElements = this.armoiresBibliotheques;
tapisRevetementsElements = this.tapisRevetements;
jardinElements = this.jardin;
portesEtFenetresElements = this.portesEtFenetres;
mursEtStructuresElements = this.mursEtStructures;
elementsSupplementairesElements = this.elementsSupplementaires;
toggleSection(section: string): void {
  switch(section) {
    case 'structure':
      this.isStructureOpen = !this.isStructureOpen;
      break;
    case 'mobilier':
      this.isMobilierOpen = !this.isMobilierOpen;
      break;
    case 'electromenager':
      this.isElectromenagerOpen = !this.isElectromenagerOpen;
      break;
    case 'salleDeBain':
      this.isSalleDeBainOpen = !this.isSalleDeBainOpen;
      break;
    case 'chambre':
      this.isChambreOpen = !this.isChambreOpen;
      break;
    case 'structureBatiment':
      this.isStructureBatimentOpen = !this.isStructureBatimentOpen;
      break;
    case 'armoiresBibliotheques':
      this.isArmoiresBibliothecuesOpen = !this.isArmoiresBibliothecuesOpen;
      break;
    case 'tapisRevetements':
      this.isTapisRevetementsOpen = !this.isTapisRevetementsOpen;
      break;
    case 'jardin':
      this.isJardinOpen = !this.isJardinOpen;
      break;
    case 'portesEtFenetres':
      this.isPortesEtFenetresOpen = !this.isPortesEtFenetresOpen;
      break;
    case 'mursEtStructures':
      this.isMursEtStructuresOpen = !this.isMursEtStructuresOpen;
      break;
    case 'elementsSupplementaires':
      this.isElementsSupplementairesOpen = !this.isElementsSupplementairesOpen;
      break;
  }
}
  ngAfterViewInit(): void {
    this.initializeCanvas();
  }

  initializeCanvas(): void {
    this.canvas = new fabric.Canvas('canvas', {
      backgroundColor: '#FAFAFA',
      width: 1000,
      height: 700,
      selection: true,
    });

    this.addGridPattern();
    this.canvasInitialized = true;

    // Événements du canvas
    this.canvas.on('selection:created', () => this.updateToolbar());
    this.canvas.on('selection:cleared', () => this.updateToolbar());
  }

  addGridPattern(): void {
    if (!this.isGridVisible) return;

    const gridSize = 20;
    const patternCanvas = document.createElement('canvas');
    patternCanvas.width = gridSize;
    patternCanvas.height = gridSize;
    const patternCtx = patternCanvas.getContext('2d')!;

    patternCtx.strokeStyle = '#E5E7EB';
    patternCtx.lineWidth = 1;
    patternCtx.beginPath();
    patternCtx.moveTo(0, gridSize);
    patternCtx.lineTo(gridSize, gridSize);
    patternCtx.lineTo(gridSize, 0);
    patternCtx.stroke();

    const pattern = new fabric.Pattern({
      source: patternCanvas,
      repeat: 'repeat',
    });

    this.canvas.backgroundColor = pattern;
    this.canvas.renderAll();
  }

  toggleGrid(): void {
    this.isGridVisible = !this.isGridVisible;
    if (this.isGridVisible) {
      this.addGridPattern();
    } else {
      this.canvas.backgroundColor = '#FAFAFA';
      this.canvas.renderAll();
    }
  }

  selectTool(elementType: string): void {
    this.selectedTool = elementType;
    this.canvas.defaultCursor = 'crosshair';

    // Désélectionner tous les objets
    this.canvas.discardActiveObject();
    this.canvas.renderAll();
  }









  getSelectedToolName(): string {
    const allElements = [...this.elements, ...this.mobilierElements];
    const selectedElement = allElements.find(
      (e) => e.type === this.selectedTool
    );
    return selectedElement ? selectedElement.name : '';
  }

 /**  addShape(type: string): void {
    if (!this.canvasInitialized) return;

    const left = Math.random() * (this.canvas.getWidth() - 150) + 50;
    const top = Math.random() * (this.canvas.getHeight() - 100) + 50;

    // Vérifier si c'est un élément de mobilier avec SVG
    const mobilierElement = this.mobilierElements.find(
      (el) => el.type === type
    );
    if (mobilierElement && mobilierElement.useSVG) {
      this.createSVGShape(type, left, top);
      return;
    }

    // Créer les formes basiques pour les éléments de structure
    this.createBasicShape(type, left, top);
  }*/

    addShape(type: string): void {
  if (!this.canvasInitialized) return;

  const left = Math.random() * (this.canvas.getWidth() - 150) + 50;
  const top = Math.random() * (this.canvas.getHeight() - 100) + 50;

  // Fonction helper pour chercher un élément dans toutes les catégories
  const findElementInCategories = (elementType: string) => {
    // Vérifier dans mobilier (existant)
    let element = this.mobilierElements.find(el => el.type === elementType);
    if (element) return { element, category: 'mobilier' };

    // Vérifier dans électroménager
    element = this.electromenagerElements.find(el => el.type === elementType);
    if (element) return { element, category: 'electromenager' };

    // Vérifier dans salle de bain
    element = this.salleDeBainElements.find(el => el.type === elementType);
    if (element) return { element, category: 'salleDeBain' };

    // Vérifier dans chambre
    element = this.chambreElements.find(el => el.type === elementType);
    if (element) return { element, category: 'chambre' };

    // Vérifier dans structure bâtiment
    element = this.structureBatimentElements.find(el => el.type === elementType);
    if (element) return { element, category: 'structureBatiment' };

    // Vérifier dans armoires et bibliothèques
    element = this.armoiresBibliothecuesElements.find(el => el.type === elementType);
    if (element) return { element, category: 'armoiresBibliotheques' };

    // Vérifier dans tapis et revêtements
    element = this.tapisRevetementsElements.find(el => el.type === elementType);
    if (element) return { element, category: 'tapisRevetements' };

    // Vérifier dans jardin
    element = this.jardinElements.find(el => el.type === elementType);
    if (element) return { element, category: 'jardin' };

    // Vérifier dans portes et fenêtres
    element = this.portesEtFenetresElements.find(el => el.type === elementType);
    if (element) return { element, category: 'portesEtFenetres' };

    // Vérifier dans murs et structures
    element = this.mursEtStructuresElements.find(el => el.type === elementType);
    if (element) return { element, category: 'mursEtStructures' };

    // Vérifier dans éléments supplémentaires
    element = this.elementsSupplementairesElements.find(el => el.type === elementType);
    if (element) return { element, category: 'elementsSupplementaires' };

    return null;
  };

  // Chercher l'élément dans toutes les catégories
  const result = findElementInCategories(type);

  if (result && result.element) {
    // Si l'élément a la propriété useSVG ou fait partie des nouvelles catégories SVG
    const isNewSVGCategory = [
      'electromenager', 'salleDeBain', 'chambre', 'structureBatiment',
      'armoiresBibliotheques', 'tapisRevetements', 'jardin',
      'portesEtFenetres', 'mursEtStructures', 'elementsSupplementaires'
    ].includes(result.category);

    if (result.element.useSVG || isNewSVGCategory) {
      this.createSVGShape(type, left, top);
      return;
    }
  }

  // Vérifier si c'est un élément de structure classique
  const structureElement = this.elements.find(el => el.type === type);
  if (structureElement) {
    this.createBasicShape(type, left, top);
    return;
  }

  // Si aucun élément trouvé, créer une forme basique par défaut
  console.warn(`Élément non trouvé: ${type}`);
  this.createBasicShape(type, left, top);
}

// Méthode alternative plus concise utilisant un tableau unifié
addShapeAlternative(type: string): void {
  if (!this.canvasInitialized) return;

  const left = Math.random() * (this.canvas.getWidth() - 150) + 50;
  const top = Math.random() * (this.canvas.getHeight() - 100) + 50;

  // Créer un tableau unifié de tous les éléments SVG
  const allSVGElements = [
    ...this.mobilierElements,
    ...this.electromenagerElements,
    ...this.salleDeBainElements,
    ...this.chambreElements,
    ...this.structureBatimentElements,
    ...this.armoiresBibliothecuesElements,
    ...this.tapisRevetementsElements,
    ...this.jardinElements,
    ...this.portesEtFenetresElements,
    ...this.mursEtStructuresElements,
    ...this.elementsSupplementairesElements
  ];

  // Chercher dans les éléments SVG
  const svgElement = allSVGElements.find(el => el.type === type);
  if (svgElement) {
    this.createSVGShape(type, left, top);
    return;
  }

  // Chercher dans les éléments de structure classiques
  const structureElement = this.elements.find(el => el.type === type);
  if (structureElement) {
    this.createBasicShape(type, left, top);
    return;
  }

  // Fallback
  console.warn(`Élément non trouvé: ${type}`);
  this.createBasicShape(type, left, top);
}

// Méthode optimisée avec Map pour de meilleures performances
private elementTypeMap: Map<string, { element: any, isSVG: boolean }> = new Map();

initializeElementMap(): void {
  // Initialiser la Map avec tous les éléments (à appeler dans ngOnInit)
  
  // Éléments SVG
  const svgCategories = [
    this.mobilierElements,
    this.electromenagerElements,
    this.salleDeBainElements,
    this.chambreElements,
    this.structureBatimentElements,
    this.armoiresBibliothecuesElements,
    this.tapisRevetementsElements,
    this.jardinElements,
    this.portesEtFenetresElements,
    this.mursEtStructuresElements,
    this.elementsSupplementairesElements
  ];

  svgCategories.forEach(category => {
    category.forEach(element => {
      this.elementTypeMap.set(element.type, { element, isSVG: true });
    });
  });

  // Éléments de structure classiques
  this.elements.forEach(element => {
    this.elementTypeMap.set(element.type, { element, isSVG: false });
  });
}

addShapeOptimized(type: string): void {
  if (!this.canvasInitialized) return;

  const left = Math.random() * (this.canvas.getWidth() - 150) + 50;
  const top = Math.random() * (this.canvas.getHeight() - 100) + 50;

  const elementData = this.elementTypeMap.get(type);
  
  if (elementData) {
    if (elementData.isSVG) {
      this.createSVGShape(type, left, top);
    } else {
      this.createBasicShape(type, left, top);
    }
  } else {
    console.warn(`Élément non trouvé: ${type}`);
    this.createBasicShape(type, left, top);
  }
}

   // Votre méthode createSVGShape mise à jour
  private createSVGShape(type: string, left: number, top: number): void {
    const svgString = this.getSVGDefinition(type);
    
    if (!svgString) {
      console.warn(`Pas de définition SVG pour le type: ${type}`);
      this.createBasicShape(type, left, top);
      return;
    }

    fabric
      .loadSVGFromString(svgString)
      .then(({ objects, options }) => {
        const validObjects = objects.filter(
          (obj): obj is fabric.Object => obj !== null
        );
        
        if (validObjects.length > 0) {
          let svgObject: fabric.Object;
          
          if (validObjects.length > 1) {
            svgObject = fabric.util.groupSVGElements(validObjects, options);
          } else {
            svgObject = validObjects[0];
          }
          
          svgObject.set({
            left,
            top,
            scaleX: 0.8,
            scaleY: 0.8,
            elementType: type,
            elementName: this.getElementName(type),
            selectable: true,
            evented: true
          });

          this.canvas.add(svgObject);
          this.canvas.setActiveObject(svgObject);
          this.canvas.renderAll();
          
          // Réinitialiser l'outil
          this.selectedTool = '';
          this.canvas.defaultCursor = 'default';
          
          console.log(`✅ Élément SVG créé: ${type}`);
        }
      })
      .catch((error) => {
        console.error('Erreur lors de la création du SVG:', error);
        this.createBasicShape(type, left, top);
      });
  }
 // Méthode utilitaire pour obtenir le nom d'un élément
  private getElementName(type: string): string {
    const allElements = [...this.elements, ...this.mobilierElements];
    const element = allElements.find(el => el.type === type);
    return element ? element.name : type;
  }
  private createBasicShape(type: string, left: number, top: number): void {
    let shape;
    const fillColor = this.selectedColor;
    const strokeColor = this.selectedStrokeColor;
    const strokeWidth = this.selectedStrokeWidth;

    switch (type) {
      case 'capteur':
        shape = new fabric.Circle({
          radius: 15,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top,
          shadow: new fabric.Shadow({
            color: 'rgba(0,0,0,0.2)',
            blur: 5,
            offsetX: 2,
            offsetY: 2,
          }),
        });
        break;

      case 'mur':
        shape = new fabric.Rect({
          width: 120,
          height: 15,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top,
        });
        break;

      case 'porte':
        shape = new fabric.Rect({
          width: 60,
          height: 8,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top,
          rx: 4,
          ry: 4,
        });
        break;

      case 'fenetre':
        shape = new fabric.Rect({
          width: 80,
          height: 6,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top,
        });
        break;

      case 'escalier':
        const stairs = [];
        for (let i = 0; i < 5; i++) {
          stairs.push(
            new fabric.Rect({
              width: 60 - i * 8,
              height: 8,
              fill: fillColor,
              stroke: strokeColor,
              strokeWidth: 1,
              left: left + i * 8,
              top: top + i * 8,
            })
          );
        }
        shape = new fabric.Group(stairs, { left, top });
        break;

      case 'zone':
        shape = new fabric.Rect({
          width: 150,
          height: 100,
          fill: this.hexToRgba(fillColor, 0.1),
          stroke: fillColor,
          strokeWidth: strokeWidth,
          strokeDashArray: [5, 5],
          left,
          top,
        });
        break;

      // Formes basiques pour le mobilier (fallback)
      case 'bureau':
        shape = new fabric.Rect({
          width: 120,
          height: 60,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top,
        });
        break;

      case 'table':
        shape = new fabric.Ellipse({
          rx: 40,
          ry: 30,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top,
        });
        break;

      case 'chaise':
        shape = new fabric.Rect({
          width: 25,
          height: 25,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top,
        });
        break;

      default:
        shape = new fabric.Rect({
          width: 50,
          height: 50,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          left,
          top,
        });
    }

    if (shape) {
      this.canvas.add(shape);
      this.canvas.setActiveObject(shape);
      this.canvas.renderAll();
      this.selectedTool = '';
      this.canvas.defaultCursor = 'default';
    }
  }

  // Remplacez votre méthode getSVGDefinition existante par celle-ci :
  private getSVGDefinition(type: string): string {
    const colors: SVGColors = {
      fillColor: this.selectedFillColor,
      strokeColor: this.selectedStrokeColor,
      strokeWidth: this.selectedStrokeWidth
    };
    
    return this.svgDefinitionsService.getSVGDefinition(type, colors);
  }

  // Méthode pour obtenir l'icône SVG (pour l'affichage dans la sidebar)
  getSVGIcon(type: string): string {
    return this.svgDefinitionsService.getSVGIcon(type);
  }

  deleteSelected(): void {
    const activeObjects = this.canvas.getActiveObjects();
    if (activeObjects.length) {
      activeObjects.forEach((obj) => this.canvas.remove(obj));
      this.canvas.discardActiveObject();
      this.canvas.renderAll();
    }
  }

  duplicateSelected(): void {
    const activeObject = this.canvas.getActiveObject();
    if (activeObject) {
      activeObject.clone().then((cloned: fabric.Object) => {
        cloned.set({
          left: (cloned.left || 0) + 20,
          top: (cloned.top || 0) + 20,
        });
        this.canvas.add(cloned);
        this.canvas.setActiveObject(cloned);
        this.canvas.renderAll();
      });
    }
  }

  clearCanvas(): void {
    if (confirm('Êtes-vous sûr de vouloir effacer tout le plan ?')) {
      this.canvas.clear();
      this.canvas.backgroundColor = '#FAFAFA';
      this.canvas.renderAll();
      if (this.isGridVisible) {
        this.addGridPattern();
      }
    }
  }

  bringAllToFront(): void {
    if (this.canvas && this.canvas.getObjects().length > 0) {
      this.canvas.getObjects().forEach((obj) => {
        this.canvas.bringObjectToFront(obj);
      });
      this.canvas.renderAll();
    }
  }

  sendAllToBack(): void {
    if (this.canvas && this.canvas.getObjects().length > 0) {
      this.canvas.getObjects().forEach((obj) => {
        this.canvas.sendObjectToBack(obj);
      });
      this.canvas.renderAll();
    }
  }

  updateToolbar(): void {
    // Mise à jour de l'interface en fonction de la sélection
  }

  setPlanType(type: 'local' | 'site'): void {
    this.planType = type;
  }

  // Méthodes pour la palette de couleurs
  selectColor(color: string): void {
    this.selectedColor = color;
    this.applyColorToSelected();
  }

  selectStrokeColor(color: string): void {
    this.selectedStrokeColor = color;
    this.applyColorToSelected();
  }

  setStrokeWidth(width: number): void {
    this.selectedStrokeWidth = width;
    this.applyColorToSelected();
  }

  onColorChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.selectColor(target.value);
  }

  onStrokeWidthChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.setStrokeWidth(parseInt(target.value, 10));
  }

  applyColorToSelected(): void {
    const activeObject = this.canvas.getActiveObject();
    if (activeObject) {
      if (activeObject.type === 'group') {
        // Pour les groupes, appliquer à tous les objets
        (activeObject as fabric.Group).getObjects().forEach((obj) => {
          obj.set({
            fill: this.selectedColor,
            stroke: this.selectedStrokeColor,
            strokeWidth: this.selectedStrokeWidth,
          });
        });
      } else {
        // Pour les objets simples
        activeObject.set({
          fill:
            activeObject.get('type') === 'zone'
              ? this.hexToRgba(this.selectedColor, 0.1)
              : this.selectedColor,
          stroke: this.selectedStrokeColor,
          strokeWidth: this.selectedStrokeWidth,
        });
      }
      this.canvas.renderAll();
    }
  }

  pickColorFromSelected(): void {
    const activeObject = this.canvas.getActiveObject();
    if (activeObject) {
      const fill = activeObject.get('fill') as string;
      const stroke = activeObject.get('stroke') as string;
      const strokeWidth = activeObject.get('strokeWidth') as number;

      if (fill && typeof fill === 'string' && fill.startsWith('#')) {
        this.selectedColor = fill;
      }
      if (stroke && typeof stroke === 'string' && stroke.startsWith('#')) {
        this.selectedStrokeColor = stroke;
      }
      if (strokeWidth) {
        this.selectedStrokeWidth = strokeWidth;
      }
    }
  }

  async importFile(event: Event): Promise<void> {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) return;

    const file = input.files[0];
    const fileName = file.name.toLowerCase();

    try {
      if (fileName.endsWith('.json')) {
        const text = await file.text();
        const jsonData = JSON.parse(text);
        this.canvas.loadFromJSON(jsonData, () => {
          this.canvas.renderAll();
          console.log('✅ Plan JSON importé');
        });
      } else if (fileName.endsWith('.svg')) {
        const text = await file.text();
        const { objects, options } = await fabric.loadSVGFromString(text);
        const validObjects = objects.filter(
          (obj): obj is fabric.Object => obj !== null
        );
        if (validObjects.length > 0) {
          const svgGroup = fabric.util.groupSVGElements(validObjects, options);
          svgGroup.set({ left: 100, top: 100 });
          this.canvas.add(svgGroup);
          this.canvas.renderAll();
          console.log('✅ SVG importé');
        } else {
          alert('❌ Aucun objet valide trouvé dans le SVG');
        }
      } else {
        alert('❌ Type de fichier non pris en charge');
      }
    } catch (error) {
      console.error("Erreur lors de l'importation:", error);
      alert("❌ Erreur lors de l'importation du fichier");
    }

    input.value = '';
  }

  exportToJson(): void {
    const json = this.canvas.toJSON();
    const exportData = {
      ...json,
      planType: this.planType,
      exportDate: new Date().toISOString(),
      version: '1.0',
    };

    const dataStr =
      'data:text/json;charset=utf-8,' +
      encodeURIComponent(JSON.stringify(exportData, null, 2));
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute('href', dataStr);
    downloadAnchorNode.setAttribute(
      'download',
      `plan-${this.planType}-${new Date().toISOString().split('T')[0]}.json`
    );
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
  }

  addPlanToLocal(): void {
    const json = JSON.stringify(this.canvas.toJSON());
    this.selectedLocal.Architecture2DImage = json;

    this.selectedLocal.Site = null;
    this.selectedLocal.Transactions = null;
    //this.selectedLocal.Site.ClientId="null";

    this.selectedLocal.TypeLocal = null;
    this.installService.updateLocal(this.selectedLocal).subscribe(
      (res) => {
        console.log('Mise à jour réussie', res);
      },
      (err) => {
        console.error('Erreur mise à jour', err);
      }
    );
  }
  exportToPNG(): void {
    const dataURL = this.canvas.toDataURL({
      format: 'png',
      quality: 1,
      multiplier: 2,
    });

    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute('href', dataURL);
    downloadAnchorNode.setAttribute(
      'download',
      `plan-${this.planType}-${new Date().toISOString().split('T')[0]}.png`
    );
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
  }

  // Fonctions utilitaires pour les couleurs
  hexToRgba(hex: string, alpha: number): string {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  }

  darkenColor(color: string): string {
    const hex = color.replace('#', '');
    const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - 50);
    const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - 50);
    const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - 50);
    return `#${r.toString(16).padStart(2, '0')}${g
      .toString(16)
      .padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }

  lightenColor(color: string): string {
    const hex = color.replace('#', '');
    const r = Math.min(255, parseInt(hex.substr(0, 2), 16) + 50);
    const g = Math.min(255, parseInt(hex.substr(2, 2), 16) + 50);
    const b = Math.min(255, parseInt(hex.substr(4, 2), 16) + 50);
    return `#${r.toString(16).padStart(2, '0')}${g
      .toString(16)
      .padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }

  //Données pour les listes déroulantes avec recherche
  clients: any[] = [];

  sites: any[] = [];

  locals: any[] = [];

  // Sélections actuelles et filtrage
  selectedClient: any = null;
  selectedSite: any = null;
  selectedLocal: any = null;

  // Recherche et filtrage
  clientSearchTerm = '';
  siteSearchTerm = '';
  localSearchTerm = '';

  // États des dropdowns
  isClientDropdownOpen = false;
  isSiteDropdownOpen = false;
  isLocalDropdownOpen = false;

  // Getters pour les listes filtrées
  get filteredClients() {
    if (!this.clientSearchTerm) return this.clients;
    return this.clients.filter(
      (client) =>
        client.name
          .toLowerCase()
          .includes(this.clientSearchTerm.toLowerCase()) ||
        client.code
          .toLowerCase()
          .includes(this.clientSearchTerm.toLowerCase()) ||
        client.type.toLowerCase().includes(this.clientSearchTerm.toLowerCase())
    );
  }

  get filteredSites() {
    let sites = this.selectedClient
      ? this.sites.filter((site) => site.clientId === this.selectedClient.id)
      : [];

    if (!this.siteSearchTerm) return sites;
    return sites.filter(
      (site) =>
        site.name.toLowerCase().includes(this.siteSearchTerm.toLowerCase()) ||
        site.address
          .toLowerCase()
          .includes(this.siteSearchTerm.toLowerCase()) ||
        site.type.toLowerCase().includes(this.siteSearchTerm.toLowerCase())
    );
  }

  get filteredLocals() {
    let locals = this.selectedSite
      ? this.locals.filter((local) => local.siteId === this.selectedSite.id)
      : [];

    if (!this.localSearchTerm) return locals;
    return locals.filter(
      (local) =>
        local.name.toLowerCase().includes(this.localSearchTerm.toLowerCase()) ||
        local.floor
          .toLowerCase()
          .includes(this.localSearchTerm.toLowerCase()) ||
        local.type.toLowerCase().includes(this.localSearchTerm.toLowerCase())
    );
  }

  // Méthodes pour la gestion des sélections
  selectClient(client: any): void {
    this.selectedClient = client;
    this.selectedSite = null;
    this.selectedLocal = null;
    this.isClientDropdownOpen = false;
    this.clientSearchTerm = '';
    this.siteSearchTerm = '';
    this.localSearchTerm = '';
    console.log('Client sélectionné:', client);
    this.installService.getSitesByClientId(client.Id).subscribe(
      (res: any) => {
        this.sites = res['Content']; // adapte selon la structure de la réponse
        console.log(this.sites);
      },
      (err) => {
        console.error(err);
      }
    );
  }

  selectSite(site: any): void {
    this.selectedSite = site;
    this.selectedLocal = null;
    this.isSiteDropdownOpen = false;
    this.siteSearchTerm = '';
    this.localSearchTerm = '';
    console.log('Site sélectionné:', site);
    this.installService.getLocalBySitetId(site.Id).subscribe(
      (res: any) => {
        this.locals = res;
        console.log('LOCAL', this.locals);
      },
      (err) => {
        console.error(err);
      }
    );
  }

  selectLocal(local: any): void {
    this.selectedLocal = local;
    this.isLocalDropdownOpen = false;
    this.localSearchTerm = '';
    console.log('Local sélectionné:', local);

    // Mettre à jour le titre du plan avec les informations sélectionnées
    this.updatePlanTitle();
  }

  // Méthodes pour gérer l'ouverture/fermeture des dropdowns
  toggleClientDropdown(): void {
    this.isClientDropdownOpen = !this.isClientDropdownOpen;
    this.isSiteDropdownOpen = false;
    this.isLocalDropdownOpen = false;
  }

  toggleSiteDropdown(): void {
    if (!this.selectedClient) return;
    this.isSiteDropdownOpen = !this.isSiteDropdownOpen;
    this.isClientDropdownOpen = false;
    this.isLocalDropdownOpen = false;
  }

  toggleLocalDropdown(): void {
    if (!this.selectedSite) return;
    this.isLocalDropdownOpen = !this.isLocalDropdownOpen;
    this.isClientDropdownOpen = false;
    this.isSiteDropdownOpen = false;
  }

  // Fermer tous les dropdowns
  closeAllDropdowns(): void {
    this.isClientDropdownOpen = false;
    this.isSiteDropdownOpen = false;
    this.isLocalDropdownOpen = false;
  }

  // Mettre à jour le titre du plan
  updatePlanTitle(): void {
    if (this.selectedClient && this.selectedSite && this.selectedLocal) {
      const planTitle = `${this.selectedClient.Name} - ${this.selectedSite.Name} - ${this.selectedLocal.Name}`;
      console.log('Titre du plan:', planTitle);

      // Optionnel: ajouter le titre comme texte sur le canvas
      const titleText = new fabric.Text(planTitle, {
        left: 20,
        top: 20,
        fontSize: 16,
        fontWeight: 'bold',
        fill: '#333333',
        backgroundColor: 'rgba(255,255,255,0.8)',
        padding: 10,
      });

      // Supprimer l'ancien titre s'il existe
      const existingTitle = this.canvas
        .getObjects()
        .find((obj) => obj.get('isTitle'));
      if (existingTitle) {
        this.canvas.remove(existingTitle);
      }

      titleText.set('isTitle', true);
      this.canvas.add(titleText);
      this.canvas.renderAll();
    }
  }

  // Réinitialiser les sélections
  resetSelections(): void {
    this.selectedClient = null;
    this.selectedSite = null;
    this.selectedLocal = null;
    this.clientSearchTerm = '';
    this.siteSearchTerm = '';
    this.localSearchTerm = '';
    this.closeAllDropdowns();
  }






  
}
