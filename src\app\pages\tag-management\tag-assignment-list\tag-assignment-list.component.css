.organisation-management-container {
  margin: 30px auto;
  padding: 25px;
  background: linear-gradient(145deg, #ffffff, #f9f9f9);
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

/* Header Section */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  animation: slideIn 0.5s ease-out;
}

.page-title {
  margin: 0;
}

.title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.title-icon {
  font-size: 30px;
  color: var(--primary);
  background: var(--primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.actions {
  display: flex;
  gap: 15px;
}

.create-button {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.create-button:hover {
  transform: translateY(-3px);
  background: linear-gradient(45deg,#81c784, var(--primary)) !important;
}

.action-icon {
  font-size: 18px;
}

/* Loading indicator */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  gap: 15px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Search Bar */
.search-bar-container {
  display: flex;
  margin-bottom: 30px;
}

.search-bar {
  display: flex !important;
  align-items: center !important;
  min-width: 400px !important;
  margin: 0 0px !important;
  border: 2px solid #e1e8ed !important;
  margin-right: 10px !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  background: #ffffff !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.2s ease !important;
}

.search-bar:focus-within {
  border-color: transparent;
  transform: translateY(-1px);
}

.search-button {
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  border-radius: 10px;
  color: white;
  padding: 7px 25px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.search-button:hover {
  background: linear-gradient(45deg,#81c784, var(--primary)) !important;
}

.search-input {
  flex: 1 !important;
  padding: 12px 16px !important;
  border: none !important;
  outline: none !important;
  font-size: 14px !important;
  color: #374151 !important;
  background: transparent !important;
  border-radius: 0 !important;
}

.search-input::placeholder {
  color: #9ca3af;
}

.clear-btn {
  background: transparent;
  border: none;
  border-radius: 6px;
  margin: 0 8px 0 2px;
  cursor: pointer;
  color: #6c757d;
  height: 28px;
  width: 35px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  transition: all 0.2s ease;
  line-height: 1;
}

.clear-btn:hover {
  color: #495057;
  transform: scale(1.05);
}

/* Table Section */
.table-section {
  margin-bottom: 30px;
}

/* Pagination */
.pagination-container {
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .actions {
    width: 100%;
    flex-direction: column;
    gap: 10px;
  }

  .create-button {
    width: 100%;
    padding: 12px;
    justify-content: center;
  }

  .search-bar {
    min-width: 100% !important;
  }

  .search-bar-container {
    flex-direction: column;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 24px;
  }

  .title-icon {
    font-size: 26px;
    color: var(--primary) !important;
  }
}

/* Dialog Styles */
::ng-deep .tag-assignment-dialog .mat-mdc-dialog-container {
  padding: 0 !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  min-width: 550px !important;
  max-width: 550px !important;
}

::ng-deep .tag-assignment-dialog .mat-mdc-dialog-surface {
  border-radius: 12px !important;
}

