/* General Styles */
.table-section {
  margin: 20px;
  padding: 20px;
  background-color: #f5f7f5;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.8s ease-out;
}

.table-section h3 {
  font-size: 20px;
  font-weight: 600;
  color: #2e7d32;
  margin-bottom: 20px;
}

.filters-container {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.filter-input {
  width: 100%;
  max-width: 300px;
}

/* Table Styles */
.sensor-table {
  width: 100%;
  overflow-x: auto;
  border-radius: 8px;
  transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
}

.sensor-table:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.mat-header-cell {
  background-color: #e0f2f1;
  color: #2e7d32;
  font-weight: bold;
  text-transform: uppercase;
  font-size: 14px;
  padding: 12px;
}

.mat-cell {
  font-size: 14px;
  padding: 12px;
  border-bottom: 1px solid #e0f2f1;
  transition: background-color 0.3s ease-in-out;
}

.mat-cell:hover {
  background-color: #e8f5e9;
}

/* Pagination Styles */
mat-paginator {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}