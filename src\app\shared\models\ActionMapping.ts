import { Capteur } from '@app/core/models/capteur';
import { DeviceAction } from '@app/shared/models/DeviceAction';
import { TypeCapteur } from '@app/shared/models/typeCapteur';

export interface ActionMapping {
  originalTopic: string;
  originalDeviceType?: string;
  originalDeviceTypeName?: string;
  originalTopicForFiltering: string;
  selectedCapteurId: string;
  selectedCapteur?: Capteur;
  selectedTypeCapteur?: TypeCapteur;
  availableActions?: DeviceAction[];
}
