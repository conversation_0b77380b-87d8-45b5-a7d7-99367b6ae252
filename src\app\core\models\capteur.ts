import { Controller } from "./controller";
import { AuditModel } from "./models-audit/audit-model";
import { SensorReading } from './sensorReading';
import { Transaction } from './transaction';
import { TypeCapteur } from '../../shared/models/typeCapteur';


export class Capteur extends AuditModel{
  Protocol!: string;
  Manufacturer!: string;
  Model?: string;
  ModelIdentifier!: string;
  FriendlyName!: string;
  LastSeen!: Date;

  IeeeAddress!: string;
  NetworkAddress!: number;
  Endpoint!: number;

  NodeId!: number;
  HomeId!: number;
  SecurityClasses!: string;
  SensorReadings!: SensorReading[];

  Transactions!: Transaction[];
  IdTypeCapteur!: string;
  TypeCapteur?: TypeCapteur = new TypeCapteur();
  ControllerId? :string;
  Controller? :Controller ;

  RowData?: string;
  Topic?: string;
  State?: string;
  Brand?: string;

}

