import {
  Component,
  OnInit,
  OnDestroy,
} from '@angular/core';
import { TableConfig } from '@app/core/models/table-config.module';
import {
  Organisation,
  OrganisationService,
  OrganisationType,
} from '@app/core/services/organisation.service';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { Router } from '@angular/router';
import { GenericTableComponent } from '../../../../components/generic-table/generic-table.component';
import { PageEvent } from '@angular/material/paginator'; // Import PageEvent

@Component({
  selector: 'app-clients-list',
  standalone: true,
  imports: [CommonModule, GenericTableComponent],
  templateUrl: './clients-list.component.html',
  styleUrls: ['./clients-list.component.css'],
})
export class ClientsListComponent implements OnInit, OnDestroy {
  organisations: Organisation[] = [];
  filteredOrganisations: Organisation[] = [];
  headers: string[] = [
    'Nom',
    'Type',
    'Email',
    'Employés',
  ];
  keys: string[] = [
    'nom',
    'type',
    'emailAddress',
    'nombreEmployees',
  ];
  organisationTypes: OrganisationType[] = [];
  selectedType: OrganisationType | null = null;
  isLoading = false;
  viewMode = 'table';
  currentPage: number = 0; // MatPaginator uses 0-based index
  pageSize: number = 10; // Default page size
  totalCount: number = 0; // Total count of filtered items

  private routeSubscription: Subscription | undefined;

  tableConfig: TableConfig = {
    columns: [
      {
        key: 'nom',
        label: 'Nom',
        sortable: true,
        filterType: 'text',
        filterPlaceholder: 'Rechercher par nom',
      },
      {
        key: 'type',
        label: 'Type',
        sortable: true,
        filterType: 'select',
        filterOptions: [
          { value: '', label: 'Tous les types' },
          { value: 'Ecole', label: 'École' },
          { value: 'Hopital', label: 'Hôpital' },
          { value: 'Entrepot', label: 'Entrepôt' },
          { value: 'Bureau', label: 'Bureau' },
          { value: 'Usine', label: 'Usine' },
          { value: 'Magasin', label: 'Magasin' },
          { value: 'Residence', label: 'Résidence' },
          { value: 'CentreCommercial', label: 'Centre Commercial' },
          { value: 'Restaurant', label: 'Restaurant' },
          { value: 'Hotel', label: 'Hôtel' },
          { value: 'Maison', label: 'Maison' },
        ],
      },
      {
        key: 'emailAddress',
        label: 'Email',
        filterType: 'text',
        filterPlaceholder: 'Rechercher par email',
      },
      {
        key: 'nombreEmployees',
        label: 'Employés',
        type: 'number',
        sortable: true,
        filterType: 'number',
        filterPlaceholder: 'Nombre min',
      },
    ],
    actions: {
      view: true,
      edit: true,
      delete: true,
    },
    pageSize: 10,
    pageSizeOptions: [5, 10, 25, 50],
  };

  constructor(
    readonly organisationService: OrganisationService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.organisationTypes = this.organisationService.getOrganisationTypes();
    this.loadOrganisations();

    this.routeSubscription = this.route.params.subscribe((params) => {
      const type = params['type'];
      if (type) {
        this.selectedType = type as OrganisationType;
      } else {
        this.selectedType = null;
      }
      this.applyFilter();
    });
  }

  ngOnDestroy(): void {
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
  }

  loadOrganisations(): void {
    this.isLoading = true;
    this.organisationService.getOrganisations().subscribe((orgs) => {
      this.organisations = orgs;
      this.isLoading = false;
      this.applyFilter();
    });
  }

  filterByType(type: OrganisationType): void {
    this.selectedType = type;
    this.applyFilter();
  }

  applyFilter(): void {
    if (this.selectedType) {
      this.filteredOrganisations = this.organisations.filter(
        (org) => org.type === this.selectedType
      );
    } else {
      this.filteredOrganisations = this.organisations;
    }
    this.totalCount = this.filteredOrganisations.length; // Update total count after filtering
    this.currentPage = 0; // Reset to first page on filter change
  }

  // Handle page changes from generic-table
  onGenericTablePageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    // For client-side pagination, no need to re-filter or re-load data here.
    // The GenericTableComponent's pagedData getter handles the slicing.
  }

  handleAction(event: { action: string; row: any }): void {
    const { action, row } = event;
    if (action === 'edit') {
      this.editOrganisation(row.id);
    } else if (action === 'delete') {
      this.deleteOrganisation(row.id);
    } else if (action === 'view') {
      this.viewOrganisationDetails(row.id);
    }
  }

  editOrganisation(id: number): void {
    this.router.navigate(['/organisations/edit', id]);
  }

  viewOrganisationDetails(id: number): void {
    this.router.navigate(['/organisation-details', id]);
  }

  goBack(): void {
    this.router.navigate(['/organisations']);
  }

  deleteOrganisation(id: number): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette organisation ?')) {
      this.organisationService.deleteOrganisation(id).subscribe({
        next: () => {
          this.organisations = this.organisations.filter(
            (org) => org.id !== id
          );
          // Re-apply filter to update filteredOrganisations and totalCount after deletion
          this.applyFilter();
        },
        error: (error) => {
          console.error('Erreur lors de la suppression:', error);
        },
      });
    }
  }

  onTableAction(event: { action: string; item: any }) {
    switch (event.action) {
      case 'view':
        this.viewOrganisationDetails(event.item.id);
        break;
      case 'edit':
        this.editOrganisation(event.item.id);
        break;
      case 'delete':
        this.deleteOrganisation(event.item.id);
        break;
    }
  }

  onPageChange(event: { pageIndex: number; pageSize: number } | number): void {
    if (typeof event === 'number') {
      this.currentPage = event;
    } else {
      this.currentPage = event.pageIndex + 1;
      this.pageSize = event.pageSize;
    }
    this.loadOrganisations();
  }

  onSortChange(sort: any) {
    // handle sort change here
  }

  onFilterChange(filter: any) {
    // handle table-level filtering
  }
}