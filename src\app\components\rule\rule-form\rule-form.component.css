/* IoT Rules Generator - Clean Modern Form Styling */

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  background: var(--background);
  color: var(--text-primary);
  min-height: 100vh;
  background-color: #222;
}

.groupe-btn {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: white;
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: background-color 0.2s ease;
}

.groupe-btn:hover {
  background: linear-gradient(45deg, #81c784, var(--primary)) !important;
}

/* Utility Classes */
.max-w-4xl {
  max-width: 56rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.p-6 {
  padding: 1.5rem;
}

.min-h-screen {
  min-height: 100vh;
}

/* Main Container */
.bg-white {
  background: var(--surface);
  border: 1px solid var(--card-border);
  box-shadow: var(--shadow-lg);
}

.rounded-lg {
  border-radius: 0.75rem;
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

/* Typography */
h1 {
  color: var(--primary);
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

h2,
h3 {
  color: var(--text-primary);
  font-weight: 600;
}

h2 {
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

h3 {
  font-size: 1.125rem;
  margin-bottom: 0.75rem;
}

/* Grid Layouts */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-1 {
  gap: 0.25rem;
}

/* Spacing */
.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.p-1 {
  padding: 0.25rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

/* Responsive Grid */
@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .md\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
}

/* Text Utilities */
.block {
  display: block;
}

.text-sm {
  font-size: 0.875rem;
}

.text-xs {
  font-size: 0.75rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.text-gray-700,
.text-gray-800 {
  color: var(--text-primary);
}

.text-gray-600 {
  color: var(--primary);
}

.text-gray-500 {
  color: var(--grey-light);
}

.text-blue-600 {
  color: var(--primary);
}

/* Sizing */
.w-full {
  width: 100%;
}

.w-3 {
  width: 0.75rem;
}

.w-4 {
  width: 1rem;
}

.w-5 {
  width: 1.25rem;
}

.w-6 {
  width: 1.5rem;
}

.h-3 {
  height: 0.75rem;
}

.h-4 {
  height: 1rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-12 {
  height: 3rem;
}

/* ===== CLEAN FORM ELEMENTS ===== */

/* Base Form Field Styling */
.form-field {
  position: relative;
  margin-bottom: 0.75rem;
}

.form-field label {
  display: block;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.375rem;
  line-height: 1.25;
}

/* Simple, Clean Input Styling */
input[type="text"],
input[type="time"],
input[type="number"] {
  width: 100%;
  height: 42px;
  padding: 0.75rem;
  background: var(--surface);
  border: 1px solid var(--card-border);
  border-radius: 6px;
  color: var(--text-primary);
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.2s ease;
  box-sizing: border-box;
  outline: none;
}

/* Clean Focus States */
input[type="text"]:focus,
input[type="time"]:focus,
input[type="number"]:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px var(--highlight-color);
}

/* Subtle Hover States */
input[type="text"]:hover:not(:focus),
input[type="time"]:hover:not(:focus),
input[type="number"]:hover:not(:focus) {
  border-color: var(--primary-light);
}

/* Placeholder Styling */
input[type="text"]::placeholder,
input[type="number"]::placeholder {
  color: var(--grey-light);
  opacity: 0.7;
}

/* ===== IMPROVED DROPDOWN STYLING ===== */

/* Enhanced Select Dropdown with better text sizing and appearance */
select {
  appearance: none;
  width: 100%;
  height: 42px;
  padding: 0.75rem 2.5rem 0.75rem 0.75rem;
  background: var(--surface);
  border: 1px solid var(--card-border);
  border-radius: 6px;
  color: var(--text-primary);
  font-size: 0.8125rem; /* Reduced from 0.875rem */
  line-height: 1.2;
  font-weight: 500;
  transition: all 0.2s ease;
  box-sizing: border-box;
  outline: none;
  cursor: pointer;

  /* Custom dropdown arrow */
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23047857' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.125em 1.125em;
}

/* Improved select option styling */
select option {
  background: var(--surface);
  color: var(--text-primary);
  padding: 0.625rem 0.75rem;
  font-size: 0.8125rem;
  line-height: 1.3;
  font-weight: 500;
  border: none;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;

  /* Better visual separation */
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* Selected option styling */
select option:checked {
  background: var(--primary);
  color: var(--white);
  font-weight: 600;
}

/* Hover state for options */
select option:hover {
  background: var(--highlight-color);
  color: var(--text-primary);
}

/* Focus state for select */
select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px var(--highlight-color);
}

/* Hover state for select */
select:hover:not(:focus) {
  border-color: var(--primary-light);
  background: rgba(255, 255, 255, 0.7);
}

/* Small selects for groups - enhanced */
select.p-1 {
  height: 32px;
  padding: 0.25rem 1.5rem 0.25rem 0.5rem;
  font-size: 0.75rem;
  line-height: 1.2;
  min-width: 4rem;
  background-size: 1em 1em;
  background-position: right 0.5rem center;
}

select.p-1 option {
  padding: 0.375rem 0.5rem;
  font-size: 0.75rem;
}

/* Specific styling for value selection dropdowns */
select[id*="select-"] {
  font-size: 0.8125rem;
  line-height: 1.25;
  padding: 0.625rem 2.5rem 0.625rem 0.75rem;
  height: 40px;
}

select[id*="select-"] option {
  font-size: 0.8125rem;
  padding: 0.5rem 0.75rem;
  line-height: 1.3;
}

/* Input with datalist */
input[type="text"][list],
input[type="number"][list] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23047857' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.25em 1.25em;
  padding-right: 2.5rem;
  cursor: pointer;
}

/* Disabled States */
input:disabled,
select:disabled {
  background: var(--beige-light);
  color: var(--grey-light);
  border-color: var(--card-border);
  cursor: not-allowed;
  opacity: 0.6;
}

select:disabled option {
  background: var(--beige-light);
  color: var(--grey-light);
}

/* Validation States */
input[type="text"].border-red-300,
input[type="number"].border-red-300,
select.border-red-300 {
  border-color: var(--danger);
}

input[type="text"].border-red-300:focus,
input[type="number"].border-red-300:focus,
select.border-red-300:focus {
  border-color: var(--danger);
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

/* ===== DIALOG STYLING ===== */

.mat-dialog-container {
  width: 50vw !important;
  max-width: 800px !important;
  min-width: 600px !important;
}

.dialog-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 85vh;
  width: 100%;
  max-width: none;
  min-width: auto;
  position: relative;
  overflow: hidden;
}

.dialog-header {
  flex-shrink: 0;
  padding: 16px 16px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  background: var(--surface);
  z-index: 10;
  border-bottom: 1px solid var(--card-border);
}

.dialog-header h1 {
  margin-bottom: 0;
}

.dialog-content {
  flex-grow: 1;
  overflow-y: auto;
  padding: 16px;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.dialog-content::-webkit-scrollbar {
  display: none;
}

.dialog-footer {
  flex-shrink: 0;
  padding: 16px;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  position: sticky;
  bottom: 0;
  background: var(--surface);
  border-top: 1px solid var(--card-border);
}

.dialog-footer button {
  min-width: 100px;
  color: white;
  justify-content: center;
}

.mat-dialog-content {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.mat-dialog-content::-webkit-scrollbar {
  display: none !important;
}

/* Close Button */
.close-button {
  background: transparent !important;
  border: none !important;
  padding: 8px !important;
  border-radius: 50% !important;
  width: 40px !important;
  height: 40px !important;
  min-width: 40px !important;
  color: var(--grey-light) !important;
  box-shadow: none !important;
  transform: none !important;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: var(--card-border) !important;
  color: var(--danger) !important;
}

.close-button[disabled] {
  opacity: 0.4;
  cursor: not-allowed;
}

/* ===== CLEAN BUTTON STYLING ===== */

button {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: none;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: var(--primary);
  color: var(--white);
}

button:hover:not(:disabled) {
  background: var(--primary-dark);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

button:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Specific Button Colors */
.save-button {
  background: linear-gradient(45deg, var(--primary), #81c784) !important;
  color: var(--white) !important;
}

.save-button:hover:not(:disabled) {
  background: linear-gradient(45deg, #81c784, var(--primary)) !important;
}

.cancel-button {
  background: var(--beige-light) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--card-border) !important;
}

.cancel-button:hover {
  background: var(--card-border) !important;
}

.download-button {
  background: linear-gradient(45deg, var(--audit), #01579e) !important;
  color: var(--white) !important;
}

.download-button:hover {
  background: linear-gradient(45deg, #01579e, var(--audit)) !important;
}

.bg-green-500 {
  background: var(--primary) !important;
  color: var(--white) !important;
}

.bg-green-500:hover {
  background: var(--primary-dark) !important;
}

.bg-blue-600 {
  background: var(--primary) !important;
}

.bg-blue-600:hover {
  background: var(--primary-dark) !important;
}

.text-red-600 {
  color: var(--white) !important;
  background: var(--danger) !important;
}

.text-red-600:hover {
  background: #dc2626 !important;
}

/* ===== CLEAN CHECKBOX STYLING ===== */

input[type="checkbox"] {
  width: 1.25rem;
  height: 1.25rem;
  background: var(--surface);
  border: 2px solid var(--card-border);
  border-radius: 0.25rem;
  appearance: none;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

input[type="checkbox"]:checked {
  background: var(--primary);
  border-color: var(--primary);
}

input[type="checkbox"]:checked::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--white);
  font-weight: bold;
  font-size: 0.875rem;
}

/* ===== CARDS AND CONTAINERS ===== */

.bg-gray-50 {
  background: var(--beige-light);
  border: 1px solid var(--card-border);
  border-radius: 0.5rem;
  padding: 1rem;
}

/* Condition Groups */
.bg-gray-50.border-2 {
  border: 2px solid var(--primary);
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px var(--box-shadow);
  margin-bottom: 1rem;
}

/* Individual Conditions */
.bg-white.border-gray-200 {
  background: var(--surface);
  border: 1px solid var(--card-border);
  border-radius: 0.5rem;
  padding: 0.75rem;
  box-shadow: var(--shadow-sm);
}

.bg-white.border-gray-200:hover {
  border-color: var(--primary-light);
}

.border-gray-200 {
  border-color: var(--card-border);
}

.border-2 {
  border-width: 2px;
}

.rounded-md {
  border-radius: 0.5rem;
}

/* Topics Display */
.bg-gray-50.min-h-\[40px\] {
  background: var(--beige-light);
  border: 2px dashed var(--card-border);
  min-height: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.min-h-\[40px\] {
  min-height: 40px;
}

/* Flexbox Utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.items-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-1 > * + * {
  margin-top: 0.25rem;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

/* SVG Icons */
svg {
  transition: all 0.2s ease;
}

.text-yellow-600 {
  color: var(--primary);
}

/* JSON Output */
pre {
  background: var(--beige-light);
  color: var(--text-primary);
  padding: 1.5rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  overflow-x: auto;
  border: 1px solid var(--card-border);
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  line-height: 1.6;
  position: relative;
}

pre::before {
  content: "JSON";
  position: absolute;
  top: 0.5rem;
  right: 0.75rem;
  background: var(--primary);
  color: var(--white);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: bold;
}

.overflow-x-auto {
  overflow-x: auto;
}

/* Clean Scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--beige-light);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* Custom scrollbar for dropdown options (webkit browsers) */
select::-webkit-scrollbar {
  width: 8px;
}

select::-webkit-scrollbar-track {
  background: var(--beige-light);
}

select::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

select::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* Group Labels */
.text-sm.font-semibold {
  color: var(--primary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* Loading States */
.absolute {
  position: absolute;
}

.inset-0 {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.bg-white.bg-opacity-75 {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(2px);
}

.z-50 {
  z-index: 50;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.pointer-events-none {
  pointer-events: none;
}

.opacity-50 {
  opacity: 0.5;
}

.text-center {
  text-align: center;
}

/* Clean Drag Handle */
.drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-bottom: 0.5rem;
  margin-left: auto;
  margin-right: auto;
  cursor: grab;
  color: var(--grey-light);
  border-radius: 0.25rem;
  transition: all 0.2s ease;
  background: transparent;
  border: 1px dashed var(--card-border);
}

.drag-handle:hover {
  background: var(--beige-light);
  color: var(--primary);
  border-color: var(--primary);
}

.drag-handle:active,
.cdk-drag-preview .drag-handle {
  cursor: grabbing;
  background: var(--primary);
  color: var(--white);
  border-color: var(--primary);
}

/* Progress Bar */
.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--beige-light);
  border-radius: 2px;
  overflow: hidden;
  margin-top: 0.5rem;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--success));
  border-radius: 2px;
  animation: progress 2s ease-in-out infinite;
}

/* Validation Feedback */
.validation-feedback {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  margin-top: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.validation-feedback.error {
  color: var(--danger);
}

.validation-feedback.success {
  color: var(--success);
}

.validation-feedback.info {
  color: var(--audit);
}

.validation-feedback svg {
  width: 0.875rem;
  height: 0.875rem;
  flex-shrink: 0;
}

/* Simple Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes progress {
  0% {
    width: 0%;
  }
  50% {
    width: 70%;
  }
  100% {
    width: 100%;
  }
}

/* Utility Classes */
.selected {
  background: var(--highlight-color) !important;
  border-color: var(--primary) !important;
}

/* Dark mode support for dropdowns */
@media (prefers-color-scheme: dark) {
  select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  }
}

/* Enhanced visual feedback for select elements */
select:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Better alignment */
select {
  vertical-align: middle;
  display: inline-block;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .mat-dialog-container {
    width: 60vw !important;
  }
}

@media (max-width: 900px) {
  .mat-dialog-container {
    width: 80vw !important;
    min-width: 400px !important;
  }
}

@media (max-width: 768px) {
  .p-6 {
    padding: 1rem;
  }

  h1 {
    font-size: 1.5rem;
  }

  .grid {
    gap: 0.75rem;
  }

  button {
    padding: 0.75rem;
    font-size: 0.75rem;
  }

  .md\:grid-cols-5,
  .md\:grid-cols-4,
  .md\:grid-cols-3 {
    grid-template-columns: 1fr;
  }

  .bg-gray-50.border-2 {
    padding: 0.75rem;
  }

  .bg-white.border-gray-200 {
    padding: 0.5rem;
  }

  .progress-bar {
    height: 3px;
  }

  /* Responsive dropdown adjustments */
  select {
    font-size: 0.75rem;
    height: 38px;
    padding: 0.625rem 2.25rem 0.625rem 0.625rem;
  }

  select option {
    font-size: 0.75rem;
    padding: 0.5rem 0.625rem;
  }

  select.p-1 {
    height: 30px;
    font-size: 0.6875rem;
    padding: 0.1875rem 1.25rem 0.1875rem 0.375rem;
  }

  select.p-1 option {
    font-size: 0.6875rem;
    padding: 0.25rem 0.375rem;
  }
}

@media (max-width: 600px) {
  .mat-dialog-container {
    width: 95vw !important;
    min-width: 320px !important;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
