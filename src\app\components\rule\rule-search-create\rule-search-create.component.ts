import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-rule-search-create',
  templateUrl: `./rule-search-create.component.html`,
  styleUrls: [`./rule-search-create.component.css`],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule
  ]
})
export class RuleSearchCreateComponent {
  @Input() searchTerm: string = '';
  @Input() isSearching: boolean = false;
  @Input() hasPendingOperations: boolean = false;

  @Output() searchTermChange = new EventEmitter<string>();
  @Output() submitSearch = new EventEmitter<void>();
  @Output() clearSearch = new EventEmitter<void>();
  @Output() openAiRuleGeneratorDialog = new EventEmitter<void>();
  @Output() openRuleFormDialog = new EventEmitter<void>();

  // Internal search term for the input
  private _internalSearchTerm: string = '';

  // Getter and setter for proper two-way binding
  get internalSearchTerm(): string {
    return this._internalSearchTerm;
  }

  set internalSearchTerm(value: string) {
    this._internalSearchTerm = value;
    this.searchTermChange.emit(value);
  }

  onSearchSubmit(): void {
    console.log('🔍 Search component: onSearchSubmit called with searchTerm:', this.internalSearchTerm);
    this.submitSearch.emit();
  }

  onClearSearch(): void {
    console.log('🧹 Search component: onClearSearch called');
    this.internalSearchTerm = '';
    this.clearSearch.emit();
  }

  onAiRuleGenerator(): void {
    this.openAiRuleGeneratorDialog.emit();
  }

  onCreateRule(): void {
    this.openRuleFormDialog.emit();
  }
}